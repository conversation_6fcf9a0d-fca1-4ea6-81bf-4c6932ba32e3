import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, Loader2, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface AccountSettingsProps {
    onDeleteAccount?: () => void;
}

interface FormErrors {
    currentPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
}

const MAX_PROFILE_PICTURE_SIZE = 5 * 1024 * 1024; // 5MB

const AccountSettings: React.FC<AccountSettingsProps> = ({
    onDeleteAccount
}) => {
    const { user, changePassword, updateProfile, updateProfilePicture, deleteAccount } = useAuth();
    const navigate = useNavigate();
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [loading, setLoading] = useState(false);
    const [savingProfile, setSavingProfile] = useState(false);
    const [uploadingPicture, setUploadingPicture] = useState(false);
    const [deletingAccount, setDeletingAccount] = useState(false);
    const [formErrors, setFormErrors] = useState<FormErrors>({});
    const [formData, setFormData] = useState({
        name: '',
        email: user?.email || '',
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });

    // Set initial name value from user object
    useEffect(() => {
        if (user) {
            const fullName = user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;
            setFormData(prev => ({
                ...prev,
                name: fullName || '',
                email: user.email || ''
            }));
        }
    }, [user]);

    const processImage = async (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = document.createElement('img');
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    let width = img.width;
                    let height = img.height;

                    // Calculate new dimensions while maintaining aspect ratio
                    if (width > 400) {
                        height = (height * 400) / width;
                        width = 400;
                    }
                    if (height > 400) {
                        width = (width * 400) / height;
                        height = 400;
                    }

                    canvas.width = width;
                    canvas.height = height;
                    const ctx = canvas.getContext('2d');
                    ctx?.drawImage(img, 0, 0, width, height);
                    resolve(canvas.toDataURL('image/jpeg', 0.85));
                };
                img.onerror = reject;
                img.src = e.target?.result as string;
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    };

    const handleProfilePictureUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        // Validate file size
        if (file.size > MAX_PROFILE_PICTURE_SIZE) {
            toast({
                title: "Error",
                description: "Profile picture must be less than 5MB",
                variant: "destructive"
            });
            return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            toast({
                title: "Error",
                description: "Please upload an image file",
                variant: "destructive"
            });
            return;
        }

        setUploadingPicture(true);
        try {
            const processedImage = await processImage(file);
            await updateProfilePicture(processedImage);
            toast({
                title: "Success",
                description: "Profile picture updated successfully"
            });
        } catch (error: any) {
            console.error('Error uploading profile picture:', error);
            toast({
                title: "Error",
                description: error.message || "Failed to update profile picture",
                variant: "destructive"
            });
        } finally {
            setUploadingPicture(false);
            // Clear the input
            e.target.value = '';
        }
    };

    const handleRemoveProfilePicture = async () => {
        setUploadingPicture(true);
        try {
            await updateProfilePicture('');
            toast({
                title: "Success",
                description: "Profile picture removed successfully"
            });
        } catch (error: any) {
            console.error('Error removing profile picture:', error);
            toast({
                title: "Error",
                description: error.message || "Failed to remove profile picture",
                variant: "destructive"
            });
        } finally {
            setUploadingPicture(false);
        }
    };

    const validatePasswordForm = (): boolean => {
        const errors: FormErrors = {};
        let isValid = true;

        // Current Password validation
        if (!formData.currentPassword.trim()) {
            errors.currentPassword = 'Current password is required';
            isValid = false;
        }

        // New Password validation
        if (!formData.newPassword.trim()) {
            errors.newPassword = 'New password is required';
            isValid = false;
        } else if (formData.newPassword.length < 8) {
            errors.newPassword = 'New password must be at least 8 characters long';
            isValid = false;
        }

        // Confirm Password validation
        if (!formData.confirmPassword.trim()) {
            errors.confirmPassword = 'Please confirm your new password';
            isValid = false;
        } else if (formData.newPassword !== formData.confirmPassword) {
            errors.confirmPassword = 'Passwords do not match';
            isValid = false;
        }

        setFormErrors(errors);
        return isValid;
    };

    const handleSaveChanges = async () => {
        if (!formData.name.trim()) {
            toast({
                title: "Error",
                description: "Name is required",
                variant: "destructive"
            });
            return;
        }

        setSavingProfile(true);
        try {
            // Split name into firstName and lastName for the API
            const nameParts = formData.name.trim().split(' ');
            const firstName = nameParts[0];
            const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

            await updateProfile(firstName, lastName);
            toast({
                title: "Success",
                description: "Profile updated successfully"
            });
        } catch (error: any) {
            console.error('Error saving profile:', error);
            toast({
                title: "Error",
                description: error.message || "Failed to update profile",
                variant: "destructive"
            });
        } finally {
            setSavingProfile(false);
        }
    };

    const handleChangePassword = async () => {
        // Clear previous errors
        setFormErrors({});

        // Validate form
        if (!validatePasswordForm()) {
            return;
        }

        setLoading(true);
        try {
            await changePassword(formData.currentPassword, formData.newPassword);
            toast({
                title: "Success",
                description: "Password changed successfully"
            });
            // Clear password fields
            setFormData(prev => ({
                ...prev,
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
            }));
        } catch (error: any) {
            console.error('Password change error:', error);
            toast({
                title: "Error",
                description: error.message || "Failed to change password",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteAccount = async () => {
        setDeletingAccount(true);
        try {
            await deleteAccount();
            toast({
                title: "Account Deleted",
                description: "Your account has been successfully deleted"
            });
            navigate('/login');
        } catch (error: any) {
            console.error('Error deleting account:', error);
            toast({
                title: "Error",
                description: error.message || "Failed to delete account",
                variant: "destructive"
            });
        } finally {
            setDeletingAccount(false);
        }
    };

    return (
        <div className="space-y-8">
            {/* Account Section */}
            <Card className="p-6">
                <div className="space-y-6">
                    <div>
                        <h2 className="text-xl font-semibold">Account</h2>
                        <p className="text-sm text-gray-500">Manage your account settings</p>
                    </div>

                    {/* Profile Picture */}
                    <div className="space-y-4">
                        <Label>Profile Picture</Label>
                        <div className="flex items-center gap-4">
                            <div className="relative w-16 h-16">
                                <div className="w-full h-full rounded-full bg-orange-600 flex items-center justify-center text-white text-2xl uppercase overflow-hidden">
                                    {user?.avatar ? (
                                        <img
                                            src={user.avatar}
                                            alt={user?.name || 'User'}
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        (user?.name?.[0] || user?.firstName?.[0] || 'U')
                                    )}
                                </div>
                                {user?.avatar && (
                                    <button
                                        onClick={handleRemoveProfilePicture}
                                        disabled={uploadingPicture}
                                        className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                                        type="button"
                                    >
                                        <X className="w-3 h-3" />
                                    </button>
                                )}
                            </div>
                            <div className="space-y-2">
                                <Button
                                    variant="outline"
                                    onClick={() => document.getElementById('profile-upload')?.click()}
                                    disabled={uploadingPicture}
                                >
                                    {uploadingPicture ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Uploading...
                                        </>
                                    ) : (
                                        user?.avatar ? 'Change Picture' : 'Upload Picture'
                                    )}
                                </Button>
                                <input
                                    type="file"
                                    id="profile-upload"
                                    className="hidden"
                                    accept="image/*"
                                    onChange={handleProfilePictureUpload}
                                    disabled={uploadingPicture}
                                />
                                <p className="text-sm text-gray-500">Maximum size 5 MB</p>
                            </div>
                        </div>
                    </div>

                    {/* Email Address */}
                    <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            disabled
                            className="bg-gray-50"
                        />
                    </div>

                    {/* Name Fields */}
                    <div className="space-y-2">
                        <Label htmlFor="name">Name</Label>
                        <Input
                            id="name"
                            value={formData.name}
                            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        />
                    </div>

                    {/* Save Changes Button */}
                    <div className="flex justify-start">
                        <Button
                            onClick={handleSaveChanges}
                            className="bg-orange-500 hover:bg-orange-600 text-white"
                            disabled={savingProfile}
                        >
                            {savingProfile ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Saving Changes...
                                </>
                            ) : (
                                'Save Changes'
                            )}
                        </Button>
                    </div>
                </div>
            </Card>

            {/* Change Password Section */}
            <Card className="p-6">
                <div className="space-y-6">
                    <div>
                        <h2 className="text-xl font-semibold">Change Password</h2>
                        <p className="text-sm text-gray-500">Update your account password</p>
                    </div>

                    {/* Current Password */}
                    <div className="space-y-2">
                        <Label htmlFor="currentPassword">Current Password</Label>
                        <div className="relative">
                            <Input
                                id="currentPassword"
                                type={showCurrentPassword ? 'text' : 'password'}
                                value={formData.currentPassword}
                                onChange={(e) => {
                                    setFormData({ ...formData, currentPassword: e.target.value });
                                    if (formErrors.currentPassword) {
                                        setFormErrors({ ...formErrors, currentPassword: undefined });
                                    }
                                }}
                                placeholder="Enter current password"
                                disabled={loading}
                                className={formErrors.currentPassword ? 'border-red-500' : ''}
                            />
                            <button
                                type="button"
                                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                className="absolute right-3 top-1/2 -translate-y-1/2"
                                disabled={loading}
                            >
                                {showCurrentPassword ? (
                                    <EyeOff className="h-4 w-4 text-gray-500" />
                                ) : (
                                    <Eye className="h-4 w-4 text-gray-500" />
                                )}
                            </button>
                        </div>
                        {formErrors.currentPassword && (
                            <p className="text-sm text-red-500 mt-1">{formErrors.currentPassword}</p>
                        )}
                    </div>

                    {/* New Password */}
                    <div className="space-y-2">
                        <Label htmlFor="newPassword">New Password</Label>
                        <div className="relative">
                            <Input
                                id="newPassword"
                                type={showNewPassword ? 'text' : 'password'}
                                value={formData.newPassword}
                                onChange={(e) => {
                                    setFormData({ ...formData, newPassword: e.target.value });
                                    if (formErrors.newPassword) {
                                        setFormErrors({ ...formErrors, newPassword: undefined });
                                    }
                                }}
                                placeholder="Enter new password"
                                disabled={loading}
                                className={formErrors.newPassword ? 'border-red-500' : ''}
                            />
                            <button
                                type="button"
                                onClick={() => setShowNewPassword(!showNewPassword)}
                                className="absolute right-3 top-1/2 -translate-y-1/2"
                                disabled={loading}
                            >
                                {showNewPassword ? (
                                    <EyeOff className="h-4 w-4 text-gray-500" />
                                ) : (
                                    <Eye className="h-4 w-4 text-gray-500" />
                                )}
                            </button>
                        </div>
                        {formErrors.newPassword && (
                            <p className="text-sm text-red-500 mt-1">{formErrors.newPassword}</p>
                        )}
                        <p className="text-sm text-gray-500 mt-2">Password must be at least 8 characters long</p>
                    </div>

                    {/* Confirm New Password */}
                    <div className="space-y-2">
                        <Label htmlFor="confirmPassword">Confirm New Password</Label>
                        <div className="relative">
                            <Input
                                id="confirmPassword"
                                type={showConfirmPassword ? 'text' : 'password'}
                                value={formData.confirmPassword}
                                onChange={(e) => {
                                    setFormData({ ...formData, confirmPassword: e.target.value });
                                    if (formErrors.confirmPassword) {
                                        setFormErrors({ ...formErrors, confirmPassword: undefined });
                                    }
                                }}
                                placeholder="Confirm new password"
                                disabled={loading}
                                className={formErrors.confirmPassword ? 'border-red-500' : ''}
                            />
                            <button
                                type="button"
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                className="absolute right-3 top-1/2 -translate-y-1/2"
                                disabled={loading}
                            >
                                {showConfirmPassword ? (
                                    <EyeOff className="h-4 w-4 text-gray-500" />
                                ) : (
                                    <Eye className="h-4 w-4 text-gray-500" />
                                )}
                            </button>
                        </div>
                        {formErrors.confirmPassword && (
                            <p className="text-sm text-red-500 mt-1">{formErrors.confirmPassword}</p>
                        )}
                    </div>

                    {/* Change Password Button */}
                    <div className="flex justify-start">
                        <Button
                            onClick={handleChangePassword}
                            className="bg-orange-500 hover:bg-orange-600 text-white"
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Changing Password...
                                </>
                            ) : (
                                'Change Password'
                            )}
                        </Button>
                    </div>
                </div>
            </Card>

            {/* Danger Zone */}
            <Card className="p-6 border-2 border-red-200 bg-red-50">
                <div className="space-y-4">
                    <div>
                        <h2 className="text-xl font-semibold text-red-600">Danger Zone</h2>
                        <p className="text-sm text-red-500">Irreversible and destructive actions</p>
                    </div>
                    <div className="pt-2">
                        <AlertDialog>
                            <AlertDialogTrigger asChild>
                                <Button
                                    variant="outline"
                                    className="border-2 border-red-200 text-red-600 hover:text-red-700 hover:bg-red-100 w-full font-semibold"
                                >
                                    Delete account
                                </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                                <AlertDialogHeader>
                                    <AlertDialogTitle className="text-red-600">Are you absolutely sure?</AlertDialogTitle>
                                    <AlertDialogDescription className="space-y-4">
                                        This action cannot be undone. This will permanently delete your account and remove all your data including:
                                        <ul className="list-disc list-inside text-red-600">
                                            <li>All your domains</li>
                                            <li>All your articles</li>
                                            <li>All your settings and preferences</li>
                                            <li>Your profile information</li>
                                        </ul>
                                    </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                        onClick={handleDeleteAccount}
                                        className="bg-red-600 hover:bg-red-700 text-white"
                                        disabled={deletingAccount}
                                    >
                                        {deletingAccount ? (
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                Deleting...
                                            </>
                                        ) : (
                                            'Yes, Delete My Account'
                                        )}
                                    </AlertDialogAction>
                                </AlertDialogFooter>
                            </AlertDialogContent>
                        </AlertDialog>
                        <p className="text-xs text-red-500 mt-2">
                            Once you delete your account, there is no going back. Please be certain.
                        </p>
                    </div>
                </div>
            </Card>
        </div>
    );
};

export default AccountSettings; 