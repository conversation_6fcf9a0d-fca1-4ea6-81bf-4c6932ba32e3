import OpenAI from 'openai';
import { WebsiteInfo } from './websiteService';

export interface ArticleGenerationParams {
    websiteInfo: WebsiteInfo;
    topic?: string;
    tone?: string;
    targetLength?: number;
}

export interface GeneratedArticle {
    id?: string;
    title: string;
    description: string;
    content: string;
    tag?: string;
    status: string;
    author: string;
    date: string;
    image?: string;
    urlSlug?: string;
    metaDescription?: string;
    excerpt?: string;
    keywords?: string[];
    sourceUrl?: string;
    metadata?: {
        originalTitle?: string;
        originalDescription?: string;
        generatedAt?: string;
    };
}

export class ArticleService {
    private openai: OpenAI;

    constructor(apiKey: string) {
        this.openai = new OpenAI({
            apiKey: apiKey,
            dangerouslyAllowBrowser: true // Enable client-side usage
        });
    }

    async generateArticle(params: ArticleGenerationParams): Promise<GeneratedArticle> {
        const { websiteInfo, topic, tone = 'professional', targetLength = 1000 } = params;

        try {
            // Create a detailed prompt for GPT-4
            const prompt = `
        Write a high-quality SEO-optimized article based on the following website information:
        
        Website Title: ${websiteInfo.title}
        Website Description: ${websiteInfo.description}
        Main Topics: ${websiteInfo.mainTopics.join(', ')}
        Keywords: ${websiteInfo.keywords.join(', ')}
        
        Requirements:
        - Topic: ${topic || 'Choose a relevant topic based on the website content'}
        - Tone: ${tone}
        - Length: Approximately ${targetLength} words
        - Include relevant headings (H2, H3)
        - Make it SEO-friendly
        - Write in a natural, engaging style
        - Include a meta description and keywords
        
        Format the response as JSON with the following structure:
        {
          "title": "Article Title",
          "content": "Full article content with HTML tags",
          "metaDescription": "SEO meta description",
          "keywords": ["keyword1", "keyword2", ...],
          "excerpt": "Brief article excerpt"
        }
      `;

            const completion = await this.openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are an expert content writer and SEO specialist."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.7,
                max_tokens: 4000
            });

            const response = completion.choices[0]?.message?.content;
            if (!response) {
                throw new Error('No response from GPT-4');
            }

            return JSON.parse(response) as GeneratedArticle;
        } catch (error) {
            console.error('Error generating article:', error);
            throw new Error('Failed to generate article');
        }
    }
} 