import apiClient from '@/lib/api-client';

interface CheckoutSessionResponse {
    sessionId: string;
    url: string;
}

interface SubscriptionResponse {
    subscription: {
        active: boolean;
        planType: string;
        stripeCustomerId?: string;
        stripeSubscriptionId?: string;
        updatedAt?: string;
        startDate?: string;
        canceledAt?: string | Date;
        expiresAt?: string | Date;
    };
}

interface PlanFeatures {
    maxDomains: number;
    maxArticlesPerMonth: number;
    customBranding: boolean;
}

interface Plan {
    _id?: string;
    userId: string;
    type: string;
    displayType: string; // User-friendly plan type (Monthly/Yearly/Free)
    features: PlanFeatures;
    startDate: string;
    endDate?: string;
    status: string;
    createdAt?: string;
    updatedAt?: string;
    domainId?: string;
}

interface PlanResponse {
    plan: Plan;
}

interface ProratedAmountResponse {
    amount: number;
    currency: string;
    details: {
        originalAmount: number;
        discount: number;
        finalAmount: number;
        remainingDays: number;
        totalDays: number;
        currentPlanPrice: number;
        newPlanPrice: number;
        unusedCredit: number;
        currentPlanType: string;
        newPlanType: string;
        subscriptionStart?: string | null;
        subscriptionEnd?: string | null;
    };
}

class PaymentService {
    private baseUrl = '/payments';

    // Standard prices in USD
    private PLAN_PRICES = {
        Monthly: 59.99,
        Yearly: 159.99,
        Daily: 9.99,
        Free: 0
    };

    /**
     * Create a checkout session for subscribing to a plan
     * @param domainId The ID of the domain to associate with the subscription
     * @param planType The type of plan ('Daily', 'Monthly' or 'Yearly')
     * @returns Checkout session details
     */
    async createCheckoutSession(domainId: string, planType: 'Daily' | 'Monthly' | 'Yearly'): Promise<CheckoutSessionResponse> {
        try {
            const response = await apiClient.post(`${this.baseUrl}/create-checkout-session`, {
                domainId,
                planType
            }, {
                timeout: 10000 // 10 second timeout
            });
            return response.data;
        } catch (error) {
            console.error('Error creating checkout session:', error);

            // Enhance error message for network issues
            if (error instanceof Error) {
                if (error.message.includes('timeout') || error.message.includes('Network Error')) {
                    throw new Error('Network connection issue. Please check your internet connection and try again.');
                }
            }

            throw error;
        }
    }

    /**
     * Get subscription status for a domain
     * @param domainId The ID of the domain
     * @returns Subscription details
     */
    async getSubscription(domainId: string): Promise<SubscriptionResponse> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/subscription/${domainId}`, {
                timeout: 8000 // 8 second timeout
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching subscription:', error);

            // Return a default response for connection errors to avoid breaking the UI
            if (error instanceof Error &&
                (error.message.includes('timeout') ||
                    error.message.includes('Network Error') ||
                    error.message.includes('Failed to fetch'))) {

                // Return a fake successful response so the UI can continue
                return {
                    subscription: {
                        active: true,
                        planType: 'Monthly'
                    }
                };
            }

            throw error;
        }
    }

    /**
     * Create or update a plan for the current user
     * @param planType The type of plan ('Free', 'Daily', 'Monthly', or 'Yearly')
     * @param domainId The ID of the domain to associate with the plan
     * @returns The created or updated plan
     */
    async createPlan(planType: 'Free' | 'Daily' | 'Monthly' | 'Yearly', domainId: string): Promise<PlanResponse> {
        try {
            const response = await apiClient.post(`${this.baseUrl}/create-plan`, {
                planType,
                domainId
            }, {
                timeout: 10000 // 10 second timeout
            });
            return response.data;
        } catch (error) {
            console.error('Error creating/updating plan:', error);
            throw error;
        }
    }

    /**
     * Calculate prorated amount when changing between plans
     * @param domainId The domain ID for which to calculate proration
     * @param newPlanType The new plan type ('Daily', 'Monthly' or 'Yearly')
     * @returns The prorated amount details
     */
    async calculateProratedAmount(domainId: string, newPlanType: 'Daily' | 'Monthly' | 'Yearly'): Promise<ProratedAmountResponse> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/calculate-proration`, {
                params: {
                    domainId,
                    newPlanType
                },
                timeout: 8000 // 8 second timeout
            });
            return response.data;
        } catch (error) {
            console.error('Error calculating prorated amount:', error);

            // Return a default response for errors to avoid breaking the UI
            if (error instanceof Error) {
                // Calculate default prorated amount based on plan prices
                const newPlanPrice = this.PLAN_PRICES[newPlanType];

                return {
                    amount: newPlanPrice,
                    currency: 'usd',
                    details: {
                        originalAmount: newPlanPrice,
                        discount: 0,
                        finalAmount: newPlanPrice,
                        remainingDays: 0,
                        totalDays: newPlanType === 'Daily' ? 1 : newPlanType === 'Monthly' ? 30 : 365,
                        currentPlanPrice: 0, // Placeholder, will be updated by API
                        newPlanPrice: newPlanPrice,
                        unusedCredit: 0, // Placeholder, will be updated by API
                        currentPlanType: 'Free', // Placeholder, will be updated by API
                        newPlanType: newPlanType,
                        subscriptionStart: null, // Placeholder, will be updated by API
                        subscriptionEnd: null // Placeholder, will be updated by API
                    }
                };
            }

            throw error;
        }
    }

    /**
     * Get the current user's plan
     * @param domainId Optional domain ID to get domain-specific plan
     * @returns The user's current plan
     */
    async getCurrentPlan(domainId?: string): Promise<PlanResponse> {
        try {
            // If domainId is provided, get domain-specific plan
            const url = domainId
                ? `${this.baseUrl}/plan?domainId=${domainId}`
                : `${this.baseUrl}/plan`;

            const response = await apiClient.get(url, {
                timeout: 8000 // 8 second timeout
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching current plan:', error);

            // Return a default plan for connection errors to avoid breaking the UI
            if (error instanceof Error &&
                (error.message.includes('timeout') ||
                    error.message.includes('Network Error') ||
                    error.message.includes('Failed to fetch'))) {

                return {
                    plan: {
                        userId: '',
                        type: 'Free',
                        displayType: 'Free',
                        features: {
                            maxDomains: 1,
                            maxArticlesPerMonth: 10,
                            customBranding: false
                        },
                        startDate: new Date().toISOString(),
                        status: 'active'
                    }
                };
            }

            throw error;
        }
    }

    /**
     * Create a checkout session with prorated amount for plan changes
     * @param domainId The ID of the domain to change plan for
     * @param newPlanType The new plan type ('Daily', 'Monthly' or 'Yearly')
     * @returns Checkout session details
     */
    async createProratedCheckoutSession(domainId: string, newPlanType: 'Daily' | 'Monthly' | 'Yearly'): Promise<CheckoutSessionResponse> {
        try {
            console.log(`Creating prorated checkout session for domain ${domainId} to ${newPlanType} plan`);
            const response = await apiClient.post(`${this.baseUrl}/create-prorated-checkout`, {
                domainId,
                newPlanType
            }, {
                timeout: 10000 // 10 second timeout
            });

            console.log('Prorated checkout response:', response.data);

            // Handle both regular checkout sessions and automatic upgrades (when prorated amount is 0)
            if (response.data.success === true && response.data.url) {
                // This is an automatic upgrade (prorated amount was 0)
                console.log('Automatic upgrade detected (zero prorated amount)');
                return {
                    sessionId: 'automatic',
                    url: response.data.url
                };
            } else if (response.data.sessionId && response.data.url) {
                // Regular checkout session
                console.log('Regular checkout session created:', response.data.sessionId);
                return response.data;
            } else {
                throw new Error('Invalid response format from server');
            }
        } catch (error) {
            console.error('Error creating prorated checkout session:', error);
            throw error;
        }
    }

    /**
     * Sync subscription status with Stripe
     * @param domainId The ID of the domain to sync subscription for
     * @returns Updated subscription data
     */
    async syncSubscription(domainId: string): Promise<any> {
        try {
            console.log(`Syncing subscription for domain ${domainId} with Stripe`);
            const response = await apiClient.post(`${this.baseUrl}/sync-subscription/${domainId}`, {}, {
                timeout: 10000 // 10 second timeout
            });
            console.log('Subscription sync response:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error syncing subscription:', error);
            throw error;
        }
    }

    /**
     * Cancel subscription and downgrade to free plan
     * @param domainId The ID of the domain to cancel subscription for
     * @param cancelImmediately Whether to cancel the subscription immediately (true) or at the end of the billing period (false)
     * @returns Response indicating success or failure
     */
    async cancelSubscription(domainId: string, cancelImmediately: boolean = false): Promise<{
        success: boolean;
        message: string;
        expiresAt?: string | Date;
    }> {
        try {
            // First get the subscription info to check if it's a Daily plan
            let dailyPlan = false;
            try {
                const subData = await this.getSubscription(domainId);
                dailyPlan = subData?.subscription?.planType === 'Daily';
                console.log(`Checking plan type before cancellation: ${subData?.subscription?.planType}, Daily plan: ${dailyPlan}`);
            } catch (error) {
                console.error('Error checking subscription type before cancellation:', error);
            }

            // For Daily plans, try syncing first to handle expired subscriptions
            if (dailyPlan) {
                try {
                    // Special handling for Daily plans - try sync first
                    const syncResponse = await this.syncSubscription(domainId);
                    console.log('Daily plan sync response:', syncResponse);

                    // Check if the sync already updated the plan to free due to expiration
                    if (syncResponse?.subscription?.planType === 'Free' || !syncResponse?.subscription?.active) {
                        console.log('Daily plan already expired and updated to Free');

                        // Force refresh subscription data
                        await this.refreshSubscriptionData(domainId);

                        // Return success response
                        return {
                            success: true,
                            message: 'Daily plan has expired. Your plan has been updated to Free.',
                            expiresAt: new Date()
                        };
                    }
                } catch (syncError) {
                    console.error('Error syncing Daily plan before cancellation:', syncError);
                    // Continue with cancellation attempt even if sync fails
                }
            } else {
                // For non-Daily plans, sync subscription with Stripe
                try {
                    await this.syncSubscription(domainId);
                    console.log('Subscription synced with Stripe before cancellation');
                } catch (syncError) {
                    console.error('Error syncing subscription before cancellation:', syncError);
                    // Continue with cancellation attempt even if sync fails
                }
            }

            console.log('Sending cancel subscription request for domain:', domainId, 'cancelImmediately:', cancelImmediately);
            const response = await apiClient.post(`${this.baseUrl}/cancel-subscription`, {
                domainId,
                cancelImmediately
            }, {
                timeout: 30000 // Increase timeout to 30 seconds
            });
            console.log('Cancel subscription API response:', response.data);

            // Force refresh subscription data
            try {
                await this.refreshSubscriptionData(domainId);
                console.log('Subscription data refreshed after cancellation');
            } catch (refreshError) {
                console.error('Error refreshing subscription data after cancellation:', refreshError);
            }

            return response.data;
        } catch (error) {
            console.error('Error canceling subscription:', error);

            // Check for response with success field for Daily plan special handling
            if (error.response?.data?.success === true) {
                console.log('Server returned success response with error status code:', error.response.data);

                // Force refresh subscription data
                try {
                    await this.refreshSubscriptionData(domainId);
                } catch (refreshError) {
                    console.error('Error refreshing subscription data:', refreshError);
                }

                // Return the response data as it may contain useful information
                return error.response.data;
            }

            // Handle specific case of no active subscription
            if (error.response?.status === 400 &&
                (error.response?.data?.message?.includes('does not have an active subscription') ||
                    error.response?.data?.error?.includes('No active subscription'))) {

                console.log('No active subscription found, refreshing subscription data');

                // Force refresh subscription data to update the UI
                try {
                    await this.refreshSubscriptionData(domainId);
                } catch (refreshError) {
                    console.error('Error refreshing subscription data:', refreshError);
                }

                // Return a formatted error that the UI can handle gracefully
                throw new Error('This domain does not have an active subscription to cancel.');
            }

            // Try to extract the error message from the response if available
            let errorMessage = 'Failed to cancel subscription. Please try again.';
            if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.data?.error) {
                errorMessage = error.response.data.error;
            } else if (error.message) {
                errorMessage = error.message;
            }

            // Enhance error message for network issues
            if (error.message?.includes('timeout')) {
                errorMessage = 'The request timed out. The server might be processing your request. Please refresh the page to check if your subscription was canceled.';
            }
            if (error.message?.includes('Network Error')) {
                errorMessage = 'Network connection issue. Please check your internet connection and try again.';
            }

            throw new Error(errorMessage);
        }
    }

    /**
     * Verify subscription status in Stripe
     * @param subscriptionId The ID of the subscription to verify
     * @returns Subscription status details from Stripe and local database
     */
    async verifySubscriptionStatus(subscriptionId: string): Promise<any> {
        try {
            console.log('Verifying subscription status for:', subscriptionId);
            const response = await apiClient.get(`${this.baseUrl}/verify-subscription/${subscriptionId}`, {
                timeout: 8000 // 8 second timeout
            });
            console.log('Subscription verification response:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error verifying subscription status:', error);

            // Enhance error message for network issues
            if (error instanceof Error) {
                if (error.message.includes('timeout')) {
                    throw new Error('The request timed out. Please try again later.');
                }
                if (error.message.includes('Network Error')) {
                    throw new Error('Network connection issue. Please check your internet connection and try again.');
                }
            }

            throw error;
        }
    }

    /**
     * Force refreshes the subscription data from the server
     * This is useful after a payment is processed to ensure UI is up to date
     * @param domainId The domain ID to refresh subscription data for
     */
    async refreshSubscriptionData(domainId: string | any): Promise<any> {
        try {
            // Ensure we have a valid string ID, not a domain object
            const id = typeof domainId === 'string' ? domainId :
                (domainId && domainId._id) ? domainId._id :
                    null;

            if (!id) {
                throw new Error('Invalid domain ID provided');
            }

            const response = await apiClient.get(`/payments/refresh-subscription/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error refreshing subscription data:', error);
            throw error;
        }
    }
}

export const paymentService = new PaymentService(); 