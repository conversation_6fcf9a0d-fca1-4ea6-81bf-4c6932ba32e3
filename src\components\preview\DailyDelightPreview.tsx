import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { Article } from '../../types/Article';
import { useAuth } from '@/contexts/AuthContext';
import { articleService } from '@/services/articleService';

interface DailyDelightPreviewProps {
    article: Article;
    fontFamily: string;
    fontColor: string;
    brandColor: string;
}

const DailyDelightPreview: React.FC<DailyDelightPreviewProps> = ({
    article,
    fontFamily,
    fontColor,
    brandColor
}) => {
    const { user } = useAuth();
    const authorData = user || article.userData;
    const [relatedArticles, setRelatedArticles] = useState<Article[]>([]);
    const [authorArticles, setAuthorArticles] = useState<Article[]>([]);
    // console.log(article);
    document.title = article.metadata?.originalTitle;

    useEffect(() => {
        const fetchArticles = async () => {
            try {
                // Get all articles from the domain
                const articles = await articleService.getArticles(article.domainId);

                // First, get articles by the same author
                const authorFiltered = articles
                    .filter(a => a.id !== article.id) // Exclude current article
                    .filter(a => {
                        // Match by userId if available
                        if (article.userId && a.userId) {
                            return a.userId === article.userId;
                        }
                        // Fallback to matching by author name if userId not available
                        return a.author === article.author;
                    })
                    .sort((a, b) => {
                        const dateA = new Date(a.createdAt || a.date).getTime();
                        const dateB = new Date(b.createdAt || b.date).getTime();
                        return dateB - dateA; // Sort by date descending
                    })
                    .slice(0, 3); // Get top 3 most recent articles
                setAuthorArticles(authorFiltered);

                // Then, get related articles excluding the author's articles
                const authorArticleIds = new Set(authorFiltered.map(a => a.id));
                const filtered = articles
                    .filter(a => a.id !== article.id) // Exclude current article
                    .filter(a => !authorArticleIds.has(a.id)) // Exclude author's articles
                    .filter(a => {
                        // Check for keyword overlap
                        const currentKeywords = article.keywords || [];
                        const articleKeywords = a.keywords || [];
                        return articleKeywords.some(keyword => currentKeywords.includes(keyword));
                    })
                    .sort((a, b) => {
                        // Prioritize articles with more matching keywords
                        const aKeywords = a.keywords || [];
                        const bKeywords = b.keywords || [];
                        const currentKeywords = article.keywords || [];
                        const aMatches = aKeywords.filter(k => currentKeywords.includes(k)).length;
                        const bMatches = bKeywords.filter(k => currentKeywords.includes(k)).length;
                        return bMatches - aMatches;
                    })
                    .slice(0, 3); // Get top 3 related articles
                setRelatedArticles(filtered);
            } catch (error) {
                console.error('Error fetching articles:', error);
            }
        };

        if (article.domainId) {
            fetchArticles();
        }
    }, [article.domainId, article.id, article.userId, article.author, article.keywords]);

    // Only show the Related Articles section if we have articles that aren't by the same author
    const showRelatedArticles = relatedArticles.length > 0 &&
        !relatedArticles.every(a => a.userId === article.userId || a.author === article.author);

    const renderArticleCard = (articleItem: Article) => (
        <a
            key={articleItem.id}
            href={`/articles/${articleItem.urlSlug}`}
            className="group block bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200"
        >
            {articleItem.image?.url && (
                <div className="aspect-video w-full overflow-hidden">
                    <img
                        src={articleItem.image.url}
                        alt={articleItem.title}
                        className="w-full h-full object-cover transition-transform group-hover:scale-105"
                    />
                </div>
            )}
            <div className="p-4">
                <h4
                    className="font-medium line-clamp-2 group-hover:text-orange-500 transition-colors"
                    style={{ color: '#1a1a1a' }}
                >
                    {articleItem.title}
                </h4>
                <p className="text-sm mt-2 line-clamp-2" style={{ color: fontColor }}>
                    {articleItem.description}
                </p>
                <div className="text-sm mt-2" style={{ color: fontColor }}>
                    {format(new Date(articleItem.createdAt || articleItem.date), 'MMM d, yyyy')}
                </div>
            </div>
        </a>
    );

    return (
        <div className="max-w-4xl mx-auto px-4" style={{ fontFamily }}>
            {/* Tag */}
            {article.tag && (
                <div className="mb-6">
                    <span
                        className="px-3 py-1 rounded-full text-sm inline-block"
                        style={{
                            backgroundColor: `${brandColor}15`,
                            color: brandColor,
                            border: `1px solid ${brandColor}`
                        }}
                    >
                        {article.tag}
                    </span>
                </div>
            )}

            {/* Title */}
            <h1 className="text-3xl font-bold mb-4" style={{ color: '#1a1a1a' }}>
                {article.title}
            </h1>

            {/* Author and Meta Info */}
            <div className="flex flex-wrap items-center gap-4 mb-6 justify-between">
                {/* Author Info */}
                <div className="flex items-center space-x-2">
                    <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden">
                        {authorData?.avatar ? (
                            <img
                                src={authorData.avatar}
                                alt={`${authorData.firstName} ${authorData.lastName}`}
                                className="w-full h-full object-cover"
                            />
                        ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-500 font-medium">
                                {authorData ? authorData.firstName.charAt(0) : 'A'}
                            </div>
                        )}
                    </div>
                    <div>
                        <div className="font-medium" style={{ color: '#1a1a1a' }}>
                            {authorData ? `${authorData.firstName} ${authorData.lastName}` : 'Anonymous'}
                        </div>
                        <div className="text-sm" style={{ color: fontColor }}>
                            {authorData ? 'Author' : 'Technical Writer'}
                        </div>
                    </div>
                </div>

                {/* Meta Info */}
                <div className="flex items-center space-x-2 text-sm" style={{ color: fontColor }}>
                    <span>{format(new Date(article.createdAt || article.date), 'MMM d, yyyy')}</span>
                </div>
            </div>

            {/* Main Image */}
            {(article.image?.url || article.image?.thumb || article.video?.thumbnail) && (
                <div className="mb-8">
                    <img
                        src={article.image?.url || article.image?.thumb || article.video?.thumbnail}
                        alt={article.title}
                        className="w-full rounded-lg"
                        onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            if (target.src === article.image?.thumb && article.image?.url) {
                                target.src = article.image.url;
                            } else {
                                target.src = '/article-placeholder.svg';
                            }
                        }}
                    />
                </div>
            )}

            {/* Article Content */}
            <div
                className="prose max-w-none mb-12"
                style={{ color: fontColor }}
                dangerouslySetInnerHTML={{ __html: article.content || '' }}
            />

            {/* Keywords Section */}
            {article.keywords && article.keywords.length > 0 && (
                <div className="border-t pt-8 mb-12">
                    <h3 className="text-xl font-semibold mb-4" style={{ color: '#1a1a1a' }}>Keywords</h3>
                    <div className="flex flex-wrap gap-2">
                        {article.keywords.map((keyword, index) => (
                            <span
                                key={index}
                                className="px-3 py-1 rounded-full text-sm"
                                style={{
                                    backgroundColor: `${brandColor}15`, // 15% opacity
                                    color: brandColor,
                                    border: `1px solid ${brandColor}`
                                }}
                            >
                                {keyword}
                            </span>
                        ))}
                    </div>
                </div>
            )}

            {/* Related Articles Section */}
            {showRelatedArticles && (
                <div className="border-t pt-8 mb-12">
                    <h3 className="text-xl font-semibold mb-6" style={{ color: '#1a1a1a' }}>Related Articles</h3>
                    <div className="grid gap-6 md:grid-cols-3">
                        {relatedArticles.map(renderArticleCard)}
                    </div>
                </div>
            )}

            {/* More from Author Section */}
            {authorArticles.length > 0 && (
                <div className="border-t pt-8 mb-12">
                    <h3 className="text-xl font-semibold mb-6" style={{ color: '#1a1a1a' }}>
                        More from {authorData ? `${authorData.firstName} ${authorData.lastName}` : (typeof article.author === 'string' ? article.author : 'this Author')}
                    </h3>
                    <div className="grid gap-6 md:grid-cols-3">
                        {authorArticles.map(renderArticleCard)}
                    </div>
                </div>
            )}
        </div>
    );
};

export default DailyDelightPreview; 