import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { X, Loader2 } from 'lucide-react';
import { api } from '@/services/api';
import { toast } from 'sonner';

interface AddDomainModalProps {
    onClose: () => void;
}

export const AddDomainModal: React.FC<AddDomainModalProps> = ({ onClose }) => {
    const navigate = useNavigate();
    const [url, setUrl] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    const handleAddDomain = async () => {
        try {
            setError('');
            setIsLoading(true);

            // Basic URL validation
            if (!url) {
                setError('Please enter a domain URL');
                return;
            }

            let formattedUrl = url;
            if (!url.startsWith('http')) {
                formattedUrl = 'https://' + url;
            }

            // Try to validate URL format
            try {
                new URL(formattedUrl);
            } catch (e) {
                setError('Please enter a valid URL');
                return;
            }

            // Create domain - this will trigger the scraping
            const response = await api.post('/domains', { url: formattedUrl });

            toast.success('Domain added successfully! Redirecting to settings...');

            // Close modal and navigate to domain settings
            onClose();
            navigate(`/dashboard/settings?domain=${response.data._id}`);
        } catch (error: any) {
            setError(error.response?.data?.message || error.response?.data?.error || 'Failed to add domain');
            toast.error(error.response?.data?.message || error.response?.data?.error || 'Failed to add domain');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md p-6 bg-white">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold">Add New Domain</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                        disabled={isLoading}
                    >
                        <X className="w-5 h-5" />
                    </button>
                </div>

                <div className="space-y-4">
                    <div>
                        <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
                            Domain URL
                        </label>
                        <Input
                            id="url"
                            type="text"
                            placeholder="Enter your domain URL (e.g., example.com)"
                            value={url}
                            onChange={(e) => setUrl(e.target.value)}
                            disabled={isLoading}
                            className="w-full"
                        />
                        {error && (
                            <p className="mt-1 text-sm text-red-600">{error}</p>
                        )}
                    </div>

                    <div className="bg-orange-50 rounded-lg p-4">
                        <h3 className="font-medium text-orange-800 mb-2">What happens next:</h3>
                        <ul className="text-sm text-orange-700 space-y-1">
                            <li>• We'll analyze your website</li>
                            <li>• Extract brand information</li>
                            <li>• Set up your domain settings</li>
                            <li>• Create a dedicated dashboard</li>
                        </ul>
                    </div>

                    <div className="flex gap-3">
                        <Button
                            variant="outline"
                            onClick={onClose}
                            disabled={isLoading}
                            className="flex-1"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleAddDomain}
                            disabled={isLoading}
                            className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    Analyzing...
                                </>
                            ) : (
                                'Add Domain'
                            )}
                        </Button>
                    </div>
                </div>
            </Card>
        </div>
    );
};
