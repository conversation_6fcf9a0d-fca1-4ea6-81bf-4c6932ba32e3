import axios from 'axios';
import { Article, ArticleVideo } from '@/types/Article';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance
export const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Add request interceptor to include auth token
api.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

interface ImageCredit {
    name: string;
    username: string;
    link: string;
}

interface ArticleImage {
    url: string;
    thumb: string;
    description?: string;
    credit?: ImageCredit;
}

export type { ArticleVideo, ArticleImage, ArticleMetadata } from '@/types/Article';

export const articleApi = {
    // Get all articles
    getArticles: async (domainId?: string): Promise<Article[]> => {
        try {
            const response = await api.get('/articles', { params: { domainId } });
            return response.data;
        } catch (error) {
            console.error('Error fetching articles:', error);
            throw new Error('Failed to fetch articles');
        }
    },

    // Get article by ID
    getArticleById: async (id: string): Promise<Article> => {
        try {
            const response = await api.get(`/articles/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching article:', error);
            throw new Error('Failed to fetch article');
        }
    },

    // Create article
    createArticle: async (article: Partial<Article>): Promise<Article> => {
        try {
            const response = await api.post('/articles', article);
            return response.data;
        } catch (error) {
            console.error('Error creating article:', error);
            throw new Error('Failed to create article');
        }
    },

    // Update article
    updateArticle: async (id: string, article: Partial<Article>): Promise<Article> => {
        try {
            const response = await api.put(`/articles/${id}`, article);
            const updatedArticle = response.data;
            return {
                ...updatedArticle,
                id: updatedArticle._id || updatedArticle.id,
                status: updatedArticle.status.toLowerCase(),
                date: updatedArticle.date || new Date().toISOString(),
                video: updatedArticle.video
            };
        } catch (error) {
            console.error('Error updating article:', error);
            if (error.response?.status === 413) {
                throw new Error('Article content is too large. Please reduce the content size.');
            }
            throw new Error('Failed to update article');
        }
    },

    // Delete article
    deleteArticle: async (id: string): Promise<void> => {
        try {
            await api.delete(`/articles/${id}`);
        } catch (error) {
            console.error('Error deleting article:', error);
            throw new Error('Failed to delete article');
        }
    },

    // Get article by slug
    getArticleBySlug: async (slug: string): Promise<Article> => {
        try {
            const response = await api.get(`/articles/by-slug/${slug}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching article by slug:', error);
            throw new Error('Failed to fetch article');
        }
    },

    // Generate article
    generateArticle: async (domainId: string, topic?: string, language?: string): Promise<Article> => {
        try {
            const response = await api.post('/articles/generate', { domainId, topic, language });
            return response.data;
        } catch (error) {
            console.error('Error generating article:', error);
            throw new Error('Failed to generate article');
        }
    }
}; 