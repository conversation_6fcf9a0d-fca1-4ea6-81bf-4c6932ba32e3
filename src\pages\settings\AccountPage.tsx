import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import AccountSettings from '@/components/AccountSettings';
import { toast } from '@/components/ui/use-toast';

const AccountPage = () => {
    const navigate = useNavigate();
    const { logout } = useAuth();

    useEffect(() => {
        document.title = 'Account | BlogBuster';
    }, []);

    const handleLogout = async () => {
        try {
            await logout();
            navigate('/login');
        } catch (error) {
            console.error('Error logging out:', error);
            toast({
                title: 'Error',
                description: 'Failed to log out',
                variant: 'destructive',
            });
        }
    };

    const handleDeleteAccount = () => {
        // TODO: Implement account deletion
        toast({
            title: 'Coming Soon',
            description: 'Account deletion will be available soon',
        });
    };

    const handleManageSubscription = () => {
        // TODO: Implement subscription management
        toast({
            title: 'Coming Soon',
            description: 'Subscription management will be available soon',
        });
    };

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">Account Settings</h1>
            <AccountSettings
                onDeleteAccount={handleDeleteAccount}
            />
        </div>
    );
};

export default AccountPage; 