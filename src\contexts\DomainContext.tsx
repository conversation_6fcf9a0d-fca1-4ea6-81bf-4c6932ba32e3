import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useRef } from 'react';
import { Domain, domainService } from '@/services/domainService';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from './AuthContext';
import { useLocation } from 'react-router-dom';

interface DomainContextType {
    domains: Domain[];
    currentDomain: Domain | null;
    isLoading: boolean;
    setCurrentDomain: (domain: Domain | null) => void;
    refreshDomains: () => Promise<void>;
    addDomain: (domain: Domain) => void;
    deleteDomain: (domainId: string) => Promise<void>;
    paymentSuccess: boolean;
    paymentPlanType: string | null;
    clearPaymentSuccess: () => void;
}

interface DomainProviderProps {
    children: ReactNode;
}

const DomainContext = createContext<DomainContextType | undefined>(undefined);

export const useDomain = () => {
    const context = useContext(DomainContext);
    if (context === undefined) {
        throw new Error('useDomain must be used within a DomainProvider');
    }
    return context;
};

export const DomainProvider: React.FC<DomainProviderProps> = ({ children }) => {
    const [domains, setDomains] = useState<Domain[]>([]);
    const [currentDomain, setCurrentDomainState] = useState<Domain | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const { user } = useAuth();
    const [paymentSuccess, setPaymentSuccess] = useState<boolean>(false);
    const [paymentPlanType, setPaymentPlanType] = useState<string | null>(null);
    const location = useLocation();

    // Ref to track if a refresh is already in progress
    const isRefreshing = useRef(false);
    // Ref to track the last refresh time
    const lastRefreshTime = useRef(0);
    // Ref to store the current domain ID before refresh
    const currentDomainIdRef = useRef<string | null>(null);
    // Flag to track initial load
    const initialLoadComplete = useRef(false);

    // Update the ref whenever currentDomain changes
    useEffect(() => {
        if (currentDomain) {
            currentDomainIdRef.current = currentDomain._id;
        }
    }, [currentDomain]);

    // Load selected domain from localStorage on initial page load
    useEffect(() => {
        if (user && !initialLoadComplete.current) {
            const storedDomainId = localStorage.getItem('currentDomainId');
            if (storedDomainId) {
                currentDomainIdRef.current = storedDomainId;
                console.log('Loaded domain ID from localStorage:', storedDomainId);
            }
            initialLoadComplete.current = true;
        }
    }, [user]);

    const refreshDomains = useCallback(async () => {
        // Don't refresh if already in progress
        if (isRefreshing.current) {
            return;
        }

        // Check if we've refreshed recently (within the last 5 seconds)
        const now = Date.now();
        if (now - lastRefreshTime.current < 5000) {
            return;
        }

        try {
            isRefreshing.current = true;
            setIsLoading(true);
            lastRefreshTime.current = now;

            // Save the current domain ID before fetching new domains
            const activeDomainId = currentDomainIdRef.current;

            const fetchedDomains = await domainService.getDomains();

            // Sort domains by creation date (newest first)
            const sortedDomains = [...fetchedDomains].sort((a, b) => {
                const dateA = new Date(a.createdAt || 0);
                const dateB = new Date(b.createdAt || 0);
                return dateB.getTime() - dateA.getTime();
            });

            setDomains(sortedDomains);

            // If we have domains
            if (sortedDomains.length > 0) {
                let domainToSelect: Domain | null = null;

                // First priority: use the active domain ID (from the current session)
                if (activeDomainId) {
                    domainToSelect = sortedDomains.find(d => d._id === activeDomainId) || null;
                }

                // Second priority: use the stored domain ID from localStorage
                if (!domainToSelect) {
                    const storedDomainId = localStorage.getItem('currentDomainId');
                    if (storedDomainId) {
                        domainToSelect = sortedDomains.find(d => d._id === storedDomainId) || null;
                    }
                }

                // Last resort: use the most recent domain
                if (!domainToSelect) {
                    domainToSelect = sortedDomains[0];
                }

                // Set the selected domain
                if (domainToSelect) {
                    setCurrentDomainState(domainToSelect);
                    localStorage.setItem('currentDomainId', domainToSelect._id);
                    currentDomainIdRef.current = domainToSelect._id;
                }
            } else {
                // No domains available
                setCurrentDomainState(null);
                localStorage.removeItem('currentDomainId');
                currentDomainIdRef.current = null;
            }
        } catch (error) {
            console.error('Error fetching domains:', error);
            toast({
                title: 'Error',
                description: 'Failed to fetch domains',
                variant: 'destructive',
            });
        } finally {
            setIsLoading(false);
            isRefreshing.current = false;
        }
    }, []); // Remove currentDomainIdRef from dependencies since it's a ref

    const setCurrentDomain = (domain: Domain | null) => {
        console.log('Setting current domain:', domain ? `${domain.name} (${domain._id})` : 'null');
        setCurrentDomainState(domain);
        if (domain) {
            localStorage.setItem('currentDomainId', domain._id);
            currentDomainIdRef.current = domain._id;
        } else {
            localStorage.removeItem('currentDomainId');
            currentDomainIdRef.current = null;
        }
    };

    const addDomain = (domain: Domain) => {
        // Add the new domain to the beginning of the list
        setDomains(prev => [domain, ...prev]);
        // Always set new domain as current domain
        setCurrentDomain(domain);
    };

    const deleteDomain = async (domainId: string) => {
        try {
            // Update the local state immediately for better UX
            const remainingDomains = domains.filter(d => d._id !== domainId);
            setDomains(remainingDomains);

            // Update current domain if needed
            if (currentDomain?._id === domainId) {
                // Select the most recent remaining domain
                const newCurrentDomain = remainingDomains.length > 0 ? remainingDomains[0] : null;
                setCurrentDomain(newCurrentDomain);
            }

            // Delete the domain on the server
            await domainService.deleteDomainWithArticles(domainId);

            toast({
                title: 'Success',
                description: 'Domain and its articles deleted successfully',
            });
        } catch (error) {
            console.error('Error deleting domain:', error);

            // Revert the state changes if there was an error (unless it's a 404)
            if (!(error instanceof Error && error.message === 'Domain not found')) {
                // Restore the domains state
                const originalDomain = domains.find(d => d._id === domainId);
                if (originalDomain) {
                    setDomains([...domains]);
                    if (currentDomain?._id === domainId) {
                        setCurrentDomain(originalDomain);
                    }
                }
            }

            // Handle specific error cases
            let errorMessage = 'Failed to delete domain';
            if (error instanceof Error) {
                if (error.message.includes('articles')) {
                    errorMessage = 'Cannot delete domain with existing articles. Please delete all articles first.';
                } else {
                    errorMessage = error.message;
                }
            }

            toast({
                title: 'Error',
                description: errorMessage,
                variant: 'destructive',
            });
            throw error;
        }
    };

    // Initialize domains when user changes
    useEffect(() => {
        if (user) {
            // Preserve the current domain ID before clearing state
            const preservedDomainId = currentDomainIdRef.current;

            // Clear current state
            setDomains([]);
            setCurrentDomainState(null);

            // Restore the preserved domain ID
            if (preservedDomainId) {
                currentDomainIdRef.current = preservedDomainId;
            }

            // Fetch fresh data for new user
            refreshDomains();
        } else {
            // Clear state when user logs out
            setDomains([]);
            setCurrentDomainState(null);
            localStorage.removeItem('currentDomainId');
            currentDomainIdRef.current = null;
        }
    }, [user]);

    // Refresh domains periodically to keep them in sync, but much less frequently
    useEffect(() => {
        if (user) {
            // We're removing the automatic refresh interval to prevent unnecessary API calls
            // Users can manually refresh domains when needed using the UI

            // const interval = setInterval(() => {
            //     console.log('Running scheduled domain refresh');
            //     refreshDomains();
            // }, 300000); // 5 minutes = 300000 ms

            // return () => clearInterval(interval);
        }
    }, [user]); // Remove refreshDomains from dependencies

    useEffect(() => {
        if (location.search.includes('payment_success=true')) {
            const params = new URLSearchParams(location.search);
            const planType = params.get('plan_type');
            setPaymentSuccess(true);
            setPaymentPlanType(planType);
        }
    }, [location]);

    const clearPaymentSuccess = () => {
        setPaymentSuccess(false);
        setPaymentPlanType(null);
    };

    const value: DomainContextType = {
        domains,
        currentDomain,
        isLoading,
        setCurrentDomain,
        refreshDomains,
        addDomain,
        deleteDomain,
        paymentSuccess,
        paymentPlanType,
        clearPaymentSuccess,
    };

    return (
        <DomainContext.Provider value={value}>
            {children}
        </DomainContext.Provider>
    );
};
