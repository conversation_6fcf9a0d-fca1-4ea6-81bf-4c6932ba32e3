import axios from 'axios';

interface FontCategory {
    name: string;
    fonts: GoogleFont[];
}

export interface GoogleFont {
    family: string;
    category: string;
    variants: string[];
    preview: string;
}

const POPULAR_FONTS = [
    'Roboto',
    'Open Sans',
    'Lato',
    'Montserrat',
    'Poppins',
    'Inter',
    'Playfair Display',
    'Source Sans Pro',
    'Raleway',
    'Nunito',
    'Ubuntu',
    'Merriweather',
    'Noto Sans',
    'PT Sans',
    'Oswald'
];

const ALL_FONTS = [
    // Sans Serif
    { family: 'Roboto', category: 'sans-serif' },
    { family: 'Open Sans', category: 'sans-serif' },
    { family: 'Lato', category: 'sans-serif' },
    { family: 'Montserrat', category: 'sans-serif' },
    { family: 'Poppins', category: 'sans-serif' },
    { family: 'Inter', category: 'sans-serif' },
    { family: 'Source Sans Pro', category: 'sans-serif' },
    { family: '<PERSON><PERSON><PERSON>', category: 'sans-serif' },
    { family: '<PERSON>uni<PERSON>', category: 'sans-serif' },
    { family: 'Ubuntu', category: 'sans-serif' },
    { family: 'Noto Sans', category: 'sans-serif' },
    { family: 'PT Sans', category: 'sans-serif' },
    { family: 'Work Sans', category: 'sans-serif' },
    { family: 'Mulish', category: 'sans-serif' },
    { family: 'Quicksand', category: 'sans-serif' },
    { family: 'DM Sans', category: 'sans-serif' },
    { family: 'Manrope', category: 'sans-serif' },
    { family: 'Space Grotesk', category: 'sans-serif' },

    // Serif
    { family: 'Playfair Display', category: 'serif' },
    { family: 'Merriweather', category: 'serif' },
    { family: 'Lora', category: 'serif' },
    { family: 'PT Serif', category: 'serif' },
    { family: 'Source Serif Pro', category: 'serif' },
    { family: 'Crimson Text', category: 'serif' },
    { family: 'Libre Baskerville', category: 'serif' },
    { family: 'Noto Serif', category: 'serif' },
    { family: 'Cormorant Garamond', category: 'serif' },
    { family: 'Spectral', category: 'serif' },
    { family: 'Fraunces', category: 'serif' },
    { family: 'Newsreader', category: 'serif' },

    // Display
    { family: 'Oswald', category: 'display' },
    { family: 'Bebas Neue', category: 'display' },
    { family: 'Abril Fatface', category: 'display' },
    { family: 'Righteous', category: 'display' },
    { family: 'Pacifico', category: 'display' },
    { family: 'Comfortaa', category: 'display' },
    { family: 'Josefin Sans', category: 'display' },
    { family: 'Lexend', category: 'display' },
    { family: 'Outfit', category: 'display' },
    { family: 'Sora', category: 'display' },

    // Monospace
    { family: 'Roboto Mono', category: 'monospace' },
    { family: 'Source Code Pro', category: 'monospace' },
    { family: 'Fira Code', category: 'monospace' },
    { family: 'JetBrains Mono', category: 'monospace' },
    { family: 'Space Mono', category: 'monospace' },
    { family: 'IBM Plex Mono', category: 'monospace' },
    { family: 'Inconsolata', category: 'monospace' },

    // Handwriting
    { family: 'Dancing Script', category: 'handwriting' },
    { family: 'Caveat', category: 'handwriting' },
    { family: 'Permanent Marker', category: 'handwriting' },
    { family: 'Satisfy', category: 'handwriting' },
    { family: 'Great Vibes', category: 'handwriting' },
    { family: 'Sacramento', category: 'handwriting' },
    { family: 'Kalam', category: 'handwriting' },
    { family: 'Indie Flower', category: 'handwriting' }
].map(font => ({
    ...font,
    variants: ['400', '700'],
    preview: 'The quick brown fox jumps over the lazy dog'
}));

class FontService {
    private static instance: FontService;
    private loadedFonts: Set<string> = new Set();

    private constructor() { }

    public static getInstance(): FontService {
        if (!FontService.instance) {
            FontService.instance = new FontService();
        }
        return FontService.instance;
    }

    async getFonts(): Promise<GoogleFont[]> {
        return ALL_FONTS;
    }

    getPopularFonts(): GoogleFont[] {
        return ALL_FONTS.filter(font =>
            POPULAR_FONTS.includes(font.family)
        );
    }

    getCategorizedFonts(): Record<string, GoogleFont[]> {
        return ALL_FONTS.reduce((acc, font) => {
            if (!acc[font.category]) {
                acc[font.category] = [];
            }
            acc[font.category].push(font);
            return acc;
        }, {} as Record<string, GoogleFont[]>);
    }

    async loadFont(fontFamily: string): Promise<void> {
        if (this.loadedFonts.has(fontFamily)) {
            return;
        }

        const link = document.createElement('link');
        link.href = `https://fonts.googleapis.com/css2?family=${fontFamily.replace(/\s+/g, '+')}:wght@400;700&display=swap`;
        link.rel = 'stylesheet';
        document.head.appendChild(link);
        this.loadedFonts.add(fontFamily);
    }

    preloadPopularFonts(): void {
        POPULAR_FONTS.forEach(font => this.loadFont(font));
    }
}

export const fontService = FontService.getInstance(); 