import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Article } from '../../types/Article';
import { articleService } from '../../services/articleService';
import { domainService, Domain } from '../../services/domainService';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { format } from 'date-fns';
import { useDomain } from '@/contexts/DomainContext';
import { userService, User } from '@/services/userService';
import { fontService } from '@/services/fontService';

interface BlogPreviewPageProps { }

// Helper function to convert HTML to plain text
const stripHtml = (html: string) => {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
};

const BlogPreviewPage: React.FC<BlogPreviewPageProps> = () => {
    const { blogUrl } = useParams<{ blogUrl: string }>();
    const [articles, setArticles] = useState<Article[]>([]);
    const [domain, setDomain] = useState<Domain | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const { currentDomain } = useDomain();
    const brandColor = currentDomain?.designSettings?.colors?.brand || '#3c484c';
    const fontColor = currentDomain?.designSettings?.colors?.accent || '#cec4c0';
    const fontFamily = currentDomain?.designSettings?.font || 'Roboto';
    const [userCache, setUserCache] = useState<Record<string, User>>({});

    // Set document title when domain is loaded
    useEffect(() => {
        if (domain?.name) {
            document.title = `${domain.name} | Blog`;
        } else {
            document.title = 'Blog';
        }
    }, [domain]);

    const fetchUserData = async (userId: string) => {
        if (userCache[userId]) return;
        try {
            const userData = await userService.getUserById(userId);
            setUserCache(prev => ({
                ...prev,
                [userId]: userData
            }));
        } catch (error) {
            console.error('Error fetching user data:', error);
        }
    };

    const getUserData = (userId: string) => {
        if (!userId) return null;
        return userCache[userId];
    };

    useEffect(() => {
        const fetchData = async () => {
            if (!blogUrl) return;

            setLoading(true);
            setError(null);
            try {
                const domainData = await domainService.getDomainByUrl(blogUrl);
                setDomain(domainData);

                const articlesData = await articleService.getArticles(domainData._id);
                setArticles(articlesData);

                // Fetch user data for all articles that have a userId
                articlesData.forEach(article => {
                    if (article.userId) {
                        fetchUserData(article.userId);
                    }
                });
            } catch (err) {
                console.error('Error fetching data:', err);
                setError(err instanceof Error ? err.message : 'Failed to load blog data');
                setDomain(null);
                setArticles([]);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [blogUrl]);

    useEffect(() => {
        if (fontFamily) {
            fontService.loadFont(fontFamily).catch(error => {
                console.error('Error loading font:', error);
            });
        }
    }, [fontFamily]);

    const handleArticleClick = (article: Article) => {
        window.location.href = `/articles/${article.urlSlug || article._id}`;
    };

    if (loading) return (
        <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Blog...</h2>
                <p className="text-gray-600">Please wait while we fetch the content.</p>
            </div>
        </div>
    );

    if (error) return (
        <div className="flex items-center justify-center min-h-screen">
            <div className="text-center max-w-lg mx-auto px-4">
                <h2 className="text-2xl font-semibold text-gray-800 mb-2">Blog Not Found</h2>
                <p className="text-gray-600 mb-4">{error}</p>
                <p className="text-gray-500 text-sm">
                    Please check if the URL is correct and try again. If the problem persists,
                    contact the blog administrator.
                </p>
            </div>
        </div>
    );

    const filteredArticles = articles.filter(article =>
        article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        article.excerpt?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const getTagColor = (tag: string) => {
        const tagColors: { [key: string]: string } = {
            'Startup Strategies': 'bg-purple-600',
            'Legal Compliance': 'bg-blue-600',
            'Marketing': 'bg-green-600',
            'Technology': 'bg-orange-600'
        };
        return tagColors[tag] || 'bg-gray-600';
    };

    const renderListArticle = (article: Article) => {
        const userData = getUserData(article.userId);
        return (
            <article
                key={article._id}
                className="bg-white rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
                style={{ fontFamily }}
            >
                <div className="flex gap-10">
                    {/* Article Image - Moved to start */}
                    <div className="flex-shrink-0">
                        <div className="w-[280px] h-[200px] rounded-lg overflow-hidden">
                            {(() => {
                                const imageUrl = article.image?.url || article.image?.thumb || article.video?.thumbnail;
                                return imageUrl ? (
                                    <img
                                        src={imageUrl}
                                        alt={article.title}
                                        className="w-full h-full object-cover"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            if (target.src === article.image?.thumb && article.image?.url) {
                                                target.src = article.image.url;
                                            } else {
                                                target.src = '/article-placeholder.svg';
                                            }
                                        }}
                                    />
                                ) : (
                                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                                        <img
                                            src="/article-placeholder.svg"
                                            alt="Placeholder"
                                            className="w-16 h-16 opacity-50"
                                        />
                                    </div>
                                );
                            })()}
                        </div>
                    </div>

                    <div className="flex flex-col flex-grow">
                        {/* Date and read time */}
                        <div className="flex items-center space-x-1 text-sm mb-4" style={{ color: fontColor }}>
                            <span>{format(new Date(article.createdAt || article.date), 'MMM d, yyyy')}</span>
                            <span>·</span>
                            <span>{Math.ceil((article.content?.length || 0) / 1000)} min read</span>
                        </div>

                        {/* Title */}
                        <h2
                            onClick={() => handleArticleClick(article)}
                            className="text-2xl font-bold mb-3 cursor-pointer line-clamp-2 transition-colors duration-200"
                            style={{
                                color: '#1a1a1a',
                                ['--hover-color' as string]: brandColor,
                                fontFamily
                            }}
                            onMouseEnter={(e) => {
                                (e.target as HTMLElement).style.color = brandColor;
                            }}
                            onMouseLeave={(e) => {
                                (e.target as HTMLElement).style.color = '#1a1a1a';
                            }}
                        >
                            {article.title}
                        </h2>

                        {/* Description */}
                        <p className="mb-4 line-clamp-2 text-base" style={{ color: fontColor, fontFamily }}>
                            {stripHtml(article.excerpt || article.description || '')}
                        </p>

                        {/* Author info */}
                        <div className="flex items-center space-x-3 mt-auto">
                            <div className="flex-shrink-0">
                                <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden">
                                    {userData?.avatar ? (
                                        <img
                                            src={userData.avatar}
                                            alt={`${userData.firstName} ${userData.lastName}`}
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <div className="w-full h-full flex items-center justify-center text-gray-500 font-medium">
                                            {userData ? userData.firstName.charAt(0) : 'A'}
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="flex flex-col">
                                <span className="text-sm font-medium" style={{ color: fontColor, fontFamily }}>
                                    {userData ? `${userData.firstName} ${userData.lastName}` : 'Anonymous'}
                                </span>
                                <span className="text-sm" style={{ color: fontColor, fontFamily }}>
                                    {userData ? 'Author' : 'Technical Writer'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        );
    };

    const renderGridArticle = (article: Article) => {
        const userData = getUserData(article.userId);
        return (
            <article
                key={article._id}
                className="bg-white rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200"
                style={{ fontFamily }}
            >
                {/* Image container */}
                <div className="relative w-full h-48">
                    {(() => {
                        const imageUrl = article.image?.url || article.image?.thumb || article.video?.thumbnail;
                        return imageUrl ? (
                            <img
                                src={imageUrl}
                                alt={article.title}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    if (target.src === article.image?.thumb && article.image?.url) {
                                        target.src = article.image.url;
                                    } else {
                                        target.src = '/article-placeholder.svg';
                                    }
                                }}
                            />
                        ) : (
                            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                                <img
                                    src="/article-placeholder.svg"
                                    alt="Placeholder"
                                    className="w-16 h-16 opacity-50"
                                />
                            </div>
                        );
                    })()}
                </div>

                {/* Content container */}
                <div className="p-6">
                    {/* Date and read time */}
                    <div className="flex items-center space-x-1 text-sm mb-3" style={{ color: fontColor, fontFamily }}>
                        <span>{format(new Date(article.createdAt || article.date), 'MMM d, yyyy')}</span>
                        <span>·</span>
                        <span>{Math.ceil((article.content?.length || 0) / 1000)} min read</span>
                    </div>

                    {/* Title */}
                    <h2
                        onClick={() => handleArticleClick(article)}
                        className="text-xl font-bold mb-3 cursor-pointer line-clamp-2 transition-colors duration-200"
                        style={{
                            color: '#1a1a1a',
                            ['--hover-color' as string]: brandColor,
                            fontFamily
                        }}
                        onMouseEnter={(e) => {
                            (e.target as HTMLElement).style.color = brandColor;
                        }}
                        onMouseLeave={(e) => {
                            (e.target as HTMLElement).style.color = '#1a1a1a';
                        }}
                    >
                        {article.title}
                    </h2>

                    {/* Description */}
                    <p className="mb-4 line-clamp-2" style={{ color: fontColor, fontFamily }}>
                        {stripHtml(article.excerpt || article.description || '')}
                    </p>

                    {/* Author info */}
                    <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 rounded-full bg-gray-200 overflow-hidden">
                                {userData?.avatar ? (
                                    <img
                                        src={userData.avatar}
                                        alt={`${userData.firstName} ${userData.lastName}`}
                                        className="w-full h-full object-cover"
                                    />
                                ) : (
                                    <div className="w-full h-full flex items-center justify-center text-gray-500 font-medium">
                                        {userData ? userData.firstName.charAt(0) : 'A'}
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="flex flex-col">
                            <span className="text-sm font-medium" style={{ color: fontColor, fontFamily }}>
                                {userData ? `${userData.firstName} ${userData.lastName}` : 'Anonymous'}
                            </span>
                            <span className="text-xs" style={{ color: fontColor, fontFamily }}>
                                {userData ? 'Author' : 'Technical Writer'}
                            </span>
                        </div>
                    </div>
                </div>
            </article>
        );
    };

    // Get layout settings from domain
    const isListView = domain?.designSettings?.layout?.listEnabled || false;
    const gridType = domain?.designSettings?.layout?.grid || 'grid-3';

    // Determine number of columns based on grid type
    const getGridColumns = () => {
        switch (gridType) {
            case 'grid-4':
                return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
            case 'grid-3':
                return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
            case 'grid-2':
                return 'grid-cols-1 sm:grid-cols-2';
            default:
                return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
        }
    };

    return (
        <div className="min-h-screen flex flex-col">
            {/* Header */}
            <header className="py-4" style={{ backgroundColor: 'rgba(222, 222, 222, 0.25)' }}>
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between">
                        {/* Logo Section */}
                        <div className="h-16">
                            {domain?.designSettings?.logo ? (
                                <img
                                    src={domain.designSettings.logo}
                                    alt={`${domain.name} logo`}
                                    className="h-full object-contain"
                                />
                            ) : (
                                <div className="h-full flex items-center">
                                    <span className="text-xl font-bold" style={{ fontFamily }}>
                                        {domain?.name || 'Blog'}
                                    </span>
                                </div>
                            )}
                        </div>

                        {/* Navigation Links */}
                        <div className="flex items-center space-x-6">
                            {/* Show header links from navigation settings */}
                            {domain?.navigationSettings?.headerLinks &&
                                domain.navigationSettings.headerLinks.length > 0 ? (
                                domain.navigationSettings.headerLinks
                                    .filter(link => link.text && link.url && link.text.trim() !== '' && link.url.trim() !== '')
                                    .map((link) => {
                                        // Validate URL format
                                        let url = link.url;
                                        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
                                            url = `https://${url}`;
                                        }

                                        return (
                                            <a
                                                key={link.id}
                                                href={url}
                                                className="text-gray-600 hover:text-gray-900 transition-colors"
                                                style={{ fontFamily }}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                {link.text}
                                            </a>
                                        );
                                    })
                            ) : (
                                // Fallback to default links if no custom links are set
                                <>
                                    <a
                                        href="#latest"
                                        className="text-gray-600 hover:text-gray-900 transition-colors"
                                        style={{ fontFamily }}
                                    >
                                        Latest
                                    </a>
                                    <a
                                        href="#categories"
                                        className="text-gray-600 hover:text-gray-900 transition-colors"
                                        style={{ fontFamily }}
                                    >
                                        Categories
                                    </a>
                                    <a
                                        href="#about"
                                        className="text-gray-600 hover:text-gray-900 transition-colors"
                                        style={{ fontFamily }}
                                    >
                                        About
                                    </a>
                                </>
                            )}
                        </div>

                        {/* CTA Button */}
                        {domain?.navigationSettings?.ctaButtonDisabled === false &&
                            domain.navigationSettings.ctaButtonText &&
                            domain.navigationSettings.ctaButtonUrl && (
                                <a
                                    href={domain.navigationSettings.ctaButtonUrl.startsWith('http')
                                        ? domain.navigationSettings.ctaButtonUrl
                                        : `https://${domain.navigationSettings.ctaButtonUrl}`}
                                    className="px-4 py-2 rounded-md text-white transition-colors"
                                    style={{
                                        backgroundColor: brandColor,
                                        fontFamily
                                    }}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    {domain.navigationSettings.ctaButtonText}
                                </a>
                            )}
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="flex-grow bg-gray-50/30">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="mb-8">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                            <Input
                                type="text"
                                placeholder="Search articles..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-10 w-full max-w-md"
                            />
                        </div>
                    </div>

                    {isListView ? (
                        <div className="space-y-6">
                            {filteredArticles.map(renderListArticle)}
                        </div>
                    ) : (
                        <div className={`grid ${getGridColumns()} gap-6`}>
                            {filteredArticles.map(renderGridArticle)}
                        </div>
                    )}
                </div>
            </main>

            {/* Footer */}
            <footer className="bg-gray-900 py-4">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Footer Links */}
                    {domain?.navigationSettings?.footerLinks &&
                        domain.navigationSettings.footerLinks.length > 0 && (
                            <div className="flex justify-center space-x-8 mb-4">
                                {domain.navigationSettings.footerLinks
                                    .filter(link => link.text && link.url && link.text.trim() !== '' && link.url.trim() !== '')
                                    .map((link) => {
                                        // Validate URL format
                                        let url = link.url;
                                        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
                                            url = `https://${url}`;
                                        }

                                        return (
                                            <a
                                                key={link.id}
                                                href={url}
                                                className="text-gray-400 hover:text-white transition-colors"
                                                style={{ fontFamily }}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                {link.text}
                                            </a>
                                        );
                                    })
                                }
                            </div>
                        )}
                    <div className="text-center text-gray-400">
                        <p>© {new Date().getFullYear()} {domain?.name || 'Blog'}. All rights reserved.</p>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default BlogPreviewPage; 