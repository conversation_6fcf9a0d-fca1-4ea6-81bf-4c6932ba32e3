import apiClient from '@/lib/api-client';

export interface BrandInfo {
    name: string;
    description: string;
    targetAudience: string;
    logo: string;
    audienceLocation: string;
    benefits: string[];
    toneOfVoice: string;
    industry: string;
    tags: string[];
}

export interface ArticleSettings {
    useBrandInfo: boolean;
    articleLength: 'short' | 'medium' | 'long';
    language: string;
    specificInstructions?: string;
    exclusions?: string;
}

export interface Domain {
    _id: string;
    name: string;
    url: string;
    userId: string;
    createdAt?: string;
    updatedAt?: string;
    hostingSettings?: {
        domain: string;
        subdomain: string;
        isVerified: boolean;
    };
    designSettings?: {
        logo?: string;
        articleTheme?: string;
        layout?: {
            grid?: string;
            listEnabled?: boolean;
        };
        colors?: {
            brand: string;
            accent: string;
        };
        font?: string;
    };
    navigationSettings?: {
        homeButtonEnabled: boolean;
        ctaButtonDisabled: boolean;
        ctaButtonText?: string;
        ctaButtonUrl?: string;
        headerLinks: Array<{ id: string; text: string; url: string }>;
        footerLinks: Array<{ id: string; text: string; url: string }>;
    };
    title?: string;
    description?: string;
    brandInfo?: BrandInfo;
    articleSettings?: ArticleSettings;
    isDefault?: boolean;
}

export interface CreateDomainRequest {
    name: string;
    url: string;
    title?: string;
    description?: string;
    brandInfo?: BrandInfo;
    articleSettings?: ArticleSettings;
}

export interface HostingSettings {
    domain: string;
    subdomain: string;
    isVerified: boolean;
    cnameRecords: {
        one: {
            host: string;
            value: string;
        };
        two: {
            host: string;
            value: string;
        };
    };
}

export interface DesignSettings {
    logo?: string;
    articleTheme: string;
    layout: {
        grid: string;
        listEnabled: boolean;
    };
    colors: {
        brand: string;
        accent: string;
    };
    font: string;
}

export interface NavigationSettings {
    homeButtonEnabled: boolean;
    ctaButtonDisabled: boolean;
    ctaButtonText?: string;
    ctaButtonUrl?: string;
    headerLinks: Array<{ id: string; text: string; url: string }>;
    footerLinks: Array<{ id: string; text: string; url: string }>;
}

class DomainService {
    private baseUrl = '/domains';

    async getDomains(): Promise<Domain[]> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/manage`);
            return response.data;
        } catch (error) {
            console.error('Error fetching domains:', error);
            throw error;
        }
    }

    async getDomainById(id: string): Promise<Domain> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/public/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching domain:', error);
            throw error;
        }
    }

    async getDomainByUrl(url: string): Promise<Domain> {
        try {
            if (!url) {
                throw new Error('URL is required');
            }

            // Remove any protocol and www
            const cleanUrl = url.replace(/^https?:\/\//, '').replace(/^www\./, '');

            const response = await apiClient.get(`${this.baseUrl}/by-url/${cleanUrl}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching domain by URL:', error);
            if (error.response?.status === 404) {
                throw new Error(`Domain not found for URL: ${url}`);
            }
            throw new Error(`Failed to fetch domain: ${error.message}`);
        }
    }

    async createDomain(domain: CreateDomainRequest): Promise<Domain> {
        try {
            const response = await apiClient.post(`${this.baseUrl}/manage`, domain);
            return response.data;
        } catch (error) {
            console.error('Error creating domain:', error);
            if (error.response?.data?.message) {
                throw new Error(error.response.data.message);
            }
            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            }
            throw error;
        }
    }

    async updateDomain(id: string, domain: Partial<Domain>): Promise<Domain> {
        try {
            const response = await apiClient.put(`${this.baseUrl}/manage/${id}`, domain);
            return response.data;
        } catch (error) {
            console.error('Error updating domain:', error);
            throw error;
        }
    }

    async updateDomainBrandInfo(id: string, brandInfo: BrandInfo): Promise<Domain> {
        try {
            const response = await apiClient.put(`${this.baseUrl}/manage/${id}/brand-info`, { brandInfo });
            return response.data;
        } catch (error) {
            console.error('Error updating domain brand info:', error);
            throw error;
        }
    }

    async updateDomainArticleSettings(id: string, articleSettings: ArticleSettings): Promise<Domain> {
        try {
            const response = await apiClient.put(`${this.baseUrl}/manage/${id}/article-settings`, { articleSettings });
            return response.data;
        } catch (error) {
            console.error('Error updating domain article settings:', error);
            throw error;
        }
    }

    async deleteDomain(id: string): Promise<void> {
        try {
            await apiClient.delete(`${this.baseUrl}/manage/${id}`);
        } catch (error) {
            console.error('Error deleting domain:', error);
            throw error;
        }
    }

    async getArticlesByDomain(id: string): Promise<any[]> {
        try {
            const response = await apiClient.get(`/articles?domainId=${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching articles for domain:', error);
            throw error;
        }
    }

    async setDefaultDomain(id: string): Promise<Domain> {
        try {
            const response = await apiClient.put(`${this.baseUrl}/${id}/set-default`);
            return response.data;
        } catch (error) {
            console.error('Error setting default domain:', error);
            throw error;
        }
    }

    async deleteAllArticlesInDomain(id: string): Promise<void> {
        try {
            await apiClient.delete(`${this.baseUrl}/${id}/articles`);
        } catch (error) {
            console.error('Error deleting articles:', error);
            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            }
            throw error;
        }
    }

    async deleteDomainWithArticles(id: string): Promise<void> {
        try {
                        // First check if the domain exists using the same manage endpoint we later call for deletion
            const response = await apiClient.get(`${this.baseUrl}/manage/${id}`);
            if (!response.data) {
                throw new Error('Domain not found');
            }

            try {
                // First try to delete all articles
                // Try to delete all articles using manage endpoint
            try {
                await apiClient.delete(`${this.baseUrl}/manage/${id}/articles`);
            } catch (error) {
                console.warn('No articles to delete or error deleting articles:', error);
            }
            } catch (error) {
                console.warn('No articles to delete or error deleting articles:', error);
                // Continue with domain deletion even if article deletion fails
            }

            // Then delete the domain itself
            await this.deleteDomain(id);
        } catch (error) {
            console.error('Error in deleteDomainWithArticles:', error);
            if (error.response?.status === 404) {
                throw new Error('Domain not found');
            }
            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            }
            throw error;
        }
    }

    async updateHostingSettings(domainId: string, settings: { domain: string; subdomain: string }): Promise<any> {
        try {
            const response = await apiClient.put(`${this.baseUrl}/manage/${domainId}/hosting`, settings);
            return response.data;
        } catch (error) {
            console.error('Error updating hosting settings:', error);
            throw error;
        }
    }

    async verifyHosting(domainId: string): Promise<any> {
        try {
            const response = await apiClient.put(`${this.baseUrl}/manage/${domainId}/hosting/verify`);
            return response.data;
        } catch (error) {
            console.error('Error verifying hosting:', error);
            throw error;
        }
    }

    async updateDesignSettings(domainId: string, settings: Partial<DesignSettings>): Promise<Domain> {
        try {
            const response = await apiClient.put(`${this.baseUrl}/manage/${domainId}/design`, settings);
            return response.data;
        } catch (error) {
            console.error('Error updating design settings:', error);
            throw error;
        }
    }

    async updateNavigationSettings(domainId: string, settings: Partial<NavigationSettings>): Promise<Domain> {
        try {
            // Ensure the settings object has all required properties with proper defaults
            const validatedSettings: NavigationSettings = {
                homeButtonEnabled: settings.homeButtonEnabled !== undefined ? settings.homeButtonEnabled : false,
                ctaButtonDisabled: settings.ctaButtonDisabled !== undefined ? settings.ctaButtonDisabled : true, // Default to true (disabled)
                ctaButtonText: settings.ctaButtonText || '',
                ctaButtonUrl: settings.ctaButtonUrl || '',
                headerLinks: Array.isArray(settings.headerLinks) ? settings.headerLinks : [],
                footerLinks: Array.isArray(settings.footerLinks) ? settings.footerLinks : []
            };

            const response = await apiClient.put(`${this.baseUrl}/manage/${domainId}/navigation`, validatedSettings);
            return response.data;
        } catch (error) {
            console.error('Error updating navigation settings:', error);
            throw error;
        }
    }
}

export const domainService = new DomainService();
