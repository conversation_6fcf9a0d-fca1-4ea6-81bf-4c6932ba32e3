[{"_id": {"$oid": "687f262fb70fd106deb5e16e"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1GuRYs6KXUss9AB2ye6c3nStMxqa8N52yUR9NPAYqIKMbdFIeOxM57pgv", "stripeSubscriptionId": null, "invoiceUrl": null, "transactionType": "New Subscription", "amount": 9.99, "currency": "usd", "planType": "Daily", "status": "failed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1GuRYs6KXUss9AB2ye6c3nStMxqa8N52yUR9NPAYqIKMbdFIeOxM57pgv#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-22T05:48:31.557Z"}, "invoiceFound": false, "hasSubscriptionId": false, "verifiedAt": {"$date": "2025-07-22T05:48:42.341Z"}, "stripeStatus": "open", "stripePaymentStatus": "unpaid", "paymentIntent": null, "chargeId": null}, "createdAt": {"$date": "2025-07-22T05:48:31.558Z"}, "updatedAt": {"$date": "2025-07-22T05:48:42.342Z"}, "__v": 0}, {"_id": {"$oid": "687f2644b70fd106deb5e1a6"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1JEusbAiQaFcmv0htPzKJabVrr2sdgitiUG7yHbsLbIQGqvuBdY7X0utn", "stripeSubscriptionId": "sub_1RnZ4VCeRpXjPE6nGxn565hP", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajE2OUtKRkJobFV0eEpmc1J3SzA2TmJ1TDRYZUI5LDE0MzcwNDE0Nw0200ITiFkRzh?s=ap", "transactionType": "New Subscription", "amount": 59.99, "currency": "usd", "planType": "Monthly", "status": "completed", "paymentMethod": "card", "metadata": {"completedAt": {"$date": "2025-07-22T05:49:07.486Z"}, "stripeStatus": "complete", "stripePaymentStatus": "paid", "stripeInvoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajE2OUtKRkJobFV0eEpmc1J3SzA2TmJ1TDRYZUI5LDE0MzcwNDE0Nw0200ITiFkRzh?s=ap", "invoiceId": "in_1RnZ4TCeRpXjPE6niEVyi6pI", "invoiceUpdatedAt": {"$date": "2025-07-22T05:49:07.911Z"}}, "createdAt": {"$date": "2025-07-22T05:48:52.223Z"}, "updatedAt": {"$date": "2025-07-22T05:48:52.224Z"}, "__v": 0, "stripeCustomerId": "cus_Sj16cJ731t5Z3n", "stripePaymentIntentId": null}, {"_id": {"$oid": "687f26d92d3547615d15fa3c"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1c6TMy1rZX6NpioDUU7VOs72qhxTofvswyTHgbkDSsvhytn7KTtNdXmFJ", "stripeSubscriptionId": "sub_1RnZ4VCeRpXjPE6nGxn565hP", "transactionType": "Plan Change", "amount": 159.99, "currency": "usd", "planType": "Yearly", "status": "failed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1c6TMy1rZX6NpioDUU7VOs72qhxTofvswyTHgbkDSsvhytn7KTtNdXmFJ#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-22T05:51:21.346Z"}, "isProrated": true, "previousPlanType": "Monthly", "originalAmount": "159.99", "discount": "0.00", "finalAmount": "159.99", "unusedCredit": "0.00", "remainingDays": 0, "totalDays": 30, "currentPlanPrice": "59.99", "newPlanPrice": "159.99", "verifiedAt": {"$date": "2025-07-22T05:58:06.436Z"}, "stripeStatus": "open", "stripePaymentStatus": "unpaid", "paymentIntent": null, "chargeId": null}, "createdAt": {"$date": "2025-07-22T05:51:21.350Z"}, "updatedAt": {"$date": "2025-07-22T05:58:06.437Z"}, "__v": 0}, {"_id": {"$oid": "687f279a9d3fd1853fb1ce91"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1illuhGHBtIDBUIZmxUgJYc9lha1qE3Cx62GIBaC2alq5F1WCEN5SI9Ro", "stripeSubscriptionId": "sub_1RnZ4VCeRpXjPE6nGxn565hP", "transactionType": "Plan Change", "amount": 159.99, "currency": "usd", "planType": "Yearly", "status": "failed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1illuhGHBtIDBUIZmxUgJYc9lha1qE3Cx62GIBaC2alq5F1WCEN5SI9Ro#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-22T05:54:34.583Z"}, "isProrated": true, "previousPlanType": "Monthly", "originalAmount": "159.99", "discount": "0.00", "finalAmount": "159.99", "unusedCredit": "0.00", "remainingDays": 0, "totalDays": 30, "currentPlanPrice": "59.99", "newPlanPrice": "159.99", "verifiedAt": {"$date": "2025-07-22T05:58:06.445Z"}, "stripeStatus": "open", "stripePaymentStatus": "unpaid", "paymentIntent": null, "chargeId": null}, "createdAt": {"$date": "2025-07-22T05:54:34.584Z"}, "updatedAt": {"$date": "2025-07-22T05:58:06.445Z"}, "__v": 0}, {"_id": {"$oid": "687f2a8c946ff210876314f2"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1XzjHNyxlgbZ2FBEZE2nlk1s7roe7KYBl8zqGvfqyxCuIncmvM154r5Jz", "stripeSubscriptionId": "sub_1RnZ4VCeRpXjPE6nGxn565hP", "transactionType": "Plan Change", "amount": 101.93516129032258, "currency": "usd", "planType": "Yearly", "status": "completed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1XzjHNyxlgbZ2FBEZE2nlk1s7roe7KYBl8zqGvfqyxCuIncmvM154r5Jz#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-22T06:07:08.167Z"}, "isProrated": true, "previousPlanType": "Monthly", "originalAmount": "159.99", "discount": "58.05", "finalAmount": "101.94", "unusedCredit": "58.05", "remainingDays": 30, "totalDays": 31, "currentPlanPrice": "59.99", "newPlanPrice": "159.99", "invoiceFound": true, "invoiceUpdatedAt": {"$date": "2025-07-22T06:07:47.128Z"}}, "createdAt": {"$date": "2025-07-22T06:07:08.169Z"}, "updatedAt": {"$date": "2025-07-22T06:07:47.129Z"}, "__v": 0, "stripePaymentIntentId": "pi_3RnZMJCeRpXjPE6n02ZyfQc8", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajFQTUdLbVhoTHZhbkc5T05xeElKZ1VCcTlranpqLDE0MzcwNTI2Nw0200SXtq1cwT?s=ap"}, {"_id": {"$oid": "687f2bf2946ff210876315b3"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1bkbh9YLABsMBojp5KrmjwwvHiAFmuepq8P8nG2mK4vqvogVO0pnqfFGr", "stripeSubscriptionId": "sub_1RnZ4VCeRpXjPE6nGxn565hP", "transactionType": "Plan Change", "amount": 50.322258064516134, "currency": "usd", "planType": "Monthly", "status": "completed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1bkbh9YLABsMBojp5KrmjwwvHiAFmuepq8P8nG2mK4vqvogVO0pnqfFGr#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-22T06:13:06.240Z"}, "isProrated": true, "previousPlanType": "Daily", "originalAmount": "59.99", "discount": "9.67", "finalAmount": "50.32", "unusedCredit": "9.67", "remainingDays": 30, "totalDays": 31, "currentPlanPrice": "9.99", "newPlanPrice": "59.99", "invoiceFound": true, "invoiceUpdatedAt": {"$date": "2025-07-22T12:12:38.676Z"}}, "createdAt": {"$date": "2025-07-22T06:13:06.240Z"}, "updatedAt": {"$date": "2025-07-22T12:12:38.676Z"}, "__v": 0, "stripePaymentIntentId": "pi_3RnZS5CeRpXjPE6n0vhz3NGY", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajFkQlZuOFVaODEyY1NTS2ZGZXFVMk5PMVh5SGc0LDE0MzcyNzE1OA0200c3ygVWxz?s=ap"}, {"_id": {"$oid": "687f2c40946ff2108763161f"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1XiMxLe3ITpMHgcZFPnDxiNMaBDyn8udNYJ4lz4WSrsznARATioGtBdsf", "stripeSubscriptionId": "sub_1RnZ4VCeRpXjPE6nGxn565hP", "transactionType": "Plan Change", "amount": 150.32225806451615, "currency": "usd", "planType": "Yearly", "status": "completed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1XiMxLe3ITpMHgcZFPnDxiNMaBDyn8udNYJ4lz4WSrsznARATioGtBdsf#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-22T06:14:24.352Z"}, "isProrated": true, "previousPlanType": "Daily", "originalAmount": "159.99", "discount": "9.67", "finalAmount": "150.32", "unusedCredit": "9.67", "remainingDays": 30, "totalDays": 31, "currentPlanPrice": "9.99", "newPlanPrice": "159.99", "invoiceFound": true, "invoiceUpdatedAt": {"$date": "2025-07-22T12:12:38.697Z"}}, "createdAt": {"$date": "2025-07-22T06:14:24.353Z"}, "updatedAt": {"$date": "2025-07-22T12:12:38.698Z"}, "__v": 0, "stripePaymentIntentId": "pi_3RnZTLCeRpXjPE6n0338xNRJ", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajFkQlZuOFVaODEyY1NTS2ZGZXFVMk5PMVh5SGc0LDE0MzcyNzE1OA0200c3ygVWxz?s=ap"}, {"_id": {"$oid": "687f2d743ac08fe420c25302"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1eVvS02Qm9oSz7lxYkLlYi8cxgXTu1UkMFqMYTWKwlaCMWt6LIwhzyAB1", "stripeSubscriptionId": "sub_1RnZ4VCeRpXjPE6nGxn565hP", "transactionType": "Plan Change", "amount": 101.93516129032258, "currency": "usd", "planType": "Yearly", "status": "completed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1eVvS02Qm9oSz7lxYkLlYi8cxgXTu1UkMFqMYTWKwlaCMWt6LIwhzyAB1#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-22T06:19:32.022Z"}, "isProrated": true, "previousPlanType": "Monthly", "originalAmount": "159.99", "discount": "58.05", "finalAmount": "101.94", "unusedCredit": "58.05", "remainingDays": 30, "totalDays": 31, "currentPlanPrice": "59.99", "newPlanPrice": "159.99", "invoiceFound": true, "invoiceUpdatedAt": {"$date": "2025-07-22T12:12:38.686Z"}}, "createdAt": {"$date": "2025-07-22T06:19:32.026Z"}, "updatedAt": {"$date": "2025-07-22T12:12:38.686Z"}, "__v": 0, "stripePaymentIntentId": "pi_3RnZYICeRpXjPE6n08Z7SJkn", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajFkQlZuOFVaODEyY1NTS2ZGZXFVMk5PMVh5SGc0LDE0MzcyNzE1OA0200c3ygVWxz?s=ap"}, {"_id": {"$oid": "687f2dc83ac08fe420c25377"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1xMKg3U744h7qTHez5aVWwcQW5rGy3WDN0PQyPF3o2cRje6tH0hUNaKAF", "stripeSubscriptionId": "sub_1RnZ4VCeRpXjPE6nGxn565hP", "transactionType": "Plan Change", "amount": 50.322258064516134, "currency": "usd", "planType": "Monthly", "status": "completed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1xMKg3U744h7qTHez5aVWwcQW5rGy3WDN0PQyPF3o2cRje6tH0hUNaKAF#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-22T06:20:56.864Z"}, "isProrated": true, "previousPlanType": "Daily", "originalAmount": "59.99", "discount": "9.67", "finalAmount": "50.32", "unusedCredit": "9.67", "remainingDays": 30, "totalDays": 31, "currentPlanPrice": "9.99", "newPlanPrice": "59.99", "invoiceFound": true, "invoiceUpdatedAt": {"$date": "2025-07-22T12:12:38.547Z"}}, "createdAt": {"$date": "2025-07-22T06:20:56.864Z"}, "updatedAt": {"$date": "2025-07-22T12:12:38.548Z"}, "__v": 0, "stripePaymentIntentId": "pi_3RnZZhCeRpXjPE6n1ircoYBd", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajFkQlZuOFVaODEyY1NTS2ZGZXFVMk5PMVh5SGc0LDE0MzcyNzE1OA0200c3ygVWxz?s=ap"}, {"_id": {"$oid": "687f65602b79ee246f89e4eb"}, "userId": {"$oid": "6864d201692ece524e90cf11"}, "domainId": {"$oid": "68690561128f602c866ccf82"}, "stripeSessionId": "cs_test_a1jMSMwbfeLYnc4PkeaxQGIdrwV3ZRN6F2Kmr2RtI5J5rC8sW5z0HKNOym", "stripeSubscriptionId": "sub_1RndH6CeRpXjPE6n7XdAamkB", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajVTRzlxN0l2UUhwR1pyNDFhNjdjSjBsV3hES1NvLDE0MzcyMDMwMg0200LSLQGzs1?s=ap", "transactionType": "New Subscription", "amount": 59.99, "currency": "usd", "planType": "Monthly", "status": "completed", "paymentMethod": "card", "metadata": {"completedAt": {"$date": "2025-07-22T10:18:21.633Z"}, "stripeStatus": "complete", "stripePaymentStatus": "paid", "stripeInvoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TajVTRzlxN0l2UUhwR1pyNDFhNjdjSjBsV3hES1NvLDE0MzcyMDMwMg0200LSLQGzs1?s=ap", "invoiceId": "in_1RndH4CeRpXjPE6ngCzsYDEv", "invoiceUpdatedAt": {"$date": "2025-07-22T10:18:22.079Z"}, "verifiedAt": {"$date": "2025-07-24T04:06:56.176Z"}, "paymentIntent": null, "chargeId": null}, "createdAt": {"$date": "2025-07-22T10:18:08.743Z"}, "updatedAt": {"$date": "2025-07-22T10:18:08.747Z"}, "__v": 0, "stripeCustomerId": "cus_Sj5R7nW9F9Cl5k", "stripePaymentIntentId": null}, {"_id": {"$oid": "6881c09e2a4d8616e644cd6d"}, "userId": {"$oid": "685911851b581277b5a5fd78"}, "domainId": {"$oid": "686baef1cb02a2ea4571950f"}, "stripeSessionId": "cs_test_a1UtkuwkJGJsjgWVei7lD0vn33i6U9g4NI69j2YvF7Z7V2Au5jsO99Iqg5", "stripeSubscriptionId": "sub_1RoHRuCeRpXjPE6ndqQnDWjY", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9Tamt4S1ZQMlpESURwNFplT0NpQW5pU3g4Zmx6ZlVZLDE0Mzg3NDczMw0200MxaDtJCZ?s=ap", "transactionType": "New Subscription", "amount": 9.99, "currency": "usd", "planType": "Daily", "status": "completed", "paymentMethod": "card", "metadata": {"completedAt": {"$date": "2025-07-24T05:12:12.213Z"}, "stripeStatus": "complete", "stripePaymentStatus": "paid", "stripeInvoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9Tamt4S1ZQMlpESURwNFplT0NpQW5pU3g4Zmx6ZlVZLDE0Mzg3NDczMw0200MxaDtJCZ?s=ap", "invoiceId": "in_1RoHRsCeRpXjPE6nCRlqcoN9", "invoiceUpdatedAt": {"$date": "2025-07-24T05:12:12.790Z"}}, "createdAt": {"$date": "2025-07-24T05:11:58.522Z"}, "updatedAt": {"$date": "2025-07-24T05:11:58.523Z"}, "__v": 0, "stripeCustomerId": "cus_SjkxaDfGNgdYxz", "stripePaymentIntentId": null}, {"_id": {"$oid": "6881d34b9d038096843ecccd"}, "userId": {"$oid": "684c104dccbcf54934d085fa"}, "domainId": {"$oid": "685a22b82ee62a247bdca6a1"}, "stripeSessionId": "recurring_1753338699595_blv4zrwfd", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TamxFc3dyN1NheVFWQ0hlc1RIN0szNTZza2RJY29ILDE0Mzg3OTQ5OQ0200ij32gMaW?s=ap", "transactionType": "Recurring Subscription", "amount": 59.99, "currency": "usd", "planType": "Free", "status": "completed", "paymentMethod": "card", "metadata": {"invoiceId": "in_1RoHi1CeRpXjPE6n1dCsGZz3", "creditsRefreshed": 0, "processedViaServerJs": true, "refreshedAt": {"$date": "2025-07-24T06:31:39.596Z"}}, "createdAt": {"$date": "2025-07-24T06:31:39.597Z"}, "updatedAt": {"$date": "2025-07-24T06:31:39.602Z"}, "__v": 0}, {"_id": {"$oid": "6881d37f9d038096843ecd09"}, "userId": {"$oid": "684c104dccbcf54934d085fa"}, "domainId": {"$oid": "685a22b82ee62a247bdca6a1"}, "stripeSessionId": "recurring_1753338751556_faqn8dwn3k", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TamxIZk5XQzVsMWNLNTFHTEpwT1FMWlNwaEFGdHhHLDE0Mzg3OTU1MQ0200Nxbu4O1D?s=ap", "transactionType": "Recurring Subscription", "amount": 59.99, "currency": "usd", "planType": "Free", "status": "completed", "paymentMethod": "card", "metadata": {"invoiceId": "in_1RoHkvCeRpXjPE6n178ePgvq", "creditsRefreshed": 0, "processedViaServerJs": true, "refreshedAt": {"$date": "2025-07-24T06:32:31.556Z"}}, "createdAt": {"$date": "2025-07-24T06:32:31.556Z"}, "updatedAt": {"$date": "2025-07-24T06:32:31.557Z"}, "__v": 0}, {"_id": {"$oid": "6881d66ee8157220141804ca"}, "userId": {"$oid": "684c104dccbcf54934d085fa"}, "domainId": {"$oid": "685a22b82ee62a247bdca6a1"}, "stripeSessionId": "recurring_1753339502453_ev493d53mnt", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TamxUNktnakhrTUJsdXc4NHFnRWNmV0dhUkZQRmNGLDE0Mzg4MDMwMg02008K8HjR82?s=ap", "transactionType": "Recurring Subscription", "amount": 59.99, "currency": "usd", "planType": "Free", "status": "completed", "paymentMethod": "card", "metadata": {"invoiceId": "in_1RoHwqCeRpXjPE6nEvCjhXWN", "creditsRefreshed": 0, "processedViaServerJs": true, "refreshedAt": {"$date": "2025-07-24T06:45:02.453Z"}}, "createdAt": {"$date": "2025-07-24T06:45:02.454Z"}, "updatedAt": {"$date": "2025-07-24T06:45:02.458Z"}, "__v": 0}, {"_id": {"$oid": "6881daaef7f1fb6e5eb0464c"}, "userId": {"$oid": "684c104dccbcf54934d085fa"}, "domainId": {"$oid": "685a22b82ee62a247bdca6a1"}, "stripeSessionId": "cs_test_a1bWVzcSHYgfQCGnAxqCVTlQtDAvnIaB1IwBJdzwbrgHaePMzbsivGUoKK", "stripeSubscriptionId": null, "invoiceUrl": null, "transactionType": "New Subscription", "amount": 9.99, "currency": "usd", "planType": "Daily", "status": "failed", "paymentMethod": "card", "metadata": {"sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1bWVzcSHYgfQCGnAxqCVTlQtDAvnIaB1IwBJdzwbrgHaePMzbsivGUoKK#fidkdWxOYHwnPyd1blpxYHZxWjA0V21UQ2dGYFd1XW9VQDNrY1FcYnY8bHNuTHxcbGNsS3x1ZzFhSVJEQ1JvUUlpYU00YlJfXDRxSmZUUTVQVl1kMV1cR1xWN2RGdVV%2FNGBhakZ1YFJISWBqNTVQbm1sf3dJMCcpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl", "createdAt": {"$date": "2025-07-24T07:03:10.566Z"}, "invoiceFound": false, "hasSubscriptionId": false, "verifiedAt": {"$date": "2025-07-24T07:08:42.659Z"}, "stripeStatus": "open", "stripePaymentStatus": "unpaid", "paymentIntent": null, "chargeId": null}, "createdAt": {"$date": "2025-07-24T07:03:10.566Z"}, "updatedAt": {"$date": "2025-07-24T07:08:42.660Z"}, "__v": 0}, {"_id": {"$oid": "6881dbffb40177e2c287b583"}, "userId": {"$oid": "684c104dccbcf54934d085fa"}, "domainId": {"$oid": "685a22b82ee62a247bdca6a1"}, "stripeSessionId": "cs_test_a1ojeuEJxQoDKoJa1Cg5KdLnwcQvYzXUdtfUG8NVHfAI1CLQtCEaPBo8dE", "stripeSubscriptionId": "sub_1RoJGxCeRpXjPE6nhEU5ZIo6", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9Tam1xN3JnNkRiRjNHdUJvWlJDdjcyYUI0RDhQbkhHLDE0Mzg4MTc0Mg0200ASqyoVS8?s=ap", "transactionType": "New Subscription", "amount": 9.99, "currency": "usd", "planType": "Daily", "status": "completed", "paymentMethod": "card", "metadata": {"completedAt": {"$date": "2025-07-24T07:09:01.202Z"}, "stripeStatus": "complete", "stripePaymentStatus": "paid", "stripeInvoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9Tam1xN3JnNkRiRjNHdUJvWlJDdjcyYUI0RDhQbkhHLDE0Mzg4MTc0Mg0200ASqyoVS8?s=ap", "invoiceId": "in_1RoJGwCeRpXjPE6nJbZu88Re", "invoiceUpdatedAt": {"$date": "2025-07-24T07:09:01.758Z"}}, "createdAt": {"$date": "2025-07-24T07:08:47.470Z"}, "updatedAt": {"$date": "2025-07-24T07:08:47.471Z"}, "__v": 0, "stripeCustomerId": "cus_SjmqRd7GNqOBZC", "stripePaymentIntentId": null}, {"_id": {"$oid": "6881dc0db40177e2c287b598"}, "userId": {"$oid": "684bb5494c79ee7876ba027e"}, "domainId": {"$oid": "685cd16eb23b92b3fbdea1e9"}, "stripeSessionId": "recurring_1753340941411_5yuvg4m22cf", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9Tam1xN3JnNkRiRjNHdUJvWlJDdjcyYUI0RDhQbkhHLDE0Mzg4MTc0MQ0200N9BsASzV?s=ap", "transactionType": "Recurring Subscription", "amount": 9.99, "currency": "usd", "planType": "Free", "status": "completed", "paymentMethod": "card", "metadata": {"invoiceId": "in_1RoJGwCeRpXjPE6nJbZu88Re", "creditsRefreshed": 0, "processedViaServerJs": true, "refreshedAt": {"$date": "2025-07-24T07:09:01.411Z"}}, "createdAt": {"$date": "2025-07-24T07:09:01.411Z"}, "updatedAt": {"$date": "2025-07-24T07:09:01.412Z"}, "__v": 0}, {"_id": {"$oid": "68820d7ccfb5b0341cacee1f"}, "userId": {"$oid": "685911851b581277b5a5fd78"}, "domainId": {"$oid": "686baef1cb02a2ea4571950f"}, "stripeSessionId": "cs_test_a1QhNnNYCaEyzEiMo8FGWvykuhv3gVdeAPRF8Z3FwNb8G0fRFXJZ3Rnxhr", "stripeSubscriptionId": "sub_1RoMZPCeRpXjPE6npl3BPL8O", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TanFGYnQ0TFNxMlZuT0l1ZUUwRmR0MURRSEJ0aHE0LDE0Mzg5NDQxNw0200EnAnKqs9?s=ap", "transactionType": "New Subscription", "amount": 9.99, "currency": "usd", "planType": "Daily", "status": "completed", "paymentMethod": "card", "metadata": {"completedAt": {"$date": "2025-07-24T10:40:16.942Z"}, "stripeStatus": "complete", "stripePaymentStatus": "paid", "stripeInvoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TanFGYnQ0TFNxMlZuT0l1ZUUwRmR0MURRSEJ0aHE0LDE0Mzg5NDQxNw0200EnAnKqs9?s=ap", "invoiceId": "in_1RoMZOCeRpXjPE6n0WjD6bmF", "invoiceUpdatedAt": {"$date": "2025-07-24T10:40:17.520Z"}}, "createdAt": {"$date": "2025-07-24T10:39:56.826Z"}, "updatedAt": {"$date": "2025-07-24T10:39:56.827Z"}, "__v": 0, "stripeCustomerId": "cus_SjqFLtm74JPhTI", "stripePaymentIntentId": null}, {"_id": {"$oid": "68820d91cfb5b0341cacee28"}, "userId": {"$oid": "684bb5494c79ee7876ba027e"}, "domainId": {"$oid": "685cd16eb23b92b3fbdea1e9"}, "stripeSessionId": "recurring_1753353617046_hqsnbt6m5mt", "invoiceUrl": "https://invoice.stripe.com/i/acct_1RhQFbCeRpXjPE6n/test_YWNjdF8xUmhRRmJDZVJwWGpQRTZuLF9TanFGYnQ0TFNxMlZuT0l1ZUUwRmR0MURRSEJ0aHE0LDE0Mzg5NDQxNw0200EnAnKqs9?s=ap", "transactionType": "Recurring Subscription", "amount": 9.99, "currency": "usd", "planType": "Free", "status": "completed", "paymentMethod": "card", "metadata": {"invoiceId": "in_1RoMZOCeRpXjPE6n0WjD6bmF", "creditsRefreshed": 0, "processedViaServerJs": true, "refreshedAt": {"$date": "2025-07-24T10:40:17.046Z"}}, "createdAt": {"$date": "2025-07-24T10:40:17.046Z"}, "updatedAt": {"$date": "2025-07-24T10:40:17.047Z"}, "__v": 0}]