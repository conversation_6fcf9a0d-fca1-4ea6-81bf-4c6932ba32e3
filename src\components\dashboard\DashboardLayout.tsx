import React, { useState } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { useLocation, useNavigate } from 'react-router-dom';

interface DashboardLayoutProps {
    children: React.ReactNode;
    pageTitle?: string;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children, pageTitle }) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [activeView, setActiveView] = useState('articles');

    const handleViewChange = (view: string) => {
        setActiveView(view);
        navigate(`/dashboard/${view}`);
    };

    const handleWrite = () => {
        navigate('/dashboard/write');
    };

    return (
        <div className="flex h-screen bg-gray-100">
            <Sidebar
                activeView={activeView}
                onViewChange={handleViewChange}
                onWrite={handleWrite}
            />
            <div className="flex-1 flex flex-col overflow-hidden">
                <Header title={pageTitle} />
                <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                    <div className="container mx-auto px-6 py-8">
                        {children}
                    </div>
                </main>
            </div>
        </div>
    );
}; 