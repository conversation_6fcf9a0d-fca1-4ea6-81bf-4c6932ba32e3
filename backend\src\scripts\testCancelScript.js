// Test script to verify the cancel daily subscriptions script setup
require('dotenv').config();

console.log('🧪 Testing Daily Subscription Cancellation Script Setup\n');

// Test environment variables
console.log('📋 Environment Variables Check:');
console.log(`STRIPE_SECRET_KEY: ${process.env.STRIPE_SECRET_KEY ? '✅ Set' : '❌ Not set'}`);
console.log(`PRICE_ID_DAILY: ${process.env.PRICE_ID_DAILY ? '✅ Set' : '❌ Not set'}`);

if (process.env.STRIPE_SECRET_KEY) {
    console.log(`Stripe Key Preview: ${process.env.STRIPE_SECRET_KEY.substring(0, 12)}...`);
}

if (process.env.PRICE_ID_DAILY) {
    console.log(`Daily Price ID: ${process.env.PRICE_ID_DAILY}`);
}

// Test Stripe connection
console.log('\n🔗 Testing Stripe Connection:');

if (!process.env.STRIPE_SECRET_KEY) {
    console.log('❌ Cannot test Stripe connection - STRIPE_SECRET_KEY not set');
    process.exit(1);
}

try {
    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

    // Test API call
    stripe.products.list({ limit: 1 })
        .then(products => {
            console.log('✅ Stripe connection successful');
            console.log(`Found ${products.data.length} product(s) in your Stripe account`);

            if (process.env.PRICE_ID_DAILY) {
                console.log('\n🔍 Testing Daily Price ID:');
                return stripe.prices.retrieve(process.env.PRICE_ID_DAILY);
            }
        })
        .then(price => {
            if (price) {
                console.log('✅ Daily price ID is valid');
                console.log(`Price: ${price.unit_amount / 100} ${price.currency.toUpperCase()}`);
                console.log(`Product ID: ${price.product}`);

                // Test subscription listing for this price
                console.log('\n📊 Testing subscription listing:');
                return stripe.subscriptions.list({
                    price: process.env.PRICE_ID_DAILY,
                    limit: 5
                });
            }
        })
        .then(subscriptions => {
            if (subscriptions) {
                console.log(`✅ Found ${subscriptions.data.length} subscription(s) for daily plan`);

                const activeCount = subscriptions.data.filter(sub =>
                    sub.status === 'active' || sub.status === 'trialing'
                ).length;

                console.log(`📈 Active/Trialing subscriptions: ${activeCount}`);

                if (subscriptions.data.length > 0) {
                    console.log('\n📋 Sample subscription details:');
                    subscriptions.data.slice(0, 3).forEach((sub, index) => {
                        console.log(`  ${index + 1}. ${sub.id} - Status: ${sub.status} - Customer: ${sub.customer}`);
                    });
                }
            }

            console.log('\n✅ All tests passed! The cancellation script should work correctly.');
            console.log('\n🚀 Next steps:');
            console.log('1. Run: npm run cancel-daily-subscriptions:dry-run');
            console.log('2. Review the output carefully');
            console.log('3. If everything looks correct, run: npm run cancel-daily-subscriptions');
        })
        .catch(error => {
            console.log('❌ Error testing Stripe setup:', error.message);

            if (error.type === 'StripeAuthenticationError') {
                console.log('💡 Your Stripe secret key appears to be invalid');
            } else if (error.type === 'StripeInvalidRequestError' && error.code === 'resource_missing') {
                console.log('💡 The PRICE_ID_DAILY value appears to be invalid');
            }

            process.exit(1);
        });

} catch (error) {
    console.log('❌ Error loading Stripe:', error.message);
    process.exit(1);
} 