import { useState } from 'react';
import { Navigate } from 'react-router-dom';
import { OnboardingFlow } from '@/components/onboarding/OnboardingFlow';

const Index = () => {
  const [isOnboarded, setIsOnboarded] = useState(false);

  const handleOnboardingComplete = () => {
    setIsOnboarded(true);
  };

  const handleSkipOnboarding = () => {
    setIsOnboarded(true);
  };

  if (isOnboarded) {
    return <Navigate to="/dashboard/articles" replace />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <OnboardingFlow onComplete={handleOnboardingComplete} onSkip={handleSkipOnboarding} />
    </div>
  );
};

export default Index;
