import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArticleEditor } from '@/components/dashboard/ArticleEditor';
import { Article } from '@/types/Article';
import { api } from '@/services/api';
import { useToast } from '@/components/ui/use-toast';
import { articleService } from '@/services/articleService';

export const EditArticlePage = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [article, setArticle] = useState<Article | null>(null);
    const [loading, setLoading] = useState(true);
    const { toast } = useToast();

    useEffect(() => {
        const fetchArticle = async () => {
            try {
                setLoading(true);
                if (!id) throw new Error('No article ID provided');
                const articleData = await articleService.fetchArticleById(id);
                setArticle(articleData);
            } catch (error) {
                console.error('Error fetching article:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "Failed to load article"
                });
                navigate('/dashboard/articles');
            } finally {
                setLoading(false);
            }
        };

        if (id) {
            fetchArticle();
        }
    }, [id, navigate, toast]);

    const handleClose = () => {
        navigate('/dashboard/articles');
    };

    const handleSave = async (updatedArticle: Article) => {
        try {
            if (!id) throw new Error('No article ID provided');
            await articleService.updateArticle(id, updatedArticle);
            toast({
                title: "Success",
                description: "Article updated successfully"
            });
            navigate('/dashboard/articles');
        } catch (error) {
            console.error('Error saving article:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to save article"
            });
        }
    };

    return (
        <div className="h-full bg-white">
            {loading ? (
                <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
                </div>
            ) : article ? (
                <ArticleEditor
                    article={article}
                    onClose={handleClose}
                    onSave={handleSave}
                />
            ) : (
                <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold text-gray-800">Article not found</h2>
                        <p className="mt-2 text-gray-600">The article you're looking for doesn't exist or has been removed.</p>
                    </div>
                </div>
            )}
        </div>
    );
}; 