const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    firstName: {
        type: String,
        required: false, // Make firstName not required
        trim: true,
        default: '' // Provide default empty string
    },
    lastName: {
        type: String,
        required: false, // Make lastName optional
        trim: true,
        default: ''
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        unique: true,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    password: {
        type: String,
        required: function () {
            // Password is required only if no social login IDs are provided
            return !this.googleId;
        },
        minlength: [8, 'Password must be at least 8 characters long'],
        select: false
    },
    googleId: {
        type: String,
        unique: true,
        sparse: true // Allows null values and only enforces uniqueness for non-null values
    },
    avatar: {
        type: String,
        default: ''
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    lastLogin: {
        type: Date,
        default: Date.now
    },
    isActive: {
        type: Boolean,
        default: true
    }
});

// Hash password before saving
userSchema.pre('save', async function (next) {
    // Only hash the password if it has been modified (or is new)
    if (!this.isModified('password')) {
        return next();
    }

    try {
        // Generate salt
        const salt = await bcrypt.genSalt(10);
        // Hash password
        this.password = await bcrypt.hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function (candidatePassword) {
    try {
        // Since password field is not selected by default, we need to handle cases where it might be undefined
        if (!this.password) {
            // If user doesn't have a password (social login only), prevent password login
            return false;
        }
        return await bcrypt.compare(candidatePassword, this.password);
    } catch (error) {
        console.error('Error comparing passwords:', error);
        throw error;
    }
};

const User = mongoose.model('User', userSchema);

module.exports = User;
