import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>ontent, Card<PERSON>ooter, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Loader2, <PERSON><PERSON><PERSON>riangle } from 'lucide-react';
import { paymentService } from '@/services/paymentService';
import { useDomain } from '@/contexts/DomainContext';
import { toast } from '@/components/ui/use-toast';
import { loadStripe } from '@stripe/stripe-js';
import { ConfirmationDialog } from '@/components/ConfirmationDialog';
import { transactionService } from '@/services/transactionService';
import { Checkbox } from '@/components/ui/checkbox';
import { useLocation } from 'react-router-dom';

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

// Helper function to validate dates
const isValidDate = (date: Date) => !isNaN(date.getTime());

// Helper function to format date
const formatDate = (dateString: string | Date | null | undefined): string => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return isValidDate(date) ? date.toLocaleDateString() : 'N/A';
};

const PlansPage = () => {
    const { currentDomain, refreshDomains } = useDomain();
    const [loading, setLoading] = useState<boolean>(false);
    const [subscription, setSubscription] = useState<{
        active: boolean;
        planType: string;
        canceledAt?: Date | string;
        expiresAt?: Date | string;
    }>({
        active: false,
        planType: 'Free'
    });
    const [plan, setPlan] = useState<any>(null);
    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [cancelImmediately, setCancelImmediately] = useState(false);
    const location = useLocation();

    // Check if we're coming from a payment success page
    const isFromPayment = location.search.includes('payment_success=true');

    useEffect(() => {
        // Fetch current plan data and subscription status when domain changes or when redirected from payment
        if (currentDomain?._id) {
            // Force a domain refresh when coming from payment success page
            if (isFromPayment) {
                refreshDomains().then(async () => {
                    // Force refresh subscription data
                    try {
                        await paymentService.refreshSubscriptionData(currentDomain._id);
                        console.log('Subscription data refreshed after payment success redirect');
                    } catch (err) {
                        console.error('Error refreshing subscription data:', err);
                    }

                    fetchCurrentPlan();
                    fetchSubscription(currentDomain._id);
                });
            } else {
                fetchCurrentPlan();
                fetchSubscription(currentDomain._id);
            }
        }
    }, [currentDomain, isFromPayment]);

    // Log subscription state for debugging
    useEffect(() => {
        if (currentDomain?._id) {
            console.log('Current subscription state:', {
                active: subscription.active,
                planType: subscription.planType,
                isCanceled: isCanceled,
                domainId: currentDomain._id
            });
        }
    }, [subscription, currentDomain]);

    const fetchCurrentPlan = async () => {
        try {
            if (!currentDomain?._id) return;

            const response = await paymentService.getCurrentPlan(currentDomain._id);
            setPlan(response.plan);
        } catch (error) {
            console.error('Error fetching plan:', error);
            toast({
                title: 'Error',
                description: 'Failed to load plan details',
                variant: 'destructive',
            });
        }
    };

    const fetchSubscription = async (domainId: string) => {
        try {
            const response = await paymentService.getSubscription(domainId);
            setSubscription(response.subscription);
        } catch (error) {
            console.error('Error fetching subscription:', error);
            toast({
                title: 'Error',
                description: 'Failed to load subscription details',
                variant: 'destructive',
            });
        }
    };

    const handleSubscribe = async (planType: 'Daily' | 'Monthly' | 'Yearly') => {
        if (!currentDomain) {
            toast({
                title: 'No domain selected',
                description: 'Please select a domain first',
                variant: 'destructive',
            });
            return;
        }

        setLoading(true);
        try {
            // Check if user is currently on a paid plan
            const isUpgrading = subscription.active && subscription.planType !== 'Free';
            let url;

            // If user is upgrading from a paid plan, use proration
            if (isUpgrading) {
                // Get prorated amount for plan change
                const proratedDetails = await paymentService.calculateProratedAmount(
                    currentDomain._id,
                    planType
                );

                // Show toast with prorated amount details
                try {
                    // Ensure we have valid numbers by providing fallbacks
                    const originalAmount = proratedDetails?.details?.originalAmount !== undefined &&
                        !isNaN(proratedDetails.details.originalAmount) ?
                        proratedDetails.details.originalAmount.toFixed(2) : '0.00';

                    const discount = proratedDetails?.details?.discount !== undefined &&
                        !isNaN(proratedDetails.details.discount) ?
                        proratedDetails.details.discount.toFixed(2) : '0.00';

                    const finalAmount = proratedDetails?.details?.finalAmount !== undefined &&
                        !isNaN(proratedDetails.details.finalAmount) ?
                        proratedDetails.details.finalAmount.toFixed(2) : '0.00';

                    const unusedCredit = proratedDetails?.details?.unusedCredit !== undefined &&
                        !isNaN(proratedDetails.details.unusedCredit) ?
                        proratedDetails.details.unusedCredit.toFixed(2) : '0.00';

                    const currentPlanType = proratedDetails?.details?.currentPlanType || 'current';
                    const newPlanType = proratedDetails?.details?.newPlanType || planType;

                    // Format dates if available
                    const subscriptionStart = proratedDetails?.details?.subscriptionStart ?
                        new Date(proratedDetails.details.subscriptionStart).toLocaleDateString() : null;

                    const subscriptionEnd = proratedDetails?.details?.subscriptionEnd ?
                        new Date(proratedDetails.details.subscriptionEnd).toLocaleDateString() : null;

                    console.log('Prorated details for toast:', {
                        originalAmount,
                        discount,
                        finalAmount,
                        unusedCredit,
                        currentPlanType,
                        newPlanType,
                        subscriptionStart,
                        subscriptionEnd,
                        rawDetails: proratedDetails?.details
                    });

                    // Build the toast message
                    let toastMessage = `Changing from ${currentPlanType} to ${newPlanType} plan.\n`;
                    toastMessage += `New plan price: $${originalAmount}\n`;
                    toastMessage += `Credit from current plan: $${unusedCredit}\n`;
                    toastMessage += `Final payment: $${finalAmount}`;

                    // Add subscription period info if available
                    if (subscriptionStart && subscriptionEnd) {
                        toastMessage += `\n(Current subscription period: ${subscriptionStart} to ${subscriptionEnd})`;
                    }

                    toast({
                        title: 'Plan Change Details',
                        description: toastMessage,
                    });
                } catch (error) {
                    console.error('Error formatting prorated amounts:', error);
                    toast({
                        title: 'Plan Change',
                        description: `Your new ${planType} plan will be prorated.`,
                    });
                }

                // Create a checkout session with prorated amount
                const response = await paymentService.createProratedCheckoutSession(currentDomain._id, planType);
                url = response.url;

                console.log("Redirecting to prorated checkout URL:", url);

                // Use setTimeout to ensure the toast is shown before redirecting
                setTimeout(() => {
                    window.location.href = url;
                }, 1000);
            } else {
                // First create the plan in our database
                await paymentService.createPlan(planType, currentDomain._id);

                // Then create the regular Stripe checkout session
                const response = await paymentService.createCheckoutSession(currentDomain._id, planType);
                url = response.url;

                // Redirect to Stripe Checkout
                window.location.href = url;
            }
        } catch (error) {
            console.error('Error creating checkout session:', error);
            toast({
                title: 'Error',
                description: 'Failed to start checkout process',
                variant: 'destructive',
            });
        } finally {
            setLoading(false);
        }
    };

    // Determine which plan is current based on the domain-specific subscription data
    // Use subscription.planType from the current domain's subscription
    const isFreePlan = subscription.planType === 'Free' || (!subscription.active && !plan?.displayType);
    const isDailyPlan = subscription.planType === 'Daily' && subscription.active;
    const isMonthlyPlan = subscription.planType === 'Monthly' && subscription.active;
    const isYearlyPlan = subscription.planType === 'Yearly' && subscription.active;
    const isCanceled = subscription.canceledAt && subscription.expiresAt;

    const handleCancelSubscription = async () => {
        if (!currentDomain) {
            toast({
                title: 'No domain selected',
                description: 'Please select a domain first',
                variant: 'destructive',
            });
            return;
        }

        // First sync with Stripe to ensure we have the latest subscription status
        setLoading(true);
        try {
            // Sync subscription with Stripe
            await paymentService.syncSubscription(currentDomain._id);

            // Refresh subscription data
            await fetchSubscription(currentDomain._id);

            // Check if the domain has an active subscription after syncing
            if (!subscription.active || !subscription.planType || subscription.planType === 'Free') {
                toast({
                    title: 'No active subscription',
                    description: 'This domain does not have an active subscription to cancel.',
                    variant: 'destructive',
                });
                setLoading(false);
                return;
            }

            // Open the confirmation dialog
            setShowCancelDialog(true);
        } catch (error) {
            console.error('Error syncing subscription:', error);
            toast({
                title: 'Error',
                description: 'Failed to verify subscription status. Please try again.',
                variant: 'destructive',
            });
        } finally {
            setLoading(false);
        }
    };

    // Handle dialog close without confirming
    const handleDialogClose = () => {
        setShowCancelDialog(false);
        // Ensure loading is reset if dialog is closed without confirming
        setLoading(false);
    };

    const confirmCancelSubscription = async () => {
        setLoading(true);
        try {
            console.log('Canceling subscription for domain:', currentDomain._id, 'immediately:', cancelImmediately);
            const response = await paymentService.cancelSubscription(currentDomain._id, cancelImmediately);
            console.log('Cancel subscription response:', response);

            // Handle Daily plan special case with expired subscription
            if (subscription.planType === 'Daily' && response.message?.includes('expired')) {
                toast({
                    title: 'Plan Updated',
                    description: 'Your Daily plan subscription has expired and been updated to Free.',
                });
            } else if (response.expiresAt) {
                // If the subscription was set to cancel at period end
                toast({
                    title: 'Subscription Canceled',
                    description: `Your subscription will remain active until ${formatDate(response.expiresAt)}, after which it will be downgraded to Free.`,
                });
            } else {
                // If the subscription was canceled immediately
                toast({
                    title: 'Subscription Canceled',
                    description: 'Your subscription has been canceled and your plan has been downgraded to Free',
                });
            }

            // Refresh the plan and subscription data
            await refreshDomains(); // Refresh domain data first
            await fetchCurrentPlan();
            await fetchSubscription(currentDomain._id);
        } catch (error) {
            console.error('Error canceling subscription:', error);

            // Special handling for Daily plans with expired subscriptions
            if (subscription.planType === 'Daily' &&
                error.message &&
                (error.message.includes('No active subscription') ||
                    error.message.includes('resource_missing') ||
                    error.message.includes('subscription not found') ||
                    error.message.includes('expired'))) {

                console.log('Daily plan subscription might have expired. Refreshing data...');

                // Force refresh data
                await refreshDomains();
                await fetchCurrentPlan();
                await fetchSubscription(currentDomain._id);

                toast({
                    title: 'Plan Updated',
                    description: 'Your Daily plan has been updated to Free.',
                });
            } else {
                // Handle other errors
                toast({
                    title: 'Error',
                    description: error.message || 'Failed to cancel subscription. Please try again.',
                    variant: 'destructive',
                });
            }
        } finally {
            setLoading(false);
            setShowCancelDialog(false);
        }
    };

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-2xl font-bold mb-2">Subscription Plans</h1>
            <p className="text-gray-600 mb-8">Choose the right plan for your blogging needs</p>

            {/* Display cancellation notice if subscription is canceled but not yet expired */}
            {isCanceled && (subscription.active) && (
                <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-md flex items-center">
                    <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 shrink-0" />
                    <div>
                        <p className="text-amber-800 font-medium">Your subscription has been canceled</p>
                        <p className="text-amber-700">
                            You will have access to your {subscription.planType} plan until {formatDate(subscription.expiresAt)}.
                            After this date, your plan will be downgraded to Free.
                        </p>
                    </div>
                </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Free Plan */}
                <Card className="border-gray-200 shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
                    <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Free</CardTitle>
                        <CardDescription className="text-xs">Get started with basic features</CardDescription>
                        <div className="mt-2">
                            <span className="text-2xl font-bold">$0</span>
                            <span className="text-gray-500 ml-1 text-xs">/ month</span>
                        </div>
                    </CardHeader>
                    <CardContent className="py-2">
                        <ul className="space-y-1 text-sm">
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>1 domain</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>10 articles per month</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>Basic SEO tools</span>
                            </li>
                        </ul>
                    </CardContent>
                    <CardFooter className="mt-auto pt-2">
                        <Button
                            variant={isFreePlan ? "outline" : "secondary"}
                            className="w-full text-sm py-1"
                            disabled={isFreePlan || !currentDomain}
                            onClick={() => {
                                if (currentDomain) {
                                    paymentService.createPlan('Free', currentDomain._id)
                                        .then(() => {
                                            toast({
                                                title: 'Success',
                                                description: 'Your plan has been updated to Free',
                                            });
                                            fetchCurrentPlan();
                                            fetchSubscription(currentDomain._id);
                                        })
                                        .catch(error => {
                                            console.error('Error updating plan:', error);
                                            toast({
                                                title: 'Error',
                                                description: 'Failed to update plan',
                                                variant: 'destructive',
                                            });
                                        });
                                }
                            }}
                        >
                            {isFreePlan ? 'Current Plan' : 'Downgrade'}
                        </Button>
                    </CardFooter>
                </Card>

                {/* Daily Plan */}
                <Card className="border-gray-200 shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
                    <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Daily</CardTitle>
                        <CardDescription className="text-xs">Perfect for daily blogging needs</CardDescription>
                        <div className="mt-2">
                            <span className="text-2xl font-bold">$9.99</span>
                            <span className="text-gray-500 ml-1 text-xs">/ day</span>
                        </div>
                    </CardHeader>
                    <CardContent className="py-2">
                        <ul className="space-y-1 text-sm">
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>2 domains</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>5 articles per day</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>Basic SEO tools</span>
                            </li>
                        </ul>
                    </CardContent>
                    <CardFooter className="mt-auto pt-2">
                        <Button
                            className="w-full bg-blue-500 hover:bg-blue-600 text-sm py-1"
                            onClick={() => handleSubscribe('Daily')}
                            disabled={loading || isDailyPlan || !currentDomain}
                        >
                            {loading ? (
                                <>
                                    <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                    Processing...
                                </>
                            ) : isDailyPlan ? (
                                'Current Plan'
                            ) : (
                                'Upgrade Now'
                            )}
                        </Button>
                    </CardFooter>
                </Card>

                {/* Monthly Plan */}
                <Card className="border-gray-200 shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
                    <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Monthly</CardTitle>
                        <CardDescription className="text-xs">Perfect for growing blogs</CardDescription>
                        <div className="mt-2">
                            <span className="text-2xl font-bold">$59.99</span>
                            <span className="text-gray-500 ml-1 text-xs">/ month</span>
                        </div>
                    </CardHeader>
                    <CardContent className="py-2">
                        <ul className="space-y-1 text-sm">
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>3 domains</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>30 articles per month</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>Advanced SEO tools</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>Priority support</span>
                            </li>
                        </ul>
                    </CardContent>
                    <CardFooter className="mt-auto pt-2">
                        <Button
                            className="w-full bg-orange-500 hover:bg-orange-600 text-sm py-1"
                            onClick={() => handleSubscribe('Monthly')}
                            disabled={loading || isMonthlyPlan || !currentDomain}
                        >
                            {loading ? (
                                <>
                                    <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                    Processing...
                                </>
                            ) : isMonthlyPlan ? (
                                'Current Plan'
                            ) : (
                                'Upgrade Now'
                            )}
                        </Button>
                    </CardFooter>
                </Card>

                {/* Yearly Plan - Most Popular */}
                <Card className="border-orange-200 shadow-md hover:shadow-lg transition-shadow relative flex flex-col h-full">
                    <div className="absolute -top-2 left-0 w-full flex justify-center">
                        <Badge className="bg-orange-500 text-white px-2 py-0.5 text-xs">Most Popular</Badge>
                    </div>
                    <CardHeader className="bg-orange-50 rounded-t-lg pb-2">
                        <CardTitle className="text-lg">Yearly</CardTitle>
                        <CardDescription className="text-xs">Best value for serious bloggers</CardDescription>
                        <div className="mt-2">
                            <span className="text-2xl font-bold">$159.99</span>
                            <span className="text-gray-500 ml-1 text-xs">/ year</span>
                            <div className="text-orange-600 text-xs mt-0.5 font-medium">Save $79.89 (33%)</div>
                        </div>
                    </CardHeader>
                    <CardContent className="py-2">
                        <ul className="space-y-1 text-sm">
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>5 domains</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>50 articles per month</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>Premium SEO tools</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>24/7 priority support</span>
                            </li>
                            <li className="flex items-start">
                                <Check className="h-4 w-4 text-green-500 mr-1 shrink-0" />
                                <span>AI-generated images</span>
                            </li>
                        </ul>
                    </CardContent>
                    <CardFooter className="mt-auto pt-2">
                        <Button
                            className="w-full bg-orange-500 hover:bg-orange-600 text-sm py-1"
                            onClick={() => handleSubscribe('Yearly')}
                            disabled={loading || isYearlyPlan || !currentDomain}
                        >
                            {loading ? (
                                <>
                                    <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                    Processing...
                                </>
                            ) : isYearlyPlan ? (
                                'Current Plan'
                            ) : (
                                'Get Best Value'
                            )}
                        </Button>
                    </CardFooter>
                </Card>
            </div>

            {!currentDomain && (
                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800">
                    <p>Please select a domain to subscribe to a plan.</p>
                </div>
            )}

            {currentDomain && (!subscription.active || subscription.planType === 'Free') && (
                <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
                    <h3 className="text-lg font-medium text-gray-800 mb-2">No Active Subscription</h3>
                    <p className="text-gray-700">You don't have an active paid subscription for this domain. Choose one of the plans above to get started.</p>
                </div>
            )}

            {plan && subscription.active && subscription.planType !== 'Free' && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <h3 className="text-lg font-medium text-blue-800 mb-2">Current Plan Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p className="text-blue-800">
                                <strong>Plan Type:</strong> {subscription.planType === 'Daily' ? 'Daily ($9.99/day)' :
                                    subscription.planType === 'Monthly' ? 'Monthly ($59.99/month)' :
                                        subscription.planType === 'Yearly' ? 'Yearly ($159.99/year)' :
                                            'Free'}
                            </p>
                            <p className="text-blue-800">
                                <strong>Status:</strong> <span className={`${isCanceled ? 'text-amber-600' :
                                    plan.status === 'active' ? 'text-green-600' :
                                        'text-red-600'} font-medium`}>
                                    {isCanceled ? 'Canceled (Active until expiry)' :
                                        plan.status.charAt(0).toUpperCase() + plan.status.slice(1)}
                                </span>
                            </p>
                            <p className="text-blue-800">
                                <strong>Start Date:</strong> {formatDate(plan.startDate)}
                            </p>
                            {plan.endDate && isValidDate(new Date(plan.endDate)) && (
                                <p className="text-blue-800">
                                    <strong>End Date:</strong> {formatDate(plan.endDate)}
                                </p>
                            )}
                            {isCanceled && (
                                <p className="text-amber-700 font-medium mt-2">
                                    <strong>Subscription Expires:</strong> {formatDate(subscription.expiresAt)}
                                </p>
                            )}
                        </div>
                        <div>
                            <p className="text-blue-800"><strong>Max Domains:</strong> {plan.features?.maxDomains}</p>
                            <p className="text-blue-800"><strong>Articles Per Month:</strong> {plan.features?.maxArticlesPerMonth}</p>
                            <p className="text-blue-800"><strong>Custom Branding:</strong> {plan.features?.customBranding ? 'Yes' : 'No'}</p>
                        </div>
                    </div>

                    {/* Add Cancel Subscription button */}
                    {subscription.active &&
                        subscription.planType &&
                        subscription.planType !== 'Free' &&
                        !isCanceled && (
                            <div className="mt-4 flex justify-end">
                                <Button
                                    variant="destructive"
                                    className="bg-red-500 hover:bg-red-600"
                                    onClick={handleCancelSubscription}
                                    disabled={loading}
                                >
                                    {loading ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Canceling...
                                        </>
                                    ) : 'Cancel Subscription'}
                                </Button>
                            </div>
                        )}
                </div>
            )}

            {/* Cancel Subscription Confirmation Dialog */}
            <ConfirmationDialog
                isOpen={showCancelDialog}
                onClose={handleDialogClose}
                onConfirm={confirmCancelSubscription}
                title="Cancel Subscription"
                message={
                    <>
                        <div className="mb-4">Are you sure you want to cancel your subscription?</div>
                        <div className="flex items-center space-x-2 mb-4">
                            <Checkbox
                                id="cancelImmediately"
                                checked={cancelImmediately}
                                onCheckedChange={(checked) => setCancelImmediately(checked === true)}
                            />
                            <label
                                htmlFor="cancelImmediately"
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                                Cancel immediately (otherwise your subscription will remain active until the end of your current billing period)
                            </label>
                        </div>
                        <div className="text-sm text-gray-500">
                            {cancelImmediately
                                ? "Your subscription will be canceled immediately and your plan will be downgraded to Free."
                                : "You will continue to have access until the end of your current billing period, after which your plan will be downgraded to Free."}
                        </div>
                    </>
                }
                confirmText="Yes, Cancel Subscription"
                cancelText="No, Keep My Plan"
                isDestructive={true}
                isLoading={loading}
            />
        </div>
    );
};

export default PlansPage; 