# Daily Subscription Cancellation Script

This script allows you to cancel all daily plan subscriptions directly from Stripe. It's designed to be safe, with dry-run capabilities and comprehensive logging.

## Features

- ✅ **Dry Run Mode**: Test the script without actually canceling subscriptions
- ✅ **Batch Processing**: Handles large numbers of subscriptions efficiently
- ✅ **Rate Limiting**: Respects Stripe API rate limits
- ✅ **Comprehensive Logging**: Detailed logs with timestamps and status indicators
- ✅ **Error Handling**: Graceful error handling with detailed error reporting
- ✅ **Safety Confirmation**: Requires user confirmation before actual cancellations
- ✅ **Flexible Options**: Can cancel by price ID or product ID

## Prerequisites

1. **Environment Variables**: Make sure you have the following environment variables set:
   ```env
   STRIPE_SECRET_KEY=sk_live_your_secret_key_here
   PRICE_ID_DAILY=price_your_daily_price_id_here
   ```

2. **Node.js Dependencies**: The script uses the existing project dependencies (stripe, dotenv, etc.)

## Usage

### Method 1: Using npm scripts (Recommended)

```bash
# Navigate to the backend directory
cd backend

# Run a dry run first (safe - won't cancel anything)
npm run cancel-daily-subscriptions:dry-run

# If the dry run looks good, run the actual cancellation
npm run cancel-daily-subscriptions
```

### Method 2: Direct node execution

```bash
# Navigate to the backend directory
cd backend

# Dry run
node src/scripts/cancelDailySubscriptions.js --dry-run

# Actual cancellation
node src/scripts/cancelDailySubscriptions.js

# Cancel by product ID instead of price ID
node src/scripts/cancelDailySubscriptions.js --product-id prod_abc123

# Show help
node src/scripts/cancelDailySubscriptions.js --help
```

## Configuration

You can modify these variables at the top of the script:

```javascript
const DRY_RUN = false; // Set to true for testing
const BATCH_SIZE = 100; // Number of subscriptions to process at once
```

## How It Works

1. **Environment Validation**: Checks that required environment variables are set
2. **Subscription Fetching**: Retrieves all subscriptions for the daily plan price ID
3. **Filtering**: Filters to only active and trialing subscriptions
4. **Summary Display**: Shows a list of subscriptions that would be canceled
5. **User Confirmation**: Asks for confirmation before proceeding (unless dry run)
6. **Batch Processing**: Cancels subscriptions in batches to respect rate limits
7. **Results Summary**: Provides a detailed summary of successful and failed cancellations

## Safety Features

### Dry Run Mode
- Set `DRY_RUN = true` or use `--dry-run` flag
- Shows exactly what would be canceled without making any changes
- Perfect for testing and verification

### User Confirmation
- Requires typing "yes" to confirm cancellation
- Shows detailed summary before proceeding
- Can be aborted at any time

### Error Handling
- Continues processing even if some cancellations fail
- Provides detailed error messages for failed cancellations
- Doesn't crash on individual failures

## Sample Output

```
ℹ️ [2024-01-15T10:30:00.000Z] Starting daily plan subscription cancellation process...
ℹ️ [2024-01-15T10:30:00.001Z] Using Stripe Secret Key: sk_live_abcd...
ℹ️ [2024-01-15T10:30:00.002Z] Daily Plan Price ID: price_daily123
ℹ️ [2024-01-15T10:30:00.003Z] Dry Run Mode: OFF
ℹ️ [2024-01-15T10:30:01.234Z] Fetching all subscriptions for price ID: price_daily123
ℹ️ [2024-01-15T10:30:02.456Z] Found 25 total subscriptions for daily plan
ℹ️ [2024-01-15T10:30:02.457Z] Found 15 active subscriptions to cancel

=== SUBSCRIPTION SUMMARY ===
1. ID: sub_abc123 | Status: active | Customer: <EMAIL> | Created: 2024-01-10T08:00:00.000Z
2. ID: sub_def456 | Status: active | Customer: <EMAIL> | Created: 2024-01-12T10:30:00.000Z
...
============================

Are you sure you want to cancel 15 daily plan subscriptions? (yes/no): yes
ℹ️ [2024-01-15T10:30:05.123Z] Starting cancellation of 15 subscriptions...
✅ [2024-01-15T10:30:06.234Z] Successfully canceled subscription: sub_abc123 (Customer: <EMAIL>)
✅ [2024-01-15T10:30:07.345Z] Successfully canceled subscription: sub_def456 (Customer: <EMAIL>)
...

=== CANCELLATION RESULTS ===
Total processed: 15
✅ Successful: 15
ℹ️ Failed: 0
============================

✅ [2024-01-15T10:30:10.000Z] Daily plan subscription cancellation process completed.
```

## Troubleshooting

### Common Issues

1. **"STRIPE_SECRET_KEY environment variable is not set"**
   - Make sure your `.env` file contains `STRIPE_SECRET_KEY=sk_live_...`
   - Ensure you're running the script from the correct directory

2. **"PRICE_ID_DAILY environment variable is not set"**
   - Add `PRICE_ID_DAILY=price_...` to your `.env` file
   - You can find this in your Stripe dashboard under Products

3. **Rate limiting errors**
   - The script automatically handles rate limits with delays
   - You can increase `BATCH_SIZE` if you're getting rate limited (decrease it if problems persist)

4. **"No active daily plan subscriptions found"**
   - This means there are no active subscriptions to cancel
   - Verify your price ID is correct
   - Check if subscriptions are already canceled

### Getting Your Price ID

1. Go to your Stripe Dashboard
2. Navigate to Products → [Your Daily Plan Product]
3. Copy the Price ID (starts with `price_`)
4. Add it to your `.env` file as `PRICE_ID_DAILY=price_your_id_here`

### Getting Your Product ID (Alternative)

If you want to cancel by product ID instead:
1. Go to your Stripe Dashboard
2. Navigate to Products → [Your Daily Plan Product]
3. Copy the Product ID (starts with `prod_`)
4. Use: `node src/scripts/cancelDailySubscriptions.js --product-id prod_your_id_here`

## Security Notes

- ⚠️ **This script will permanently cancel subscriptions**
- ⚠️ **Always run a dry run first**
- ⚠️ **Keep your Stripe secret key secure**
- ⚠️ **Consider backing up subscription data before running**

## Support

If you encounter any issues:
1. Check the error messages in the console
2. Verify your environment variables
3. Try running with `--dry-run` first
4. Check your Stripe dashboard for any API issues 