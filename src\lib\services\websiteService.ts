import apiClient from '@/lib/api-client';

export interface WebsiteInfo {
    title: string;
    description: string;
    keywords: string[];
    language: string;
    mainContent: string;
    headings: string[];
    links: string[];
    images: string[];
    metaTags: {
        [key: string]: string;
    };
    openGraph: {
        [key: string]: string;
    };
    twitter: {
        [key: string]: string;
    };
    schema: any[];
}

export const extractWebsiteInfo = async (url: string): Promise<WebsiteInfo> => {
    try {
        // Ensure URL has protocol
        if (!url.startsWith('http')) {
            url = `https://${url}`;
        }

        // Use the backend API to extract website information
        const response = await apiClient.post('/process-domain', {
            url
        });

        if (response.data.error) {
            throw new Error(response.data.error);
        }

        return response.data.websiteInfo;
    } catch (error) {
        console.error('Error extracting website info:', error);
        throw new Error('Failed to extract website information: ' + error.message);
    }
};