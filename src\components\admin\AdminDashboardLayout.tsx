import React from 'react';
import { Outlet, useNavigate, Link } from 'react-router-dom';
import { useAdmin } from '../../contexts/AdminContext';
import {
    LayoutDashboard,
    Users,
    Globe,
    FileText,
    Settings,
    LogOut,
    CreditCard
} from 'lucide-react';
import { Button } from '../ui/button';

const AdminDashboardLayout: React.FC = () => {
    const { admin, logout } = useAdmin();
    const navigate = useNavigate();

    const handleLogout = async () => {
        await logout();
        navigate('/admin/login');
    };

    const menuItems = [
        { icon: <LayoutDashboard className="w-4 h-4" />, label: 'Dashboard', path: '/admin/dashboard' },
        { icon: <Users className="w-4 h-4" />, label: 'Users', path: '/admin/users' },
        { icon: <Globe className="w-4 h-4" />, label: 'Domains', path: '/admin/domains' },
        { icon: <FileText className="w-4 h-4" />, label: 'Articles', path: '/admin/articles' },
        { icon: <CreditCard className="w-4 h-4" />, label: 'Transactions', path: '/admin/transactions' },
        { icon: <Settings className="w-4 h-4" />, label: 'Settings', path: '/admin/settings' },
    ];

    return (
        <div className="min-h-screen bg-gray-100 flex">
            {/* Sidebar */}
            <aside className="w-64 bg-white shadow-md">
                <div className="p-6">
                    <h2 className="text-2xl font-bold text-gray-800">Admin Panel</h2>
                    <p className="text-sm text-gray-600 mt-1">{admin?.fullName}</p>
                </div>
                <nav className="mt-6">
                    {menuItems.map((item) => (
                        <Link
                            key={item.path}
                            to={item.path}
                            className="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors"
                        >
                            {item.icon}
                            <span className="ml-3">{item.label}</span>
                        </Link>
                    ))}
                    <Button
                        variant="ghost"
                        className="flex items-center w-full px-6 py-3 text-gray-700 hover:bg-gray-100"
                        onClick={handleLogout}
                    >
                        <LogOut className="w-4 h-4" />
                        <span className="ml-3">Logout</span>
                    </Button>
                </nav>
            </aside>

            {/* Main Content */}
            <main className="flex-1 p-8">
                <Outlet />
            </main>
        </div>
    );
};

export default AdminDashboardLayout; 