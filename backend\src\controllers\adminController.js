const Admin = require('../models/Admin');
const User = require('../models/User');
const Domain = require('../models/Domain');
const Article = require('../models/Article');
const bcrypt = require('bcryptjs');
const Plan = require('../models/Plan');
const Transaction = require('../models/Transaction');

const adminController = {
    // Admin Authentication
    async login(req, res) {
        try {
            const { email, password } = req.body;
            const admin = await Admin.findByCredentials(email, password);
            const token = await admin.generateAuthToken();
            admin.lastLogin = new Date();
            await admin.save();
            res.send({ admin, token });
        } catch (error) {
            res.status(400).send({ error: error.message });
        }
    },

    async logout(req, res) {
        try {
            req.admin.tokens = req.admin.tokens.filter(token => token.token !== req.token);
            await req.admin.save();
            res.send({ message: 'Logged out successfully' });
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    // Admin Management
    async createAdmin(req, res) {
        try {
            if (req.admin.role !== 'super-admin') {
                return res.status(403).send({ error: 'Only super admins can create new admins' });
            }
            const admin = new Admin(req.body);
            await admin.save();
            res.status(201).send(admin);
        } catch (error) {
            res.status(400).send({ error: error.message });
        }
    },

    async updateAdmin(req, res) {
        const updates = Object.keys(req.body);
        const allowedUpdates = ['fullName', 'email', 'password', 'role', 'permissions', 'isActive'];
        const isValidOperation = updates.every(update => allowedUpdates.includes(update));

        if (!isValidOperation) {
            return res.status(400).send({ error: 'Invalid updates' });
        }

        try {
            const admin = await Admin.findById(req.params.id);
            if (!admin) {
                return res.status(404).send();
            }

            if (req.admin.role !== 'super-admin' && req.admin._id.toString() !== admin._id.toString()) {
                return res.status(403).send({ error: 'Not authorized to update other admins' });
            }

            updates.forEach(update => admin[update] = req.body[update]);
            await admin.save();
            res.send(admin);
        } catch (error) {
            res.status(400).send({ error: error.message });
        }
    },

    // Dashboard Statistics
    async getDashboardStats(req, res) {
        try {
            const stats = {
                users: await User.countDocuments(),
                domains: await Domain.countDocuments(),
                articles: await Article.countDocuments(),
                admins: await Admin.countDocuments(),
                recentUsers: await User.find().sort({ createdAt: -1 }).limit(5),
                recentArticles: await Article.find().sort({ createdAt: -1 }).limit(5)
            };
            res.send(stats);
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    // User Management
    async getUsers(req, res) {
        try {
            const users = await User.find({});

            // Get counts for each user
            const usersWithCounts = await Promise.all(users.map(async (user) => {
                const userObj = user.toObject();

                // Get domain count
                const domainCount = await Domain.countDocuments({ userId: user._id });

                // Get article count
                const articleCount = await Article.countDocuments({ userId: user._id });

                // Get user's plan (you might want to adjust this based on your actual plan structure)
                const userPlan = await Plan.findOne({ userId: user._id }) || { type: 'Free' };

                return {
                    ...userObj,
                    domainCount,
                    articleCount,
                    plan: userPlan.type
                };
            }));

            res.json(usersWithCounts);
        } catch (error) {
            console.error('Error fetching users:', error);
            res.status(500).send({ error: error.message });
        }
    },

    async updateUserStatus(req, res) {
        try {
            const user = await User.findById(req.params.id);
            if (!user) {
                return res.status(404).send();
            }
            user.isActive = req.body.isActive;
            await user.save();
            res.send(user);
        } catch (error) {
            res.status(400).send({ error: error.message });
        }
    },

    async getUserDetails(req, res) {
        try {
            const user = await User.findById(req.params.id)
                .select('firstName lastName email createdAt lastLogin status avatar isActive');

            if (!user) {
                return res.status(404).send({ error: 'User not found' });
            }

            res.json(user);
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    async getUserDomains(req, res) {
        try {
            const domains = await Domain.find({ userId: req.params.id })
                .select('name url status createdAt lastUpdated')
                .sort({ createdAt: -1 });

            res.json(domains);
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    // Domain Management
    async getDomains(req, res) {
        try {
            const domains = await Domain.find().populate('userId', 'email fullName');
            res.send(domains);
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    // Article Management
    async getArticles(req, res) {
        try {
            const articles = await Article.find()
                .populate('domainId', 'url')
                .populate('userId', 'email fullName')
                .sort({ createdAt: -1 });
            res.send(articles);
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    async updateArticleStatus(req, res) {
        try {
            const article = await Article.findById(req.params.id);
            if (!article) {
                return res.status(404).send();
            }
            article.status = req.body.status;
            await article.save();
            res.send(article);
        } catch (error) {
            res.status(400).send({ error: error.message });
        }
    },

    async getDomainArticles(req, res) {
        try {
            const articles = await Article.find({ domainId: req.params.domainId })
                .select('title status createdAt updatedAt urlSlug')
                .sort({ createdAt: -1 });

            res.json(articles);
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    // Profile Management
    async getProfile(req, res) {
        try {
            const admin = await Admin.findById(req.admin._id)
                .select('-password -tokens');
            res.send(admin);
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    async updateProfile(req, res) {
        const updates = Object.keys(req.body);
        const allowedUpdates = ['username', 'email', 'fullName', 'avatar'];
        const isValidOperation = updates.every(update => allowedUpdates.includes(update));

        if (!isValidOperation) {
            return res.status(400).send({ error: 'Invalid updates' });
        }

        try {
            const admin = await Admin.findById(req.admin._id);
            if (!admin) {
                return res.status(404).send();
            }

            updates.forEach(update => admin[update] = req.body[update]);
            await admin.save();
            res.send(admin);
        } catch (error) {
            res.status(400).send({ error: error.message });
        }
    },

    async changePassword(req, res) {
        try {
            const { currentPassword, newPassword } = req.body;
            const admin = await Admin.findById(req.admin._id);

            if (!admin) {
                return res.status(404).send({ error: 'Admin not found' });
            }

            // Verify current password
            const isMatch = await bcrypt.compare(currentPassword, admin.password);
            if (!isMatch) {
                return res.status(400).send({ error: 'Current password is incorrect' });
            }

            // Update password
            admin.password = newPassword;

            // Clear all tokens to force re-login with new password
            admin.tokens = [];

            await admin.save();

            res.send({ message: 'Password updated successfully. Please login again with your new password.' });
        } catch (error) {
            res.status(400).send({ error: error.message });
        }
    },

    // Verify Token
    async verifyToken(req, res) {
        try {
            // req.admin is already set by the adminAuth middleware
            const admin = await Admin.findById(req.admin._id)
                .select('-password -tokens');

            if (!admin) {
                throw new Error();
            }

            res.send(admin);
        } catch (error) {
            res.status(401).send({ error: 'Please authenticate' });
        }
    },

    async getRecentUsers(req, res) {
        try {
            const recentUsers = await User.find()
                .select('firstName lastName email createdAt')
                .sort({ createdAt: -1 })
                .limit(5);

            res.json(recentUsers);
        } catch (error) {
            console.error('Error fetching recent users:', error);
            res.status(500).json({ error: error.message });
        }
    },

    async getStats(req, res) {
        try {
            const [totalUsers, totalDomains, totalArticles, activeAdmins] = await Promise.all([
                User.countDocuments(),
                Domain.countDocuments(),
                Article.countDocuments(),
                Admin.countDocuments({ status: 'active' })
            ]);

            res.json({
                totalUsers,
                totalDomains,
                totalArticles,
                activeAdmins
            });
        } catch (error) {
            res.status(500).send({ error: error.message });
        }
    },

    async getRecentArticles(req, res) {
        try {
            const recentArticles = await Article.find()
                .select('title status createdAt')
                .sort({ createdAt: -1 })
                .limit(5);

            res.json(recentArticles);
        } catch (error) {
            console.error('Error fetching recent articles:', error);
            res.status(500).json({ error: error.message });
        }
    },

    async getArticleBySlug(req, res) {
        try {
            const article = await Article.findOne({ urlSlug: req.params.slug })
                .populate({
                    path: 'domainId',
                    model: 'Domain',
                    select: '_id name url designSettings brandInfo articleSettings hostingSettings'
                })
                .populate({
                    path: 'userId',
                    model: 'User',
                    select: '_id firstName lastName avatar'
                });

            if (!article) {
                return res.status(404).send({ error: 'Article not found' });
            }

            // Transform the response to match the expected format
            const transformedArticle = article.toObject();

            // Ensure all required fields are present
            if (transformedArticle.domainId) {
                transformedArticle.domainId.designSettings = transformedArticle.domainId.designSettings || {};
                transformedArticle.domainId.designSettings.colors = transformedArticle.domainId.designSettings.colors || {
                    brand: '#3c484c',
                    accent: '#cec4c0'
                };
            }

            res.json(transformedArticle);
        } catch (error) {
            console.error('Error fetching article:', error);
            res.status(500).send({ error: error.message });
        }
    },

    async deleteUser(req, res) {
        try {
            const user = await User.findById(req.params.id);
            if (!user) {
                return res.status(404).send({ error: 'User not found' });
            }

            // Delete associated data
            await Domain.deleteMany({ userId: user._id });
            await Article.deleteMany({ userId: user._id });
            await Plan.deleteMany({ userId: user._id });

            // Delete the user
            await User.findByIdAndDelete(req.params.id);

            res.status(200).send({ message: 'User and associated data deleted successfully' });
        } catch (error) {
            console.error('Error deleting user:', error);
            res.status(500).send({ error: error.message });
        }
    },

    async updateUser(req, res) {
        try {
            const { firstName, lastName, email, isActive } = req.body;

            // Check if email is being changed and if it's already in use
            if (email) {
                const existingUser = await User.findOne({ email, _id: { $ne: req.params.id } });
                if (existingUser) {
                    return res.status(400).send({ error: 'Email already in use' });
                }
            }

            const user = await User.findByIdAndUpdate(
                req.params.id,
                {
                    firstName,
                    lastName,
                    email,
                    isActive,
                    updatedAt: Date.now()
                },
                { new: true, runValidators: true }
            );

            if (!user) {
                return res.status(404).send({ error: 'User not found' });
            }

            res.json(user);
        } catch (error) {
            console.error('Error updating user:', error);
            res.status(500).send({ error: error.message });
        }
    },

    // Transaction Management
    async getAllTransactions(req, res) {
        try {
            const transactions = await Transaction.find()
                .populate('userId', 'firstName lastName email avatar')
                .populate('domainId', 'name url')
                .sort({ createdAt: -1 });

            res.json(transactions);
        } catch (error) {
            console.error('Error fetching transactions:', error);
            res.status(500).send({ error: error.message });
        }
    },

    async getTransactionById(req, res) {
        try {
            const transaction = await Transaction.findById(req.params.id)
                .populate('userId', 'firstName lastName email')
                .populate('domainId', 'name url');

            if (!transaction) {
                return res.status(404).send({ error: 'Transaction not found' });
            }

            res.json(transaction);
        } catch (error) {
            console.error('Error fetching transaction:', error);
            res.status(500).send({ error: error.message });
        }
    },

    async getUserTransactions(req, res) {
        try {
            const transactions = await Transaction.find({ userId: req.params.userId })
                .populate('userId', 'firstName lastName email')
                .populate('domainId', 'name url')
                .sort({ createdAt: -1 });

            res.json(transactions);
        } catch (error) {
            console.error('Error fetching user transactions:', error);
            res.status(500).send({ error: error.message });
        }
    },

    async generateInvoice(req, res) {
        try {
            const transaction = await Transaction.findById(req.params.id);

            if (!transaction) {
                return res.status(404).send({ error: 'Transaction not found' });
            }

            // If transaction already has an invoice URL, return it
            if (transaction.invoiceUrl) {
                return res.json({ invoiceUrl: transaction.invoiceUrl });
            }

            // Otherwise, we'd need to generate one via Stripe
            // This would typically call Stripe's API to get or create an invoice
            // For now, we'll just return a placeholder or error
            return res.status(400).send({ error: 'Invoice not available for this transaction' });
        } catch (error) {
            console.error('Error generating invoice:', error);
            res.status(500).send({ error: error.message });
        }
    }
};

module.exports = adminController; 