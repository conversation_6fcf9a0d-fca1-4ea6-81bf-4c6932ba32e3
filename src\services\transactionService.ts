import apiClient from '@/lib/api-client';
import adminApiClient from '@/lib/admin-api-client';
import axios from 'axios';

export interface Transaction {
    _id: string;
    userId: {
        _id: string;
        firstName: string;
        lastName: string;
        email: string;
        avatar?: string;
    } | string;
    domainId: {
        _id: string;
        name: string;
        url: string;
    } | string;
    stripeSessionId: string;
    stripeCustomerId?: string;
    stripeSubscriptionId?: string;
    stripePaymentIntentId?: string;
    stripeChargeId?: string;
    invoiceUrl?: string;
    transactionType: 'New Subscription' | 'Recurring Subscription';
    amount: number;
    currency: string;
    planType: 'Monthly' | 'Yearly' | 'Free';
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    paymentMethod: string;
    billingDetails?: {
        name?: string;
        email?: string;
        address?: {
            line1?: string;
            line2?: string;
            city?: string;
            state?: string;
            postal_code?: string;
            country?: string;
        };
    };
    metadata?: Record<string, any>;
    createdAt: string;
    updatedAt: string;
}

class TransactionService {
    private baseUrl = '/payments';

    /**
     * Get all transactions for the current user
     * @returns List of transactions
     */
    async getTransactions(): Promise<Transaction[]> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/transactions`);
            return response.data;
        } catch (error) {
            console.error('Error fetching transactions:', error);
            return [];
        }
    }

    /**
     * Get details of a specific transaction
     * @param transactionId The ID of the transaction
     * @returns Transaction details
     */
    async getTransaction(transactionId: string): Promise<Transaction | null> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/transactions/${transactionId}`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching transaction ${transactionId}:`, error);
            return null;
        }
    }

    /**
     * Verify the payment status directly with Stripe
     * @param sessionId The Stripe session ID to verify
     * @returns The verified payment status and payment details
     */
    async verifyStripePaymentStatus(sessionId: string): Promise<{
        status: 'completed' | 'pending' | 'failed' | 'refunded';
        stripeStatus: string;
        stripePaymentStatus: string;
        paymentIntentId?: string;
        chargeId?: string;
    }> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/verify-payment/${sessionId}`);
            return {
                status: response.data.status,
                stripeStatus: response.data.stripeStatus,
                stripePaymentStatus: response.data.stripePaymentStatus,
                paymentIntentId: response.data.paymentIntentId,
                chargeId: response.data.chargeId
            };
        } catch (error) {
            console.error(`Error verifying payment status for session ${sessionId}:`, error);
            return {
                status: 'pending',
                stripeStatus: 'unknown',
                stripePaymentStatus: 'unknown'
            };
        }
    }

    /**
     * Sync all transaction statuses with Stripe
     * @returns Updated transactions with correct statuses
     */
    async syncTransactionStatuses(): Promise<Transaction[]> {
        try {
            const response = await apiClient.post(`${this.baseUrl}/sync-transactions`);
            return response.data;
        } catch (error) {
            console.error('Error syncing transaction statuses:', error);

            // If the backend endpoint fails, fall back to getting transactions
            const transactions = await this.getTransactions();

            // Force completed status for transactions with stripeSubscriptionId
            return transactions.map(transaction => {
                if (transaction.stripeSubscriptionId && transaction.status === 'pending') {
                    return { ...transaction, status: 'completed' as 'completed' };
                }
                return transaction;
            });
        }
    }

    /**
     * Format currency amount for display
     * @param amount The amount to format
     * @param currency The currency code
     * @returns Formatted currency string
     */
    formatCurrency(amount: number, currency: string = 'usd'): string {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency.toUpperCase(),
        }).format(amount);
    }

    /**
     * Format date for display
     * @param dateString The date string to format
     * @returns Formatted date string
     */
    formatDate(dateString: string): string {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        }).format(date);
    }

    /**
     * Format date for display without time
     * @param dateString The date string to format
     * @returns Formatted date string without time
     */
    formatDateNoTime(dateString: string): string {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        }).format(date);
    }

    /**
     * Update plan after a prorated payment is completed
     * @param sessionId The Stripe session ID for the prorated payment
     * @returns Success status
     */
    async updatePlanAfterProration(sessionId: string): Promise<{ success: boolean }> {
        try {
            const response = await apiClient.post(`${this.baseUrl}/update-plan-after-proration`, { sessionId });
            return response.data;
        } catch (error) {
            console.error('Error updating plan after proration:', error);
            throw error;
        }
    }

    /**
     * Get Stripe dashboard URL for a payment intent
     * @param paymentIntentId Payment intent ID
     * @returns Stripe dashboard URL
     */
    getStripePaymentUrl(paymentIntentId: string): string {
        return `https://dashboard.stripe.com/payments/${paymentIntentId}`;
    }

    /**
     * Get Stripe dashboard URL for a charge
     * @param chargeId Charge ID
     * @returns Stripe dashboard URL
     */
    getStripeChargeUrl(chargeId: string): string {
        return `https://dashboard.stripe.com/payments/${chargeId}`;
    }

    /**
     * Get Stripe dashboard URL for a subscription
     * @param subscriptionId Subscription ID
     * @returns Stripe dashboard URL
     */
    getStripeSubscriptionUrl(subscriptionId: string): string {
        return `https://dashboard.stripe.com/subscriptions/${subscriptionId}`;
    }

    /**
     * Get Stripe dashboard URL for a customer
     * @param customerId Customer ID
     * @returns Stripe dashboard URL
     */
    getStripeCustomerUrl(customerId: string): string {
        return `https://dashboard.stripe.com/customers/${customerId}`;
    }

    /**
     * Open the Stripe payment intent page in a new tab
     * @param paymentIntentId Payment intent ID
     */
    openStripePaymentIntent(paymentIntentId: string): void {
        window.open(this.getStripePaymentUrl(paymentIntentId), '_blank');
    }

    /**
     * Open the Stripe charge page in a new tab
     * @param chargeId Charge ID
     */
    openStripeCharge(chargeId: string): void {
        window.open(this.getStripeChargeUrl(chargeId), '_blank');
    }

    /**
     * Open the Stripe subscription page in a new tab
     * @param subscriptionId Subscription ID
     */
    openStripeSubscription(subscriptionId: string): void {
        window.open(this.getStripeSubscriptionUrl(subscriptionId), '_blank');
    }

    /**
     * Open the Stripe customer page in a new tab
     * @param customerId Customer ID
     */
    openStripeCustomer(customerId: string): void {
        window.open(this.getStripeCustomerUrl(customerId), '_blank');
    }

    /**
     * Get the invoice URL for a transaction
     * @param transactionId The ID of the transaction
     * @returns The invoice URL if available
     */
    async getInvoiceUrl(transactionId: string): Promise<string | null> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/transactions/${transactionId}/invoice`);
            return response.data.invoiceUrl;
        } catch (error) {
            console.error(`Error fetching invoice URL for transaction ${transactionId}:`, error);
            return null;
        }
    }

    /**
     * Open the invoice URL in a new tab
     * @param invoiceUrl Invoice URL
     */
    openInvoice(invoiceUrl: string): void {
        window.open(invoiceUrl, '_blank');
    }

    /**
     * Admin methods for transaction management
     */
    async getAllTransactionsAdmin(): Promise<Transaction[]> {
        try {
            const response = await adminApiClient.get('/admin/transactions');
            return response.data;
        } catch (error) {
            console.error('Error fetching all transactions:', error);
            return [];
        }
    }

    async getTransactionByIdAdmin(transactionId: string): Promise<Transaction | null> {
        try {
            const response = await adminApiClient.get(`/admin/transactions/${transactionId}`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching transaction ${transactionId}:`, error);
            return null;
        }
    }

    async getUserTransactionsAdmin(userId: string): Promise<Transaction[]> {
        try {
            const response = await adminApiClient.get(`/admin/users/${userId}/transactions`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching transactions for user ${userId}:`, error);
            return [];
        }
    }

    async generateInvoiceAdmin(transactionId: string): Promise<string | null> {
        try {
            const response = await adminApiClient.get(`/admin/transactions/${transactionId}/invoice`);
            return response.data.invoiceUrl;
        } catch (error) {
            console.error(`Error generating invoice for transaction ${transactionId}:`, error);
            return null;
        }
    }
}

export const transactionService = new TransactionService();