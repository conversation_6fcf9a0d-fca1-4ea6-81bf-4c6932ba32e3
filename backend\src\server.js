require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const connectDB = require('./config/db');
const articleRoutes = require('./routes/articleRoutes');
const domainRoutes = require('./routes/domainRoutes');
const authRoutes = require('./routes/auth');
const scraperService = require('./services/scraperService');
const gptService = require('./services/gptService');
const autoArticleService = require('./services/autoArticleService');
const Article = require('./models/Article');
const Domain = require('./models/Domain');
const User = require('./models/User');
const Admin = require('./models/Admin');
const bodyParser = require('body-parser');
const { protect } = require('./middleware/auth');
const adminRoutes = require('./routes/adminRoutes');
const paymentRoutes = require('./routes/paymentRoutes');
const passport = require('passport');
const session = require('express-session');
const ensureLogsDirectory = require('./utils/ensureLogsDir');
const fs = require('fs');
const path = require('path');

// Initialize express app
const app = express();

// Ensure logs directory exists
const logsDir = ensureLogsDirectory();

// Set up request logging with Morgan
if (logsDir) {
    // Create a write stream for access logs
    const accessLogStream = fs.createWriteStream(
        path.join(logsDir, `access-${new Date().toISOString().split('T')[0]}.log`),
        { flags: 'a' }
    );

    // Use morgan to log requests to the file
    app.use(morgan('combined', { stream: accessLogStream }));

    // Also log to console
    app.use(morgan('dev'));
} else {
    // Just log to console if logs directory couldn't be created
    app.use(morgan('dev'));
}

// Connect to MongoDB
connectDB();

// CORS Configuration
const corsOptions = {
    origin: ['http://localhost:8080', 'http://localhost:3000'],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
};

// Middleware
app.use(cors(corsOptions));

// Add this before any body parsers
// Special handling for Stripe webhook
app.use('/api/payments/webhook', express.raw({ type: 'application/json' }));

// Add handler for the incorrect webhook path that Stripe is using
app.use('/api/v1/subscriptions/webhook', express.raw({ type: 'application/json' }));

// Then add regular body parsers
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

// Configure express-session middleware
app.use(session({
    secret: process.env.SESSION_SECRET || 'blogbuster-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: process.env.NODE_ENV === 'production' }
}));

// Initialize Passport and restore authentication state from session
app.use(passport.initialize());
app.use(passport.session());

// Public routes for blog preview
app.get('/api/articles/public/:domainId', async (req, res) => {
    try {
        const articles = await Article.find({ domainId: req.params.domainId });
        res.json(articles);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch articles' });
    }
});

// Public route for getting article by slug
app.get('/api/articles/by-slug/:slug', async (req, res) => {
    try {
        const article = await Article.findOne({ urlSlug: req.params.slug });
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }

        // Transform the article to match frontend expectations
        const transformedArticle = {
            id: article._id.toString(),
            title: article.title,
            description: article.description,
            content: article.content,
            tag: article.tag,
            status: article.status,
            author: article.author,
            date: article.date,
            image: article.image,
            video: article.video,
            urlSlug: article.urlSlug,
            metaDescription: article.metaDescription,
            excerpt: article.excerpt,
            keywords: article.keywords,
            sourceUrl: article.sourceUrl,
            domainId: article.domainId?.toString(),
            metadata: article.metadata,
            createdAt: article.createdAt
        };

        res.json(transformedArticle);
    } catch (error) {
        console.error('Error fetching article by slug:', error);
        res.status(500).json({ message: 'Error fetching article', error: error.message });
    }
});

app.get('/api/articles', async (req, res) => {
    try {
        const { domainId } = req.query;
        if (!domainId) {
            return res.status(400).json({ error: 'Domain ID is required' });
        }
        const articles = await Article.find({ domainId });
        res.json(articles);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch articles' });
    }
});

// Public domain routes
app.get('/api/domains/public/:id', async (req, res) => {
    try {
        const domain = await Domain.findById(req.params.id);
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }
        res.json(domain);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch domain' });
    }
});

app.get('/api/domains/by-url/:url', async (req, res) => {
    try {
        const url = req.params.url;
        // Split the URL into subdomain and domain parts
        const parts = url.split('.');
        const subdomain = parts[0];
        const domain = parts.slice(1).join('.');

        const domainDoc = await Domain.findOne({
            'hostingSettings.domain': domain,
            'hostingSettings.subdomain': subdomain
        });

        if (!domainDoc) {
            return res.status(404).json({ error: 'Domain not found' });
        }
        res.json(domainDoc);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch domain' });
    }
});

// Protected routes
app.use('/api/auth', authRoutes);
app.use('/api/articles', protect, articleRoutes); // Changed from /api/articles/manage to /api/articles
app.use('/api/domains/manage', protect, domainRoutes); // Changed path to avoid conflict
app.use('/api/payments', protect, paymentRoutes); // Add payment routes

// Handle the incorrect webhook path that Stripe is using
app.post('/api/v1/subscriptions/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
    console.log('⚠️ Webhook received at incorrect path: /api/v1/subscriptions/webhook');
    console.log('🔄 Processing webhook directly');

    try {
        // Process the webhook directly
        const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY || 'sk_test_dummy');
        const payload = req.body;
        const sig = req.headers['stripe-signature'];
        let event;

        try {
            // Get webhook secret from environment variable
            const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

            // Log webhook secret status
            console.log('🔑 Webhook secret:', webhookSecret ? 'Configured' : 'Not configured');

            // Check if webhook secret is available
            if (!webhookSecret) {
                console.warn('⚠️ STRIPE_WEBHOOK_SECRET is not set. Using test mode for webhook.');
                // In development mode, just parse the payload
                event = JSON.parse(payload.toString());
            } else {
                // In production, verify the signature
                console.log('🔐 Attempting to verify webhook signature...');
                event = stripe.webhooks.constructEvent(payload, sig, webhookSecret);
                console.log('✅ Signature verification successful!');
            }

            console.log('📝 Event type:', event.type);
            console.log('🆔 Event ID:', event.id);

            // Create webhook log file
            const logWebhookEvent = (eventType, data, isError = false) => {
                try {
                    const timestamp = new Date().toISOString();
                    const logDir = path.join(__dirname, '..', '..', 'logs');

                    // Create logs directory if it doesn't exist
                    if (!fs.existsSync(logDir)) {
                        fs.mkdirSync(logDir, { recursive: true });
                    }

                    // Format for console log
                    const logPrefix = isError ? '❌ ERROR' : '✅ EVENT';
                    console.log(`${logPrefix} [${timestamp}] ${eventType}: ${JSON.stringify(data, null, 2)}`);

                    // Write to log file
                    const logFile = path.join(logDir, `webhook-${new Date().toISOString().split('T')[0]}.log`);
                    const logEntry = `[${timestamp}] ${isError ? 'ERROR' : 'EVENT'} ${eventType}: ${JSON.stringify(data)}\n`;

                    fs.appendFileSync(logFile, logEntry);
                } catch (err) {
                    console.error('❌ Error writing to webhook log:', err);
                }
            };

            // Handle checkout.session.completed event
            if (event.type === 'checkout.session.completed') {
                const session = event.data.object;
                console.log('💰 Checkout session completed:', session.id);
                console.log('💰 Session mode:', session.mode);
                console.log('💰 Session has subscription:', !!session.subscription);
                console.log('💰 Session isProrated metadata:', session.metadata?.isProrated);

                logWebhookEvent('checkout_session_completed', {
                    sessionId: session.id,
                    paymentStatus: session.payment_status,
                    mode: session.mode,
                    metadata: session.metadata
                });

                // Extract metadata
                const { userId, domainId, planType, isProrated } = session.metadata || {};

                // Log the extracted plan type to verify it's correct
                console.log('📋 Extracted plan type from metadata:', planType);
                console.log('📋 Is prorated payment:', isProrated);

                // If this is a prorated payment (payment mode), handle it specially
                if (session.mode === 'payment' && isProrated === 'true') {
                    console.log('🔄 Processing prorated payment in webhook');

                    // For prorated payments, we need to:
                    // 1. Find the transaction
                    // 2. Update it to completed
                    // 3. Trigger the subscription update

                    const Transaction = require('./models/Transaction');

                    Transaction.findOne({ stripeSessionId: session.id })
                        .then(transaction => {
                            if (!transaction) {
                                console.error('❌ Transaction not found for prorated payment session:', session.id);
                                return;
                            }

                            console.log('✅ Found prorated payment transaction:', transaction._id);

                            // Update transaction status
                            transaction.status = 'completed';
                            if (session.payment_intent) {
                                transaction.stripePaymentIntentId = session.payment_intent;
                            }

                            return transaction.save().then(() => {
                                console.log('📝 Prorated payment transaction updated to completed');

                                // Now trigger the subscription update by calling the process function
                                console.log('🎯 Triggering subscription update for prorated payment');

                                // Import the processCompletedProratedPayment function and call it
                                const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY || 'sk_test_dummy');
                                const Domain = require('./models/Domain');
                                const Plan = require('./models/Plan');

                                // Call the subscription update logic directly
                                processCompletedProratedPaymentInWebhook(transaction, stripe, Domain, Plan)
                                    .then(() => {
                                        console.log('✅ Prorated payment subscription update completed');
                                    })
                                    .catch(error => {
                                        console.error('❌ Error updating subscription for prorated payment:', error);
                                    });
                            });
                        })
                        .catch(error => {
                            console.error('❌ Error processing prorated payment transaction:', error);
                        });

                    // Return early for prorated payments to avoid the subscription mode logic below
                    return;
                }

                if (domainId) {
                    // Find the domain
                    const Domain = require('./models/Domain');
                    const Plan = require('./models/Plan');
                    const Transaction = require('./models/Transaction');
                    const User = require('./models/User');
                    const emailService = require('./utils/emailService');

                    Domain.findById(domainId)
                        .then(domain => {
                            if (!domain) {
                                console.error('❌ Domain not found with ID:', domainId);
                                logWebhookEvent('domain_not_found', { domainId }, true);
                                return;
                            }

                            console.log('🌐 Found domain:', domain.name, '(ID:', domain._id, ')');
                            logWebhookEvent('domain_found', {
                                domainId: domain._id,
                                name: domain.name,
                                userId: domain.userId
                            });

                            // Only update domain subscription if payment is successful
                            if (session.payment_status === 'paid') {
                                console.log('💰 Payment successful, updating domain subscription');
                                logWebhookEvent('payment_successful', {
                                    domainId: domain._id,
                                    planType
                                });

                                // Update domain with subscription info
                                return Domain.findByIdAndUpdate(domainId, {
                                    subscription: {
                                        active: true,
                                        planType: planType, // Use the plan type from metadata
                                        stripeCustomerId: session.customer,
                                        stripeSubscriptionId: session.subscription,
                                        updatedAt: new Date(),
                                        credits: planType === 'Daily' ? 1 :
                                            planType === 'Monthly' ? 30 :
                                                planType === 'Yearly' ? 365 : 0,
                                        canceledAt: domain.subscription?.canceledAt,
                                        expiresAt: domain.subscription?.expiresAt
                                    }
                                }, { new: true }).then((updatedDomain) => {
                                    console.log('✅ Domain subscription updated successfully:', updatedDomain.subscription);
                                    logWebhookEvent('domain_subscription_updated', {
                                        domainId: domain._id,
                                        planType,
                                        subscription: updatedDomain.subscription
                                    });

                                    // Update user plan
                                    let planFeatures;
                                    let dbPlanType;
                                    let endDate;

                                    if (planType === 'Monthly') {
                                        planFeatures = { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true };
                                        dbPlanType = 'Pro';
                                        endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
                                    } else if (planType === 'Yearly') {
                                        planFeatures = { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true };
                                        dbPlanType = 'Enterprise';
                                        endDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 365 days
                                    } else if (planType === 'Daily') {
                                        planFeatures = { maxDomains: 2, maxArticlesPerMonth: 5, customBranding: true };
                                        dbPlanType = 'Basic';
                                        endDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day
                                    } else {
                                        planFeatures = { maxDomains: 1, maxArticlesPerMonth: 10, customBranding: false };
                                        dbPlanType = 'Free';
                                        endDate = null;
                                    }

                                    return Plan.findOneAndUpdate(
                                        { userId: domain.userId },
                                        {
                                            type: dbPlanType,
                                            displayType: planType,
                                            features: planFeatures,
                                            startDate: new Date(),
                                            endDate: endDate,
                                            status: 'active'
                                        },
                                        { upsert: true, new: true }
                                    );
                                }).then(plan => {
                                    console.log('✅ User plan updated successfully:', plan.type);
                                    logWebhookEvent('user_plan_updated', {
                                        userId: domain.userId,
                                        planType: plan.type,
                                        displayType: plan.displayType
                                    });

                                    // Update transaction status
                                    return Transaction.findOneAndUpdate(
                                        { stripeSessionId: session.id },
                                        {
                                            status: 'completed',
                                            stripeCustomerId: session.customer,
                                            stripeSubscriptionId: session.subscription,
                                            stripePaymentIntentId: session.payment_intent,
                                            planType: planType, // Ensure planType is set correctly in the transaction
                                            metadata: {
                                                completedAt: new Date(),
                                                stripeStatus: session.status,
                                                stripePaymentStatus: session.payment_status
                                            }
                                        },
                                        { new: true, upsert: true }
                                    );
                                }).then(transaction => {
                                    console.log('✅ Transaction updated successfully:', transaction._id);
                                    logWebhookEvent('transaction_updated', {
                                        transactionId: transaction._id,
                                        status: transaction.status,
                                        planType: transaction.planType
                                    });

                                    // Send purchase confirmation email
                                    return User.findById(domain.userId).then(user => {
                                        if (user) {
                                            // First try to get invoice URL from transaction
                                            let invoiceUrl = transaction.invoiceUrl;

                                            // If no invoice URL, try to get it from Stripe
                                            if (!invoiceUrl || invoiceUrl.includes('localhost')) {
                                                // Try to get invoice URL from subscription
                                                if (session.subscription) {
                                                    try {
                                                        console.log('🔍 Attempting to fetch invoice URL directly from Stripe');

                                                        const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY || 'sk_test_dummy');

                                                        // Method 1: Get invoice URL from subscription
                                                        stripe.subscriptions.retrieve(session.subscription, {
                                                            expand: ['latest_invoice']
                                                        }).then(subscription => {
                                                            if (subscription.latest_invoice) {
                                                                const invoice = typeof subscription.latest_invoice === 'string'
                                                                    ? stripe.invoices.retrieve(subscription.latest_invoice)
                                                                    : subscription.latest_invoice;

                                                                return invoice;
                                                            }
                                                        }).then(invoice => {
                                                            if (invoice && invoice.hosted_invoice_url) {
                                                                invoiceUrl = invoice.hosted_invoice_url;
                                                                console.log('🔗 Found Stripe invoice URL:', invoiceUrl);

                                                                // Update transaction with invoice URL
                                                                return Transaction.findByIdAndUpdate(transaction._id, {
                                                                    invoiceUrl: invoiceUrl,
                                                                    metadata: {
                                                                        ...transaction.metadata,
                                                                        stripeInvoiceUrl: invoiceUrl,
                                                                        invoiceId: invoice.id,
                                                                        invoiceUpdatedAt: new Date()
                                                                    }
                                                                });
                                                            }
                                                        }).catch(err => {
                                                            console.error('❌ Error retrieving invoice from subscription:', err);
                                                        });

                                                        // Create a customer portal session as fallback
                                                        if (session.customer && (!invoiceUrl || invoiceUrl.includes('localhost'))) {
                                                            stripe.billingPortal.sessions.create({
                                                                customer: session.customer,
                                                                return_url: `${process.env.FRONTEND_URL || 'https://dashboard.stripe.com'}/dashboard/transactions`
                                                            }).then(portal => {
                                                                if (portal && portal.url) {
                                                                    invoiceUrl = portal.url;
                                                                    console.log('🔗 Created Stripe customer portal URL:', invoiceUrl);

                                                                    // Update transaction with portal URL
                                                                    return Transaction.findByIdAndUpdate(transaction._id, {
                                                                        invoiceUrl: invoiceUrl,
                                                                        metadata: {
                                                                            ...transaction.metadata,
                                                                            stripePortalUrl: invoiceUrl,
                                                                            portalCreatedAt: new Date()
                                                                        }
                                                                    });
                                                                }
                                                            }).catch(err => {
                                                                console.error('❌ Error creating customer portal:', err);
                                                            });
                                                        }
                                                    } catch (error) {
                                                        console.error('❌ Error fetching invoice URL:', error);
                                                    }
                                                }
                                            }

                                            // If still no valid invoice URL, use Stripe dashboard as fallback
                                            if (!invoiceUrl || invoiceUrl.includes('localhost')) {
                                                invoiceUrl = 'https://dashboard.stripe.com';
                                                console.log('🔄 Using Stripe dashboard as fallback URL');
                                            }

                                            // Prepare plan details with correct plan type
                                            const planDetails = {
                                                planType: planType, // Use the plan type from metadata
                                                amount: planType === 'Monthly' ? 59.99 : 159.99,
                                                invoiceUrl: invoiceUrl,
                                                metadata: {
                                                    stripeInvoiceUrl: invoiceUrl
                                                }
                                            };

                                            console.log(`📧 Sending email with plan type: ${planDetails.planType}, invoice URL: ${invoiceUrl}`);

                                            // Send email
                                            return emailService.sendPlanPurchaseEmail(user, planDetails)
                                                .then(success => {
                                                    console.log(`📧 Plan purchase email sent to ${user.email} for ${planType} plan:`, success);
                                                })
                                                .catch(error => {
                                                    console.error('❌ Error sending plan purchase email:', error);
                                                });
                                        }
                                    });
                                });
                            } else {
                                console.log('⚠️ Payment not completed for domain, not updating subscription');
                                logWebhookEvent('payment_incomplete', {
                                    domainId: domain._id,
                                    paymentStatus: session.payment_status
                                }, true);
                            }
                        })
                        .catch(err => {
                            console.error('❌ Error processing webhook:', err);
                            logWebhookEvent('webhook_processing_error', {
                                error: err.message,
                                stack: err.stack
                            }, true);
                        });
                }
            }

            // Handle invoice.payment_succeeded event (for recurring payments)
            if (event.type === 'invoice.payment_succeeded') {
                console.log('💰 Invoice payment succeeded:', event.data.object.id);
                console.log('🔄 Processing recurring payment...');

                logWebhookEvent('invoice_payment_succeeded', {
                    invoiceId: event.data.object.id,
                    subscriptionId: event.data.object.subscription,
                    amount: event.data.object.amount_paid
                });

                // Process the recurring payment directly
                try {
                    const invoice = event.data.object;
                    const Domain = require('./models/Domain');
                    const Transaction = require('./models/Transaction');

                    console.log(`🔍 Looking for domain with subscription ID: ${invoice.subscription}`);

                    // Find domain with this subscription
                    const domain = await Domain.findOne({ 'subscription.stripeSubscriptionId': invoice.subscription });

                    if (!domain) {
                        console.log(`❌ No domain found for subscription ID: ${invoice.subscription}`);

                        // Create orphaned transaction record
                        try {
                            const subscription = await stripe.subscriptions.retrieve(invoice.subscription);

                            if (subscription && subscription.metadata) {
                                const { userId, domainId, planType } = subscription.metadata;

                                console.log(`✅ Creating orphaned transaction record for subscription ${invoice.subscription}`);

                                const detectedPlanType = planType || 'Unknown';

                                const orphanedTransaction = new Transaction({
                                    userId: userId || null,
                                    domainId: domainId || null,
                                    stripeSessionId: `orphaned_recurring_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                                    stripeSubscriptionId: invoice.subscription,
                                    stripeCustomerId: subscription.customer,
                                    transactionType: 'Recurring Subscription',
                                    planType: detectedPlanType,
                                    status: 'completed',
                                    amount: invoice.amount_paid / 100,
                                    currency: invoice.currency,
                                    invoiceUrl: invoice.hosted_invoice_url,
                                    metadata: {
                                        invoiceId: invoice.id,
                                        orphaned: true,
                                        reason: 'Domain subscription data not found',
                                        processedViaServerJs: true,
                                        refreshedAt: new Date()
                                    }
                                });

                                await orphanedTransaction.save();
                                console.log(`✅ Orphaned recurring transaction created: ${orphanedTransaction._id}`);
                            }
                        } catch (orphanedError) {
                            console.error('❌ Error creating orphaned transaction:', orphanedError);
                        }
                        return;
                    }

                    // Domain found - process normally
                    console.log(`✅ Found domain: ${domain.name} (${domain._id})`);

                    // Set credits based on plan type
                    let credits = 0;
                    if (domain.subscription.planType === 'Daily') {
                        credits = 1;
                    } else if (domain.subscription.planType === 'Monthly') {
                        credits = 30;
                    } else if (domain.subscription.planType === 'Yearly') {
                        credits = 365;
                    }

                    // Update domain credits
                    await Domain.findByIdAndUpdate(domain._id, {
                        'subscription.credits': credits,
                        'subscription.updatedAt': new Date()
                    });

                    console.log(`✅ Credits refreshed for domain ${domain._id}: ${credits} credits`);

                    // Create recurring transaction record
                    const recurringTransaction = new Transaction({
                        userId: domain.userId,
                        domainId: domain._id,
                        stripeSessionId: `recurring_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                        stripeSubscriptionId: invoice.subscription,
                        stripeCustomerId: domain.subscription.stripeCustomerId,
                        transactionType: 'Recurring Subscription',
                        planType: domain.subscription.planType,
                        status: 'completed',
                        amount: invoice.amount_paid / 100,
                        currency: invoice.currency,
                        invoiceUrl: invoice.hosted_invoice_url,
                        metadata: {
                            invoiceId: invoice.id,
                            creditsRefreshed: credits,
                            processedViaServerJs: true,
                            refreshedAt: new Date()
                        }
                    });

                    await recurringTransaction.save();
                    console.log(`✅ Recurring transaction created: ${recurringTransaction._id}`);

                } catch (error) {
                    console.error('❌ Error processing invoice.payment_succeeded:', error);
                }
            }

            // Return a 200 response to acknowledge receipt of the event
            res.status(200).send('Webhook received');
        } catch (err) {
            console.error('❌ Webhook Error:', err.message);
            return res.status(400).send(`Webhook Error: ${err.message}`);
        }
    } catch (error) {
        console.error('❌ Error in webhook handler:', error);
        return res.status(500).send('Server Error');
    }
});

// Special case for admin login - no auth required
app.post('/api/admin/login', require('./controllers/adminController').login);

// Admin routes - protected by adminAuth middleware
app.use('/api/admin', adminRoutes);

// Domain processing route
app.post('/api/process-domain', protect, async (req, res) => {
    const { url } = req.body;
    let domain, websiteData;

    try {
        // Check if domain exists for the current user
        domain = await Domain.findOne({ url, userId: req.user.id });
        const isExistingDomain = !!domain;

        if (isExistingDomain) {
            // Domain exists and belongs to the same user
            return res.status(400).json({
                error: 'Domain already exists',
                message: 'You have already registered this domain. Please use a different domain URL.'
            });
        }

        // Step 1: Scrape website data
        try {
            websiteData = await scraperService.scrapeWebsite(url);
            console.log('Website data scraped successfully');
        } catch (error) {
            console.error('Error scraping website:', error);
            // Instead of failing, create minimal websiteData with the URL
            websiteData = {
                title: new URL(url).hostname,
                description: `Website for ${new URL(url).hostname}`,
                mainContent: '',
                headings: [],
                paragraphs: [],
                links: [],
                images: [],
                keywords: [],
                metadata: {}
            };
            console.log('Created fallback website data');
        }

        // Step 2: Create new domain for this user
        try {
            // Create brandInfo object only with fields that have actual data
            const brandInfo = {};

            // Add fields only if they exist and have meaningful content
            if (websiteData.title) brandInfo.name = websiteData.title;
            if (websiteData.description) brandInfo.description = websiteData.description;
            if (websiteData.metadata?.audience) brandInfo.targetAudience = websiteData.metadata.audience;

            // Look for logo in images
            const logoImage = websiteData.images?.find(img =>
                img.alt?.toLowerCase().includes('logo') ||
                img.url?.toLowerCase().includes('logo')
            );
            if (logoImage?.url) brandInfo.logo = logoImage.url;

            // Extract benefits from headings
            const benefitHeadings = websiteData.headings?.filter(h =>
                h.text?.toLowerCase().includes('benefit') ||
                h.text?.toLowerCase().includes('feature')
            ).map(h => h.text);
            if (benefitHeadings?.length > 0) brandInfo.benefits = benefitHeadings;

            // Add industry if found in metadata
            if (websiteData.metadata?.industry) brandInfo.industry = websiteData.metadata.industry;

            // Add keywords/tags if available
            if (websiteData.keywords?.length > 0) brandInfo.tags = websiteData.keywords;

            domain = new Domain({
                name: websiteData.title || new URL(url).hostname,
                url: url,
                title: websiteData.title || new URL(url).hostname,
                description: websiteData.description || `Website for ${new URL(url).hostname}`,
                userId: req.user.id,
                brandInfo: Object.keys(brandInfo).length > 0 ? brandInfo : {
                    name: new URL(url).hostname,
                    description: `Website for ${new URL(url).hostname}`,
                    industry: 'General',
                    targetAudience: 'General audience'
                }
            });
            await domain.save();
            console.log('New domain created:', domain.name);
        } catch (error) {
            console.error('Error creating domain:', error);
            return res.status(500).json({
                error: 'Failed to create domain',
                details: error.message
            });
        }

        // Return success response with domain data
        res.json({
            websiteInfo: websiteData,
            domain: domain,
            isExistingDomain: false,
            message: `New domain "${domain.name}" created successfully`
        });
    } catch (error) {
        console.error('Error processing domain:', error);
        res.status(500).json({
            error: 'Failed to process domain',
            details: error.message
        });
    }
});

// Add this after app.use(express.json())
app.use('/api/payments', paymentRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!', details: err.message });
});

// Start server
const PORT = process.env.PORT || 5000; // Changed default port to 5080
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);

    // Start auto article generation service
    autoArticleService.start();
    console.log('Auto article generation service initialized - will run daily at 11:00 AM');
});

// Helper function to process completed prorated payments in webhook
async function processCompletedProratedPaymentInWebhook(transaction, stripe, Domain, Plan) {
    console.log(`[WEBHOOK] Processing completed prorated payment for transaction ${transaction._id}`);

    try {
        // Get the domain
        const domain = await Domain.findById(transaction.domainId);
        if (!domain) {
            console.log(`[WEBHOOK] Domain not found for transaction ${transaction._id}`);
            return;
        }

        // Get the current subscription from Stripe
        const currentSubscriptionId = domain.subscription?.stripeSubscriptionId;
        if (!currentSubscriptionId) {
            console.log(`[WEBHOOK] No subscription ID found for domain ${transaction.domainId}`);
            return;
        }

        // Update the existing subscription instead of canceling and creating new one
        const newPlanType = transaction.planType;
        let priceId;

        // Get price IDs from environment
        const PRICE_ID_MONTHLY = process.env.PRICE_ID_MONTHLY;
        const PRICE_ID_YEARLY = process.env.PRICE_ID_YEARLY;
        const PRICE_ID_DAILY = process.env.PRICE_ID_DAILY;

        if (newPlanType === 'Monthly') {
            priceId = PRICE_ID_MONTHLY;
        } else if (newPlanType === 'Yearly') {
            priceId = PRICE_ID_YEARLY;
        } else if (newPlanType === 'Daily') {
            priceId = PRICE_ID_DAILY;
        } else {
            console.error(`[WEBHOOK] Invalid plan type for prorated payment: ${newPlanType}`);
            throw new Error(`Invalid plan type: ${newPlanType}`);
        }

        try {
            // Get current subscription details for debugging
            const currentSubscription = await stripe.subscriptions.retrieve(currentSubscriptionId);
            console.log(`[WEBHOOK] Current subscription before update: Plan=${currentSubscription.items.data[0].price.id}, Status=${currentSubscription.status}`);

            // Update the existing subscription with the new plan
            const updatedSubscription = await stripe.subscriptions.update(currentSubscriptionId, {
                items: [
                    {
                        id: currentSubscription.items.data[0].id,
                        price: priceId,
                    },
                ],
                metadata: {
                    userId: transaction.userId,
                    domainId: transaction.domainId,
                    planType: newPlanType,
                    plan: newPlanType, // Add this for Stripe dashboard display
                    isProrated: 'true',
                    previousPlanType: domain.subscription.planType,
                    updatedAt: new Date().toISOString(),
                    updatedViaWebhook: 'true'
                },
                proration_behavior: 'create_prorations'
            });

            console.log(`[WEBHOOK] Updated subscription: Plan=${updatedSubscription.items.data[0].price.id}, Status=${updatedSubscription.status}`);
            console.log(`[WEBHOOK] Updated subscription ${currentSubscriptionId} with new plan ${newPlanType} for domain ${transaction.domainId}`);

            // Calculate credits based on the new plan type
            const credits = newPlanType === 'Daily' ? 1 :
                newPlanType === 'Monthly' ? 30 :
                    newPlanType === 'Yearly' ? 365 : 0;

            // Update domain subscription (keeping the same subscription ID)
            await Domain.findByIdAndUpdate(transaction.domainId, {
                subscription: {
                    active: true,
                    planType: newPlanType,
                    credits: credits,
                    stripeCustomerId: domain.subscription.stripeCustomerId,
                    stripeSubscriptionId: currentSubscriptionId, // Keep the same subscription ID
                    updatedAt: new Date(),
                    canceledAt: domain.subscription?.canceledAt,
                    expiresAt: domain.subscription?.expiresAt
                }
            });
            console.log(`[WEBHOOK] Domain ${transaction.domainId} subscription updated after prorated payment with same subscription ID`);

            // Update user plan
            const planFeatures = newPlanType === 'Monthly'
                ? { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true }
                : newPlanType === 'Yearly'
                    ? { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true }
                    : { maxDomains: 2, maxArticlesPerMonth: 5, customBranding: true };

            await Plan.findOneAndUpdate(
                { userId: transaction.userId },
                {
                    type: newPlanType === 'Monthly' ? 'Pro' : newPlanType === 'Yearly' ? 'Enterprise' : 'Basic',
                    displayType: newPlanType,
                    features: planFeatures,
                    startDate: new Date(),
                    endDate: newPlanType === 'Monthly'
                        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days
                        : newPlanType === 'Yearly'
                            ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)  // 365 days
                            : new Date(Date.now() + 24 * 60 * 60 * 1000),  // 1 day
                    status: 'active'
                },
                { upsert: true }
            );

            console.log(`[WEBHOOK] Plan updated for user ${transaction.userId} after prorated payment`);

        } catch (stripeError) {
            console.error(`[WEBHOOK] Error updating subscription ${currentSubscriptionId}:`, stripeError);
            throw stripeError;
        }
    } catch (error) {
        console.error(`[WEBHOOK] Error processing prorated payment for transaction ${transaction._id}:`, error);
        throw error;
    }
} 