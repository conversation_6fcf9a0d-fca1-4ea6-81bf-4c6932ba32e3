const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const adminAuth = require('../middleware/adminAuth');

// Public admin routes (no authentication required)
router.post('/login', adminController.login);

// Apply adminAuth middleware to all protected routes
router.use(adminAuth);

// Admin Authentication Routes
router.post('/logout', adminController.logout);
router.get('/verify-token', adminController.verifyToken);

// Profile Management Routes
router.get('/profile', adminController.getProfile);
router.patch('/profile', adminController.updateProfile);
router.post('/change-password', adminController.changePassword);

// Admin Management Routes
router.post('/admins', adminController.createAdmin);
router.patch('/admins/:id', adminController.updateAdmin);

// Dashboard Statistics and Recent Data
router.get('/stats', adminController.getStats);
router.get('/users/recent', adminController.getRecentUsers);
router.get('/articles/recent', adminController.getRecentArticles);

// User Management Routes
router.get('/users', adminController.getUsers);
router.get('/users/:id', adminController.getUserDetails);
router.get('/users/:id/domains', adminController.getUserDomains);
router.patch('/users/:id/status', adminController.updateUserStatus);
router.delete('/users/:id', adminController.deleteUser);
router.put('/users/:id', adminController.updateUser);

// Domain Management Routes
router.get('/domains', adminController.getDomains);

// Article Management Routes
router.get('/articles', adminController.getArticles);
router.get('/articles/by-slug/:slug', adminController.getArticleBySlug);
router.patch('/articles/:id/status', adminController.updateArticleStatus);

// Get articles for a specific domain
router.get('/domains/:domainId/articles', adminController.getDomainArticles);

// Transaction Management Routes
router.get('/transactions', adminController.getAllTransactions);
router.get('/transactions/:id', adminController.getTransactionById);
router.get('/users/:userId/transactions', adminController.getUserTransactions);
router.get('/transactions/:id/invoice', adminController.generateInvoice);

module.exports = router; 