import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X } from 'lucide-react';
import { BrandInfo } from '@/services/domainService';

interface BrandSetupProps {
  onNext: () => void;
  onData: (data: BrandInfo) => void;
  initialData: {
    name: string;
    description: string;
    keywords: string[];
  };
}

export const BrandSetup = ({ onNext, onData, initialData }: BrandSetupProps) => {
  const [brandInfo, setBrandInfo] = useState<BrandInfo>({
    name: initialData.name,
    description: initialData.description,
    targetAudience: '',
    logo: '',
    audienceLocation: 'India',
    benefits: ['', '', ''],
    toneOfVoice: '',
    industry: '',
    tags: initialData.keywords || []
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onData(brandInfo);
    onNext();
  };

  const addBenefit = () => {
    setBrandInfo(prev => ({
      ...prev,
      benefits: [...prev.benefits, '']
    }));
  };

  const removeBenefit = (index: number) => {
    setBrandInfo(prev => ({
      ...prev,
      benefits: prev.benefits.filter((_, i) => i !== index)
    }));
  };

  const updateBenefit = (index: number, value: string) => {
    setBrandInfo(prev => ({
      ...prev,
      benefits: prev.benefits.map((benefit, i) => i === index ? value : benefit)
    }));
  };

  const addTag = () => {
    setBrandInfo(prev => ({
      ...prev,
      tags: [...prev.tags, '']
    }));
  };

  const removeTag = (index: number) => {
    setBrandInfo(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index)
    }));
  };

  const updateTag = (index: number, value: string) => {
    setBrandInfo(prev => ({
      ...prev,
      tags: prev.tags.map((tag, i) => i === index ? value : tag)
    }));
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-8">
      <div className="mb-8 text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          Tell us about your brand
        </h1>
        <p className="text-gray-600">
          This information helps us generate content that matches your brand voice and target audience
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card className="p-6">
          <div className="space-y-6">
            <div>
              <Label htmlFor="name">Brand Name (used by customers)</Label>
              <Input
                id="name"
                value={brandInfo.name}
                onChange={(e) => setBrandInfo(prev => ({ ...prev, name: e.target.value }))}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="description">Brand Description</Label>
              <Textarea
                id="description"
                value={brandInfo.description}
                onChange={(e) => setBrandInfo(prev => ({ ...prev, description: e.target.value }))}
                className="mt-2 h-32"
              />
            </div>

            <div>
              <Label htmlFor="targetAudience">Target Audience</Label>
              <Input
                id="targetAudience"
                value={brandInfo.targetAudience}
                onChange={(e) => setBrandInfo(prev => ({ ...prev, targetAudience: e.target.value }))}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="audienceLocation">Audience Location</Label>
              <Select
                value={brandInfo.audienceLocation}
                onValueChange={(value) => setBrandInfo(prev => ({ ...prev, audienceLocation: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="India">India</SelectItem>
                  <SelectItem value="United States">United States</SelectItem>
                  <SelectItem value="Global">Global</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Key Benefits</Label>
              <div className="space-y-3 mt-2">
                {brandInfo.benefits.map((benefit, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={benefit}
                      onChange={(e) => updateBenefit(index, e.target.value)}
                      placeholder={`Benefit ${index + 1}`}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeBenefit(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addBenefit}
                  className="w-full"
                >
                  Add Benefit
                </Button>
              </div>
            </div>

            <div>
              <Label htmlFor="toneOfVoice">Tone of Voice</Label>
              <Textarea
                id="toneOfVoice"
                value={brandInfo.toneOfVoice}
                onChange={(e) => setBrandInfo(prev => ({ ...prev, toneOfVoice: e.target.value }))}
                className="mt-2"
                placeholder="e.g., Professional yet approachable"
              />
            </div>

            <div>
              <Label htmlFor="industry">Industry</Label>
              <Input
                id="industry"
                value={brandInfo.industry}
                onChange={(e) => setBrandInfo(prev => ({ ...prev, industry: e.target.value }))}
                className="mt-2"
                placeholder="e.g., E-commerce, Technology, Healthcare"
              />
            </div>

            <div>
              <Label>Content Tags</Label>
              <div className="space-y-3 mt-2">
                {brandInfo.tags.map((tag, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={tag}
                      onChange={(e) => updateTag(index, e.target.value)}
                      placeholder={`Tag ${index + 1}`}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeTag(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addTag}
                  className="w-full"
                >
                  Add Tag
                </Button>
              </div>
            </div>
          </div>
        </Card>

        <div className="flex justify-end">
          <Button type="submit" className="bg-orange-500 hover:bg-orange-600 text-white">
            Continue
          </Button>
        </div>
      </form>
    </div>
  );
};
