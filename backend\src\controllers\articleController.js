const Article = require('../models/Article');
const gptService = require('../services/gptService');
const Domain = require('../models/Domain');

// Get all articles
exports.getArticles = async (req, res) => {
    try {
        const { domainId } = req.query;
        let query = { userId: req.user.id };

        if (domainId) {
            // Verify domain ownership
            const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
            if (!domain) {
                return res.status(404).json({ error: 'Domain not found' });
            }
            query.domainId = domainId;
        } else if (domainId === 'null') {
            // Handle articles without domain
            query.domainId = { $exists: false };
        }

        const articles = await Article.find(query).populate('domainId').sort({ createdAt: -1 });

        // Transform articles to match frontend expectations
        const transformedArticles = articles.map(article => ({
            id: article._id.toString(),
            title: article.title,
            description: article.description,
            content: article.content,
            tag: article.tag,
            status: article.status,
            author: article.author,
            date: article.date,
            image: article.image,
            video: article.video,
            urlSlug: article.urlSlug,
            metaDescription: article.metaDescription,
            excerpt: article.excerpt,
            keywords: article.keywords,
            sourceUrl: article.sourceUrl,
            createdAt: article.createdAt,
            domainId: article.domainId?._id?.toString(),
            metadata: article.metadata
        }));

        res.json(transformedArticles);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch articles', details: error.message });
    }
};

// Get single article by ID
exports.getArticleById = async (req, res) => {
    try {
        const article = await Article.findOne({ _id: req.params.id, userId: req.user.id }).populate('domainId');
        if (!article) {
            return res.status(404).json({ error: 'Article not found' });
        }

        // Transform article to match frontend expectations
        const transformedArticle = {
            id: article._id.toString(),
            title: article.title,
            description: article.description,
            content: article.content,
            tag: article.tag,
            status: article.status,
            author: article.author,
            date: article.date,
            image: article.image,
            video: article.video,
            urlSlug: article.urlSlug,
            metaDescription: article.metaDescription,
            excerpt: article.excerpt,
            keywords: article.keywords,
            sourceUrl: article.sourceUrl,
            createdAt: article.createdAt,
            domainId: article.domainId?._id?.toString(),
            metadata: article.metadata
        };

        res.json(transformedArticle);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch article', details: error.message });
    }
};

// Create new article
exports.createArticle = async (req, res) => {
    try {
        // If domainId is provided, verify ownership
        if (req.body.domainId) {
            const domain = await Domain.findOne({ _id: req.body.domainId, userId: req.user.id });
            if (!domain) {
                return res.status(404).json({ error: 'Domain not found' });
            }
        }

        // Create new article with the request body
        const article = new Article({
            ...req.body,
            userId: req.user.id,
            date: new Date().toISOString(),
            metadata: {
                ...req.body.metadata,
                generatedAt: new Date().toISOString()
            }
        });

        // Save the article
        const savedArticle = await article.save();

        // Transform the saved article to match frontend expectations
        const transformedArticle = {
            id: savedArticle._id.toString(),
            title: savedArticle.title,
            description: savedArticle.description,
            content: savedArticle.content,
            tag: savedArticle.tag,
            status: savedArticle.status,
            author: savedArticle.author,
            date: savedArticle.date,
            image: savedArticle.image,
            video: savedArticle.video,
            urlSlug: savedArticle.urlSlug,
            metaDescription: savedArticle.metaDescription,
            excerpt: savedArticle.excerpt,
            keywords: savedArticle.keywords || [],
            sourceUrl: savedArticle.sourceUrl,
            domainId: savedArticle.domainId?.toString(),
            metadata: savedArticle.metadata
        };

        res.status(201).json(transformedArticle);
    } catch (error) {
        console.error('Error creating article:', error);
        res.status(400).json({
            error: 'Failed to create article',
            details: error.message,
            validationErrors: error.errors
        });
    }
};

// Update article
exports.updateArticle = async (req, res) => {
    try {
        // If domainId is being updated, verify ownership
        if (req.body.domainId) {
            const domain = await Domain.findOne({ _id: req.body.domainId, userId: req.user.id });
            if (!domain) {
                return res.status(404).json({ error: 'Domain not found' });
            }
        }

        const article = await Article.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            req.body,
            { new: true, runValidators: true }
        );

        if (!article) {
            return res.status(404).json({ error: 'Article not found' });
        }

        res.json(article);
    } catch (error) {
        res.status(400).json({ error: 'Failed to update article', details: error.message });
    }
};

// Delete article
exports.deleteArticle = async (req, res) => {
    try {
        const article = await Article.findOneAndDelete({ _id: req.params.id, userId: req.user.id });
        if (!article) {
            return res.status(404).json({ error: 'Article not found' });
        }
        res.json({ message: 'Article deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: 'Failed to delete article', details: error.message });
    }
};

// Get article by slug
exports.getArticleBySlug = async (req, res) => {
    try {
        const article = await Article.findOne({ urlSlug: req.params.slug, userId: req.user.id });
        if (!article) {
            return res.status(404).json({ error: 'Article not found' });
        }
        res.json(article);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch article', details: error.message });
    }
};

// Generate article using GPT
exports.generateArticle = async (req, res) => {
    try {
        const { domainId, topic, language } = req.body;

        // Check if domain exists and belongs to user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        // Validate required domain settings
        if (!domain.brandInfo || !domain.brandInfo.name) {
            return res.status(400).json({ error: 'Domain brand info is not set up. Please set up brand info first.' });
        }

        if (!domain.articleSettings) {
            return res.status(400).json({ error: 'Domain article settings are not set up. Please set up article settings first.' });
        }

        // Get existing articles for this domain
        const existingArticles = await Article.find({ domainId, userId: req.user.id }).select('title content');

        // Prepare brand info with defaults
        const brandInfo = {
            name: domain.brandInfo.name,
            description: domain.brandInfo.description || domain.description || '',
            targetAudience: domain.brandInfo.targetAudience || 'General audience',
            audienceLocation: domain.brandInfo.audienceLocation || 'Global',
            benefits: domain.brandInfo.benefits || [],
            toneOfVoice: domain.brandInfo.toneOfVoice || 'Professional',
            industry: domain.brandInfo.industry || 'General',
            tags: domain.brandInfo.tags || [],
            logo: domain.brandInfo.logo || ''
        };

        // Prepare article settings with defaults
        const articleSettings = {
            useBrandInfo: domain.articleSettings.useBrandInfo ?? true,
            articleLength: domain.articleSettings.articleLength || 'medium',
            language: language || domain.articleSettings.language || 'English',
            specificInstructions: domain.articleSettings.specificInstructions || '',
            exclusions: domain.articleSettings.exclusions || ''
        };

        // Generate a unique topic/angle if not provided and there are existing articles
        let uniqueTopic = topic || '';
        if (!uniqueTopic && existingArticles.length > 0) {
            try {
                const existingTitles = existingArticles.map(article => article.title).join(', ');
                const existingTopics = existingArticles.map(article => {
                    // Extract first paragraph or first 100 characters as topic summary
                    const firstParagraph = article.content.match(/<p.*?>(.*?)<\/p>/);
                    return firstParagraph ? firstParagraph[1].substring(0, 100) : article.content.substring(0, 100);
                }).join(', ');

                const uniqueTopicPrompt = `Generate a unique article topic/angle for a ${brandInfo.industry} website that is completely different from existing content.

Existing article titles: ${existingTitles}

Existing article topics: ${existingTopics}

Brand information:
- Name: ${brandInfo.name}
- Target audience: ${brandInfo.targetAudience}
- Industry: ${brandInfo.industry}
- Tone of voice: ${brandInfo.toneOfVoice}
- Keywords/Tags: ${brandInfo.tags.join(', ')}

Requirements:
1. Generate a unique topic that hasn't been covered in existing articles
2. Topic should be relevant to ${brandInfo.industry} industry
3. Topic should interest ${brandInfo.targetAudience}
4. Topic should align with ${brandInfo.name}'s brand identity
5. Return ONLY the topic as a brief phrase or sentence (max 10 words)

Return only the topic, no explanations or other text.`;

                uniqueTopic = await gptService.callOpenAI(
                    [
                        { role: "system", content: "You are an expert content strategist who creates unique article topics." },
                        { role: "user", content: uniqueTopicPrompt }
                    ],
                    0.9, // Higher temperature for more creativity
                    50
                );

                // Clean up the response
                uniqueTopic = uniqueTopic.trim().replace(/^["']|["']$/g, '');

                console.log(`Generated unique topic: ${uniqueTopic}`);
            } catch (error) {
                console.error('Error generating unique topic:', error);
                // Continue with empty topic if generation fails
            }
        }

        // Generate article using GPT
        const websiteData = {
            title: domain.title || domain.name,
            description: domain.description || '',
            url: domain.url,
            topic: uniqueTopic || '',
            mainContent: domain.description || '',
            keywords: brandInfo.tags,
            domain: {
                id: domain._id.toString(),
                name: domain.name,
                brandInfo: brandInfo,
                articleSettings: articleSettings
            }
        };

        const generatedArticle = await gptService.generateArticle(websiteData, existingArticles);

        // Create and save the article
        const article = new Article({
            ...generatedArticle,
            userId: req.user.id,
            domainId,
            status: 'generated',
            date: new Date().toISOString(),
            metadata: {
                ...generatedArticle.metadata,
                usedBrandInfo: articleSettings.useBrandInfo,
                brandName: brandInfo.name,
                language: articleSettings.language,
                industry: brandInfo.industry,
                targetAudience: brandInfo.targetAudience,
                toneOfVoice: brandInfo.toneOfVoice,
                articleLength: articleSettings.articleLength,
                generatedAt: new Date().toISOString()
            }
        });

        const savedArticle = await article.save();

        // Transform the response
        const transformedArticle = {
            id: savedArticle._id.toString(),
            title: savedArticle.title,
            description: savedArticle.description,
            content: savedArticle.content,
            tag: savedArticle.tag,
            status: savedArticle.status,
            author: savedArticle.author,
            date: savedArticle.date,
            image: savedArticle.image,
            video: savedArticle.video,
            urlSlug: savedArticle.urlSlug,
            metaDescription: savedArticle.metaDescription,
            excerpt: savedArticle.excerpt,
            keywords: savedArticle.keywords,
            sourceUrl: savedArticle.sourceUrl,
            domainId: savedArticle.domainId?.toString(),
            metadata: savedArticle.metadata
        };

        res.json(transformedArticle);
    } catch (error) {
        console.error('Error generating article:', error);
        res.status(500).json({ error: 'Failed to generate article', details: error.message });
    }
}; 