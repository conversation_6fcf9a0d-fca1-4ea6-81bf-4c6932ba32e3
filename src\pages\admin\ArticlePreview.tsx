import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAdmin } from '../../contexts/AdminContext';
import { Article } from '../../types/Article';
import { Button } from "../../components/ui/button";
import { ArrowLeft } from 'lucide-react';
import DailyDelightPreview from '../../components/preview/DailyDelightPreview';
import ModernCapsulePreview from '../../components/preview/ModernCapsulePreview';

interface DomainData {
    _id: string;
    name: string;
    url: string;
    designSettings: {
        articleTheme?: string;
        colors?: {
            brand: string;
            accent: string;
        };
        font?: string;
        logo?: string;
    };
    brandInfo: {
        name: string;
        description: string;
        targetAudience: string;
    };
    articleSettings: {
        userBrandInfo: boolean;
        articleLength: string;
    };
    hostingSettings: {
        domain: string;
        subdomain: string;
    };
}

interface UserData {
    _id: string;
    firstName: string;
    lastName: string;
    avatar?: string;
}

interface AdminArticle extends Omit<Article, 'domainId' | 'userId'> {
    domainId: DomainData;
    userId: UserData;
}

const AdminArticlePreview = () => {
    const { slug } = useParams<{ slug: string }>();
    const navigate = useNavigate();
    const { token } = useAdmin();
    const [article, setArticle] = useState<AdminArticle | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchArticle = async () => {
            try {
                setLoading(true);
                setError(null);

                const response = await axios.get(
                    `http://localhost:5000/api/admin/articles/by-slug/${slug}`,
                    { headers: { Authorization: `Bearer ${token}` } }
                );

                setArticle(response.data);
            } catch (err) {
                console.error('Error fetching article:', err);
                setError('Failed to load article');
            } finally {
                setLoading(false);
            }
        };

        if (slug) {
            fetchArticle();
        }
    }, [slug, token]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                    <p className="mt-4 text-gray-600">Loading article...</p>
                </div>
            </div>
        );
    }

    if (error || !article) {
        return (
            <div className="flex flex-col items-center justify-center min-h-screen">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
                    <p className="text-gray-600 mb-4">{error || 'Article not found'}</p>
                    <Button
                        variant="outline"
                        onClick={() => navigate(-1)}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="w-4 h-4" />
                        Go Back
                    </Button>
                </div>
            </div>
        );
    }

    const theme = article.domainId?.designSettings?.articleTheme || 'Daily Delight';
    const brandColor = article.domainId?.designSettings?.colors?.brand || '#3c484c';
    const fontColor = article.domainId?.designSettings?.colors?.accent || '#cec4c0';
    const fontFamily = article.domainId?.designSettings?.font || 'Roboto';

    // Transform the article to match the expected format
    const previewArticle: Article = {
        ...article,
        domainId: article.domainId._id,
        userId: article.userId._id,
        userData: {
            firstName: article.userId.firstName,
            lastName: article.userId.lastName,
            avatar: article.userId.avatar
        }
    };

    const commonProps = {
        article: previewArticle,
        fontFamily,
        fontColor,
        brandColor
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="bg-white shadow">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="ghost"
                            onClick={() => navigate(-1)}
                            size="icon"
                            className="h-10 w-10"
                        >
                            <ArrowLeft className="h-5 w-5" />
                        </Button>
                        <h1 className="text-xl font-semibold">Article Preview</h1>
                    </div>
                </div>
            </div>

            {/* Preview Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="bg-white rounded-lg shadow-sm p-8">
                    {theme === 'Modern Capsule' ? (
                        <ModernCapsulePreview {...commonProps} />
                    ) : (
                        <DailyDelightPreview {...commonProps} />
                    )}
                </div>
            </div>
        </div>
    );
};

export default AdminArticlePreview; 