export interface Author {
    name: string;
    avatar?: string;
    bio?: string;
}

export interface ImageCredit {
    name: string;
    username: string;
    link: string;
}

export interface ArticleImage {
    url: string;
    thumb: string;
    description?: string;
    credit?: ImageCredit;
}

export interface VideoCredit {
    name: string;
    url: string;
}

export interface ArticleVideo {
    url: string;
    width?: number;
    height?: number;
    duration?: number;
    description?: string;
    thumbnail?: string;
    credit?: {
        name?: string;
        url?: string;
    };
}

export interface ArticleMetadata {
    originalTitle?: string;
    originalDescription?: string;
    generatedAt?: string;
}

export interface Article {
    id: string;
    _id: string;
    title: string;
    content: string;
    description?: string;
    author?: string | Author;
    date: string;
    status: string;
    tag?: string;
    sourceUrl?: string;
    image?: {
        url: string;
        thumb?: string;
        description?: string;
        credit?: {
            name?: string;
            username?: string;
            link?: string;
        };
    };
    domainId?: string;
    metadata?: {
        originalTitle?: string;
        originalDescription?: string;
        generatedAt?: string;
        [key: string]: any;
    };
    video?: {
        url: string;
        width?: number;
        height?: number;
        duration?: number;
        thumbnail?: string;
        description?: string;
        credit?: {
            name?: string;
            url?: string;
        };
    };
    urlSlug?: string;
    metaDescription?: string;
    excerpt?: string;
    keywords?: string[];
    createdAt?: string;
    updatedAt?: string;
    userId?: string;
    userData?: {
        firstName: string;
        lastName: string;
        avatar?: string;
    };
}

export interface HostingSettings {
    domain: string;
    subdomain: string;
    isVerified: boolean;
    cnameRecords: {
        one: {
            host: string;
            value: string;
        };
        two: {
            host: string;
            value: string;
        };
    };
}

export interface DesignSettings {
    logo: string;
    articleTheme: string;
    layout: {
        grid: string;
        listEnabled: boolean;
    };
    colors: {
        brand: string;
        accent: string;
    };
    font: string;
}

export interface Domain {
    _id: string;
    name: string;
    url: string;
    createdAt?: string;
    updatedAt?: string;
    userId: string;
    hostingSettings?: {
        domain: string;
        subdomain: string;
        isVerified: boolean;
    };
    designSettings?: {
        logo?: string;
        articleTheme?: string;
        layout?: {
            grid?: string;
            listEnabled?: boolean;
        };
        colors?: {
            brand: string;
            accent: string;
        };
        font?: string;
    };
    navigationSettings?: {
        homeButtonEnabled: boolean;
        ctaButtonDisabled: boolean;
        headerLinks: Array<{ id: string; text: string; url: string }>;
        footerLinks: Array<{ id: string; text: string; url: string }>;
    };
} 