import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { CheckCircle } from 'lucide-react';

const ForgotPassword = () => {
    const { forgotPassword } = useAuth();
    const [email, setEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [emailSent, setEmailSent] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);

        try {
            await forgotPassword(email);
            setEmailSent(true);
            toast.success('Password reset link sent to your email');
        } catch (error: any) {
            toast.error(error.message || 'Failed to send password reset email');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-[#fff7ed] p-4">
            <div className="w-full max-w-[400px]">
                <div className="bg-white rounded-lg shadow-sm p-8">
                    <div className="text-center mb-6">
                        <h1 className="text-2xl font-semibold text-gray-900">Forgot Password</h1>
                        <p className="mt-2 text-sm text-gray-600">
                            {emailSent
                                ? 'Check your email for a password reset link'
                                : 'Enter your email address to receive a password reset link'}
                        </p>
                    </div>

                    {!emailSent ? (
                        <form onSubmit={handleSubmit} className="space-y-5">
                            <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                    Email address
                                </label>
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    autoComplete="email"
                                    required
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#ea580c] focus:border-transparent"
                                    placeholder="Enter your email address"
                                />
                            </div>

                            <button
                                type="submit"
                                disabled={loading}
                                className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#ea580c] hover:bg-[#dc2626] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#ea580c] transition-colors duration-200"
                            >
                                {loading ? 'Sending...' : 'Send Reset Link'}
                            </button>
                        </form>
                    ) : (
                        <div className="text-center">
                            {/* Success Icon */}
                            <div className="flex justify-center mb-4">
                                <div className="rounded-full bg-green-100 p-3">
                                    <CheckCircle className="h-8 w-8 text-green-600" />
                                </div>
                            </div>

                            {/* Success Message */}
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                <h3 className="text-green-800 font-medium mb-1">Email Sent Successfully!</h3>
                                <p className="text-green-700">
                                    We've sent a password reset link to <strong>{email}</strong>
                                </p>
                            </div>

                            <p className="mb-4 text-gray-600">
                                Please check your email and follow the instructions to reset your password.
                            </p>
                            <p className="mb-4 text-sm text-gray-500">
                                If you don't receive an email within a few minutes, check your spam folder or try again.
                            </p>
                            <button
                                onClick={() => {
                                    setEmail('');
                                    setEmailSent(false);
                                }}
                                className="text-[#ea580c] hover:text-[#dc2626] font-medium"
                            >
                                Try with a different email
                            </button>
                        </div>
                    )}

                    <div className="mt-6 text-center text-sm">
                        <p className="text-gray-600">
                            Remember your password?{' '}
                            <Link to="/login" className="text-[#ea580c] hover:text-[#dc2626] font-medium">
                                Sign in
                            </Link>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword; 