import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextStyle from '@tiptap/extension-text-style';
import TextAlign from '@tiptap/extension-text-align';
import Color from '@tiptap/extension-color';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import { ArrowLeft } from 'lucide-react';
import { Article, ArticleImage, ArticleVideo, ArticleMetadata } from '@/types/Article';
import { Bold, Italic, Underline as UnderlineIcon, Heading2, Heading3, <PERSON>, ListOrdered, Link2, <PERSON>2Off, Image as ImageIcon } from 'lucide-react';
import { ConfirmationDialog } from '@/components/ConfirmationDialog';
import { toast } from '@/components/ui/use-toast';

// Extended Article type for editor-specific properties
interface EditableArticle extends Article {
    _originalVideoHtml?: string;
    _imagePositions?: Array<{
        src: string | null;
        node: HTMLImageElement;
    }>;
    activeSection?: 'info' | 'keywords' | 'tags' | 'content' | null;
}

interface ArticleEditorProps {
    article: Article | null;
    onClose: () => void;
    onSave: (article: Article) => void;
}

const MAX_IMAGE_WIDTH = 800; // Maximum width for images
const MAX_IMAGE_HEIGHT = 600; // Maximum height for images

const defaultImage: ArticleImage = {
    url: '/lovable-uploads/default-article.png',
    thumb: '/lovable-uploads/default-article-thumb.png',
    description: '',
    credit: {
        name: '',
        username: '',
        link: ''
    }
};

const defaultMetadata: ArticleMetadata = {
    originalTitle: '',
    originalDescription: '',
    generatedAt: new Date().toISOString()
};

const defaultArticle: EditableArticle = {
    id: '',
    _id: '',
    title: '',
    description: '',
    content: '',
    tag: 'General',
    status: 'draft',
    author: 'AI Writer',
    date: new Date().toISOString(),
    image: defaultImage,
    urlSlug: '',
    metaDescription: '',
    excerpt: '',
    keywords: [],
    metadata: defaultMetadata
};

export const ArticleEditor = ({ article, onClose, onSave }: ArticleEditorProps) => {
    const [editedArticle, setEditedArticle] = useState<EditableArticle>({
        ...defaultArticle,
        ...article,
        video: article?.video // Explicitly preserve the video property
    });
    const [activeSection, setActiveSection] = useState<'info' | 'keywords' | 'tags' | 'content' | null>(null);
    const [showSaveConfirmation, setShowSaveConfirmation] = useState(false);
    const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
    const imageInputRef = useRef<HTMLInputElement>(null);

    const toggleSection = (section: 'info' | 'keywords' | 'tags' | 'content') => {
        setActiveSection(prev => prev === section ? null : section);
    };

    // Function to process and resize image
    const processImage = async (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = document.createElement('img');
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    let width = img.width;
                    let height = img.height;

                    // Calculate new dimensions while maintaining aspect ratio
                    if (width > MAX_IMAGE_WIDTH) {
                        height = (height * MAX_IMAGE_WIDTH) / width;
                        width = MAX_IMAGE_WIDTH;
                    }
                    if (height > MAX_IMAGE_HEIGHT) {
                        width = (width * MAX_IMAGE_HEIGHT) / height;
                        height = MAX_IMAGE_HEIGHT;
                    }

                    canvas.width = width;
                    canvas.height = height;
                    const ctx = canvas.getContext('2d');
                    ctx?.drawImage(img, 0, 0, width, height);
                    resolve(canvas.toDataURL('image/jpeg', 0.85));
                };
                img.onerror = reject;
                img.src = e.target?.result as string;
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    };

    const editor = useEditor({
        extensions: [
            StarterKit,
            Underline,
            TextStyle,
            Color,
            Link,
            Image.configure({
                inline: true,
                allowBase64: true,
                HTMLAttributes: {
                    class: 'article-image',
                    style: 'max-width: 100%; height: auto;'
                }
            }),
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            }),
        ],
        content: article?.content || '',
        editorProps: {
            attributes: {
                class: 'prose prose-lg max-w-none min-h-[500px] focus:outline-none w-full h-full p-4',
                style: 'cursor: text'
            }
        },
        onUpdate: ({ editor }) => {
            setEditedArticle(prev => ({
                ...prev,
                content: editor.getHTML(),
                video: article?.video // Preserve video during content updates
            }));
        },
    });

    useEffect(() => {
        if (article && editor) {
            // Store the original video HTML if it exists
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = article.content || '';
            const videoContainer = tempDiv.querySelector('.article-video-container');
            const originalVideoHtml = videoContainer?.outerHTML || '';

            // Remove video from editor content
            if (videoContainer) {
                videoContainer.remove();
            }

            // Find all images in the content and store their positions
            const images = tempDiv.querySelectorAll('img');
            const imagePositions = Array.from(images).map(img => ({
                src: img.getAttribute('src'),
                node: img
            }));

            // Set editor content without video
            editor.commands.setContent(tempDiv.innerHTML);

            // Store both the original video HTML and video data
            setEditedArticle(prev => ({
                ...article,
                status: article.status.toLowerCase(),
                tag: article.tag || 'General',
                image: article.image || defaultImage,
                video: article.video,
                _originalVideoHtml: originalVideoHtml,
                _imagePositions: imagePositions, // Store image positions for reference
                urlSlug: article.urlSlug || '',
                metaDescription: article.metadata?.originalDescription || article.metaDescription || '',
                excerpt: article.excerpt || '',
                keywords: article.keywords || [],
                metadata: {
                    ...defaultMetadata,
                    ...article.metadata
                }
            }));
        }
    }, [article, editor]);

    const handleImageUpload = async (file: File) => {
        try {
            const resizedImage = await processImage(file);

            // Get the current selection
            const { from } = editor?.state.selection || {};

            // Find the closest image node to the cursor
            let imageNode = null;
            editor?.state.doc.nodesBetween(from, from, (node, pos) => {
                if (node.type.name === 'image') {
                    imageNode = { node, pos };
                    return false;
                }
            });

            if (imageNode) {
                // Replace the existing image
                editor?.chain()
                    .focus()
                    .setNodeSelection(imageNode.pos)
                    .deleteSelection()
                    .insertContent({
                        type: 'image',
                        attrs: {
                            src: resizedImage,
                            alt: file.name.split('.')[0]
                        }
                    })
                    .run();

                // Also update the featured image if it matches the old image
                if (editedArticle.image?.url === imageNode.node.attrs.src) {
                    setEditedArticle(prev => ({
                        ...prev,
                        image: {
                            ...defaultImage,
                            url: resizedImage,
                            thumb: resizedImage,
                            description: file.name.split('.')[0]
                        }
                    }));
                }
            } else {
                // If no image is selected, show an error
                toast({
                    title: "No image selected",
                    description: "Please click on an image first to replace it",
                    variant: "destructive"
                });
            }
        } catch (error) {
            console.error('Error processing image:', error);
            toast({
                title: "Error",
                description: "Failed to process image",
                variant: "destructive"
            });
        }
    };

    // Add styles to the head
    useEffect(() => {
        const style = document.createElement('style');
        style.textContent = `
            .article-image {
                max-width: 100%;
                height: auto;
                margin: 1rem 0;
                border-radius: 8px;
            }
        `;
        document.head.appendChild(style);
        return () => {
            document.head.removeChild(style);
        };
    }, []);

    const handleSave = () => {
        if (!editor) return;

        // Get the current editor content
        const editorContent = editor.getHTML();

        // If there was a video, reinsert the original video HTML
        if (editedArticle._originalVideoHtml) {
            // Split content into paragraphs
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = editorContent;
            const paragraphs = Array.from(tempDiv.children);

            // Find the original position or middle point
            const middleIndex = Math.floor(paragraphs.length / 2);

            // Insert the original video HTML
            const videoDiv = document.createElement('div');
            videoDiv.innerHTML = editedArticle._originalVideoHtml;

            if (paragraphs.length > 0) {
                paragraphs[middleIndex].insertAdjacentHTML('afterend', editedArticle._originalVideoHtml);
            } else {
                tempDiv.innerHTML = editorContent + editedArticle._originalVideoHtml;
            }

            // Save with the original video HTML reinserted
            const finalContent = tempDiv.innerHTML;
            const { _originalVideoHtml, ...articleWithoutOriginalHtml } = editedArticle;

            onSave({
                ...articleWithoutOriginalHtml,
                content: finalContent
            });
        } else {
            // No video, just save the content as is
            const { _originalVideoHtml, ...articleWithoutOriginalHtml } = editedArticle;
            onSave({
                ...articleWithoutOriginalHtml,
                content: editorContent
            });
        }
    };

    const handleCloseClick = () => {
        onClose();
    };

    const handleKeywordsChange = (value: string) => {
        const keywords = value.split(',').map(k => k.trim()).filter(k => k);
        setEditedArticle(prev => ({ ...prev, keywords }));
    };

    const handleImageUrl = () => {
        // Get the current selection
        const { from } = editor?.state.selection || {};

        // Find the closest image node to the cursor
        let imageNode = null;
        editor?.state.doc.nodesBetween(from, from, (node, pos) => {
            if (node.type.name === 'image') {
                imageNode = { node, pos };
                return false;
            }
        });

        if (imageNode) {
            const url = window.prompt('Enter image URL:');
            if (url) {
                // Replace the existing image
                editor?.chain()
                    .focus()
                    .setNodeSelection(imageNode.pos)
                    .deleteSelection()
                    .insertContent({
                        type: 'image',
                        attrs: {
                            src: url,
                            alt: 'Image from URL'
                        }
                    })
                    .run();

                // Also update the featured image if it matches the old image
                if (editedArticle.image?.url === imageNode.node.attrs.src) {
                    setEditedArticle(prev => ({
                        ...prev,
                        image: {
                            ...defaultImage,
                            url: url,
                            thumb: url
                        }
                    }));
                }
            }
        } else {
            toast({
                title: "No image selected",
                description: "Please click on an image first to replace it",
                variant: "destructive"
            });
        }
    };

    const EditorToolbar = () => (
        <div className="border-b bg-white p-2">
            <div className="flex flex-wrap items-center gap-1">
                {/* Text Style Group */}
                <div className="flex items-center gap-1 border-r pr-1">
                    <button
                        onClick={() => editor?.chain().focus().toggleBold().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('bold') ? 'bg-gray-200' : ''}`}
                        title="Bold"
                    >
                        <Bold className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleItalic().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('italic') ? 'bg-gray-200' : ''}`}
                        title="Italic"
                    >
                        <Italic className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleUnderline().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('underline') ? 'bg-gray-200' : ''}`}
                        title="Underline"
                    >
                        <UnderlineIcon className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleStrike().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('strike') ? 'bg-gray-200' : ''}`}
                        title="Strike"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="5" y1="12" x2="19" y2="12"></line><path d="M16 6C16 6 14.5 4 12 4C9.5 4 7 6 7 8"></path><path d="M8 16C8 16 10 18 12 18C14 18 16 16 16 14"></path></svg>
                    </button>
                </div>

                {/* Heading Group */}
                <div className="flex items-center gap-1 border-r pr-1">
                    <button
                        onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''}`}
                        title="Heading 1"
                    >
                        H1
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}`}
                        title="Heading 2"
                    >
                        H2
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''}`}
                        title="Heading 3"
                    >
                        H3
                    </button>
                </div>

                {/* Alignment Group */}
                <div className="flex items-center gap-1 border-r pr-1">
                    <button
                        onClick={() => editor?.chain().focus().setTextAlign('left').run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive({ textAlign: 'left' }) ? 'bg-gray-200' : ''}`}
                        title="Align Left"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="12" x2="15" y2="12"></line><line x1="3" y1="18" x2="18" y2="18"></line></svg>
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().setTextAlign('center').run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive({ textAlign: 'center' }) ? 'bg-gray-200' : ''}`}
                        title="Align Center"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="3" y1="6" x2="21" y2="6"></line><line x1="6" y1="12" x2="18" y2="12"></line><line x1="4" y1="18" x2="20" y2="18"></line></svg>
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().setTextAlign('right').run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive({ textAlign: 'right' }) ? 'bg-gray-200' : ''}`}
                        title="Align Right"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="3" y1="6" x2="21" y2="6"></line><line x1="9" y1="12" x2="21" y2="12"></line><line x1="6" y1="18" x2="21" y2="18"></line></svg>
                    </button>
                </div>

                {/* List Group */}
                <div className="flex items-center gap-1 border-r pr-1">
                    <button
                        onClick={() => editor?.chain().focus().toggleBulletList().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('bulletList') ? 'bg-gray-200' : ''}`}
                        title="Bullet List"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="8" y1="6" x2="21" y2="6"></line><line x1="8" y1="12" x2="21" y2="12"></line><line x1="8" y1="18" x2="21" y2="18"></line><line x1="3" y1="6" x2="3.01" y2="6"></line><line x1="3" y1="12" x2="3.01" y2="12"></line><line x1="3" y1="18" x2="3.01" y2="18"></line></svg>
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleOrderedList().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('orderedList') ? 'bg-gray-200' : ''}`}
                        title="Numbered List"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="10" y1="6" x2="21" y2="6"></line><line x1="10" y1="12" x2="21" y2="12"></line><line x1="10" y1="18" x2="21" y2="18"></line><path d="M4 6h1v4"></path><path d="M4 10h2"></path><path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"></path></svg>
                    </button>
                </div>

                {/* Additional Features */}
                <div className="flex items-center gap-1">
                    <button
                        onClick={() => {
                            const url = window.prompt('Enter link URL:');
                            if (url) {
                                editor?.chain().focus().setLink({ href: url }).run();
                            }
                        }}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('link') ? 'bg-gray-200' : ''}`}
                        title="Add Link"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg>
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleBlockquote().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('blockquote') ? 'bg-gray-200' : ''}`}
                        title="Block Quote"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path><path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path></svg>
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().setHorizontalRule().run()}
                        className="p-1.5 rounded hover:bg-gray-100"
                        title="Horizontal Line"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                    </button>
                </div>

                {/* Add Image Group after Additional Features */}
                <div className="flex items-center gap-1 border-l pl-1">
                    <input
                        type="file"
                        ref={imageInputRef}
                        onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                                try {
                                    const resizedImage = await processImage(file);
                                    const oldImageUrl = editedArticle.image.url;

                                    // First update the editor content
                                    if (editor) {
                                        const doc = editor.state.doc;
                                        let tr = editor.state.tr;

                                        // Find and replace all image nodes
                                        doc.descendants((node, pos) => {
                                            if (node.type.name === 'image' && node.attrs.src === oldImageUrl) {
                                                tr = tr.setNodeMarkup(pos, undefined, {
                                                    ...node.attrs,
                                                    src: resizedImage
                                                });
                                            }
                                        });

                                        editor.view.dispatch(tr);
                                    }

                                    // Then update the featured image
                                    setEditedArticle(prev => ({
                                        ...prev,
                                        image: {
                                            url: resizedImage,
                                            thumb: resizedImage
                                        }
                                    }));

                                    // Clear the input
                                    e.target.value = '';
                                } catch (error) {
                                    console.error('Error processing image:', error);
                                    toast({
                                        title: "Error",
                                        description: "Failed to process image",
                                        variant: "destructive"
                                    });
                                }
                            }
                        }}
                        accept="image/*"
                        className="hidden"
                    />
                    <button
                        onClick={() => imageInputRef.current?.click()}
                        className={`p-1.5 rounded hover:bg-gray-100`}
                        title="Replace Image"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21 15 16 10 5 21"></polyline>
                        </svg>
                    </button>
                    <button
                        onClick={() => {
                            const url = window.prompt('Enter image URL:');
                            if (url) {
                                const oldImageUrl = editedArticle.image.url;

                                // First update the editor content
                                if (editor) {
                                    const doc = editor.state.doc;
                                    let tr = editor.state.tr;

                                    // Find and replace all image nodes
                                    doc.descendants((node, pos) => {
                                        if (node.type.name === 'image' && node.attrs.src === oldImageUrl) {
                                            tr = tr.setNodeMarkup(pos, undefined, {
                                                ...node.attrs,
                                                src: url
                                            });
                                        }
                                    });

                                    editor.view.dispatch(tr);
                                }

                                // Then update the featured image
                                setEditedArticle(prev => ({
                                    ...prev,
                                    image: {
                                        url: url,
                                        thumb: url
                                    }
                                }));
                            }
                        }}
                        className="p-1.5 rounded hover:bg-gray-100"
                        title="Replace with URL"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 19H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h18a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2zM3 7v10h18V7H3z"></path>
                            <path d="M9.5 13a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5z"></path>
                            <path d="M21 19l-4.5-4.5L14 17l-4-4L3 19"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    );

    if (!article) return null;

    return (
        <div className="h-full bg-white">
            {/* Header */}
            <div className="h-14 border-b flex items-center justify-between px-4">
                <div className="flex items-center space-x-4">
                    <button
                        onClick={handleCloseClick}
                        className="text-sm text-gray-600 hover:text-gray-900 flex items-center space-x-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        <span>Back to Articles</span>
                    </button>
                </div>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={handleCloseClick}
                        className="text-sm text-gray-600 hover:text-gray-900 px-3 py-1.5"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={handleSave}
                        className="text-sm text-white bg-orange-500 hover:bg-orange-600 px-3 py-1.5 rounded"
                    >
                        Save Changes
                    </button>
                </div>
            </div>

            {/* Content */}
            <div className="flex h-[calc(100vh-8rem)]">
                {/* Main Editor */}
                <div className="flex-1 overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:none]">
                    <div className="p-6">
                        <div className="space-y-6">
                            {/* Title Section */}
                            <div>
                                <h2 className="text-base font-medium mb-4">Title & Description</h2>
                                <Input
                                    value={editedArticle.title}
                                    onChange={(e) => setEditedArticle(prev => ({ ...prev, title: e.target.value }))}
                                    placeholder="Enter article title"
                                    className="mb-4"
                                />
                                <Textarea
                                    value={editedArticle.description}
                                    onChange={(e) => setEditedArticle(prev => ({ ...prev, description: e.target.value }))}
                                    placeholder="Enter article description"
                                    className="h-24"
                                />
                            </div>

                            {/* Featured Image Section */}
                            <div>
                                <h2 className="text-base font-medium mb-4">Featured Image</h2>
                                <div className="border rounded-lg p-4 space-y-4">
                                    {editedArticle.image?.url ? (
                                        <div className="relative">
                                            <img
                                                src={editedArticle.image.url}
                                                alt="Featured image"
                                                className="w-full h-64 object-cover rounded-lg border border-gray-200 shadow-sm"
                                            />
                                            <button
                                                onClick={() => setEditedArticle(prev => ({
                                                    ...prev,
                                                    image: defaultImage
                                                }))}
                                                className="absolute top-2 right-2 bg-red-500 text-white p-1.5 rounded-full hover:bg-red-600 transition-colors shadow-sm"
                                                title="Remove image"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                                </svg>
                                            </button>
                                        </div>
                                    ) : (
                                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer" onClick={() => imageInputRef.current?.click()}>
                                            <div className="flex flex-col items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                                <p className="text-base text-gray-600 mb-2">No featured image selected</p>
                                                <p className="text-sm text-gray-500">Click to upload or drag and drop</p>
                                                <p className="text-xs text-gray-400 mt-2">Recommended size: 1200x630 pixels</p>
                                            </div>
                                        </div>
                                    )}

                                    <div className="flex gap-2">
                                        <input
                                            type="file"
                                            accept="image/*"
                                            className="hidden"
                                            ref={imageInputRef}
                                            onChange={async (e) => {
                                                const file = e.target.files?.[0];
                                                if (file) {
                                                    try {
                                                        const resizedImage = await processImage(file);
                                                        const oldImageUrl = editedArticle.image.url;

                                                        // First update the editor content
                                                        if (editor) {
                                                            const doc = editor.state.doc;
                                                            let tr = editor.state.tr;

                                                            // Find and replace all image nodes
                                                            doc.descendants((node, pos) => {
                                                                if (node.type.name === 'image' && node.attrs.src === oldImageUrl) {
                                                                    tr = tr.setNodeMarkup(pos, undefined, {
                                                                        ...node.attrs,
                                                                        src: resizedImage
                                                                    });
                                                                }
                                                            });

                                                            editor.view.dispatch(tr);
                                                        }

                                                        // Then update the featured image
                                                        setEditedArticle(prev => ({
                                                            ...prev,
                                                            image: {
                                                                url: resizedImage,
                                                                thumb: resizedImage
                                                            }
                                                        }));

                                                        // Clear the input
                                                        e.target.value = '';
                                                    } catch (error) {
                                                        console.error('Error processing image:', error);
                                                        toast({
                                                            title: "Error",
                                                            description: "Failed to process image",
                                                            variant: "destructive"
                                                        });
                                                    }
                                                }
                                            }}
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => imageInputRef.current?.click()}
                                            className="flex-1"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                            </svg>
                                            Upload New Image
                                        </Button>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => {
                                                const url = window.prompt('Enter image URL:');
                                                if (url) {
                                                    const oldImageUrl = editedArticle.image.url;

                                                    // First update the editor content
                                                    if (editor) {
                                                        const doc = editor.state.doc;
                                                        let tr = editor.state.tr;

                                                        // Find and replace all image nodes
                                                        doc.descendants((node, pos) => {
                                                            if (node.type.name === 'image' && node.attrs.src === oldImageUrl) {
                                                                tr = tr.setNodeMarkup(pos, undefined, {
                                                                    ...node.attrs,
                                                                    src: url
                                                                });
                                                            }
                                                        });

                                                        editor.view.dispatch(tr);
                                                    }

                                                    // Then update the featured image
                                                    setEditedArticle(prev => ({
                                                        ...prev,
                                                        image: {
                                                            url: url,
                                                            thumb: url
                                                        }
                                                    }));
                                                }
                                            }}
                                            className="flex-1"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                            </svg>
                                            Image URL
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            {/* Content Section */}
                            <div>
                                <div className="flex items-center justify-between mb-4">
                                    <h2 className="text-base font-medium">Content</h2>
                                </div>
                                <div className="border rounded-md">
                                    <EditorToolbar />
                                    <EditorContent
                                        editor={editor}
                                        className="prose max-w-none p-4 min-h-[400px] w-full h-full cursor-text"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Sidebar */}
                <div className="w-80 border-l bg-gray-50 overflow-y-auto">
                    <div className="p-4 space-y-3">
                        {/* Info Section */}
                        <div className="bg-white rounded-lg shadow-sm">
                            <button
                                className="w-full px-4 py-3 flex items-center justify-between text-left"
                                onClick={() => toggleSection('info')}
                            >
                                <h2 className="text-base font-medium">Info</h2>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className={`h-5 w-5 transform transition-transform ${activeSection === 'info' ? 'rotate-180' : ''}`}
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            {activeSection === 'info' && (
                                <div className="px-4 pb-4 space-y-4">
                                    <div>
                                        <h3 className="text-sm font-medium mb-2">Status</h3>
                                        <div className="flex gap-3">
                                            <label className="flex items-center space-x-2">
                                                <input
                                                    type="radio"
                                                    value="draft"
                                                    checked={editedArticle.status === 'draft'}
                                                    onChange={(e) => setEditedArticle(prev => ({ ...prev, status: e.target.value }))}
                                                    className="h-4 w-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                                                />
                                                <span className="text-sm text-gray-700">Draft</span>
                                            </label>
                                            <label className="flex items-center space-x-2">
                                                <input
                                                    type="radio"
                                                    value="published"
                                                    checked={editedArticle.status === 'published'}
                                                    onChange={(e) => setEditedArticle(prev => ({ ...prev, status: e.target.value }))}
                                                    className="h-4 w-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                                                />
                                                <span className="text-sm text-gray-700">Published</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium mb-2">Word count</h3>
                                        <p className="text-sm text-gray-600">{editedArticle.content.split(/\s+/).length} words</p>
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium mb-2">Created by</h3>
                                        <p className="text-sm text-gray-600">{editedArticle.author}</p>
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium mb-2">Created on</h3>
                                        <p className="text-sm text-gray-600">
                                            {new Date(editedArticle.date).toLocaleDateString('en-US', {
                                                month: 'short',
                                                day: 'numeric',
                                                year: 'numeric',
                                                hour: 'numeric',
                                                minute: 'numeric',
                                                hour12: true
                                            })}
                                        </p>
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium mb-2">Language</h3>
                                        <p className="text-sm text-gray-600">English (International)</p>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Keywords Score Section */}
                        <div className="bg-white rounded-lg shadow-sm">
                            <button
                                className="w-full px-4 py-3 flex items-center justify-between text-left"
                                onClick={() => toggleSection('keywords')}
                            >
                                <div className="flex items-center gap-2">
                                    <h2 className="text-base font-medium">Keywords score</h2>
                                    <span className="text-sm text-gray-500">{editedArticle.keywords?.length || 0} keywords</span>
                                </div>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className={`h-5 w-5 transform transition-transform ${activeSection === 'keywords' ? 'rotate-180' : ''}`}
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            {activeSection === 'keywords' && (
                                <div className="px-4 pb-4">
                                    <div className="space-y-3">
                                        {editedArticle.keywords?.map((keyword, index) => (
                                            <div key={index} className="flex items-center justify-between group">
                                                <div className="flex items-center gap-2">
                                                    <button
                                                        onClick={() => {
                                                            const newKeywords = [...(editedArticle.keywords || [])];
                                                            newKeywords.splice(index, 1);
                                                            setEditedArticle(prev => ({ ...prev, keywords: newKeywords }));
                                                        }}
                                                        className="text-gray-400 hover:text-gray-600"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                    </button>
                                                    <span className="text-sm text-gray-700">{keyword}</span>
                                                </div>
                                                <span className="text-sm bg-gray-100 px-2 py-0.5 rounded">1</span>
                                            </div>
                                        ))}
                                        <Input
                                            placeholder="Add keywords to track"
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter' && e.currentTarget.value) {
                                                    const newKeyword = e.currentTarget.value.trim();
                                                    if (newKeyword) {
                                                        setEditedArticle(prev => ({
                                                            ...prev,
                                                            keywords: [...(prev.keywords || []), newKeyword]
                                                        }));
                                                        e.currentTarget.value = '';
                                                    }
                                                }
                                            }}
                                            className="mt-3"
                                        />
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Tags Section */}
                        <div className="bg-white rounded-lg shadow-sm">
                            <button
                                className="w-full px-4 py-3 flex items-center justify-between text-left"
                                onClick={() => toggleSection('tags')}
                            >
                                <div className="flex items-center gap-2">
                                    <h2 className="text-base font-medium">Tags</h2>
                                    <span className="text-sm text-gray-500">1 Tags</span>
                                </div>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className={`h-5 w-5 transform transition-transform ${activeSection === 'tags' ? 'rotate-180' : ''}`}
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            {activeSection === 'tags' && (
                                <div className="px-4 pb-4">
                                    <Input
                                        value={editedArticle.tag}
                                        onChange={(e) => setEditedArticle(prev => ({ ...prev, tag: e.target.value }))}
                                        placeholder="Enter tag"
                                    />
                                </div>
                            )}
                        </div>

                        {/* Article Content Section */}
                        <div className="bg-white rounded-lg shadow-sm">
                            <button
                                className="w-full px-4 py-3 flex items-center justify-between text-left"
                                onClick={() => toggleSection('content')}
                            >
                                <div className="flex items-center gap-2">
                                    <h2 className="text-base font-medium">Article Content</h2>
                                    <span className="text-sm text-green-500 bg-green-50 px-2 py-0.5 rounded">Completed</span>
                                </div>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className={`h-5 w-5 transform transition-transform ${activeSection === 'content' ? 'rotate-180' : ''}`}
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            {activeSection === 'content' && (
                                <div className="px-4 pb-4 space-y-4">
                                    <div>
                                        <h3 className="text-sm font-medium mb-2">URL Slug</h3>
                                        <Input
                                            value={editedArticle.urlSlug}
                                            onChange={(e) => setEditedArticle(prev => ({ ...prev, urlSlug: e.target.value }))}
                                            placeholder="url-slug"
                                        />
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium mb-2">Meta Description</h3>
                                        <Textarea
                                            value={editedArticle.metaDescription}
                                            onChange={(e) => setEditedArticle(prev => ({ ...prev, metaDescription: e.target.value }))}
                                            placeholder="SEO meta description"
                                            className="h-24"
                                        />
                                    </div>

                                    <div>
                                        <h3 className="text-sm font-medium mb-2">Excerpt</h3>
                                        <Textarea
                                            value={editedArticle.excerpt}
                                            onChange={(e) => setEditedArticle(prev => ({ ...prev, excerpt: e.target.value }))}
                                            placeholder="Article excerpt"
                                            className="h-24"
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Confirmation Dialogs */}
            <ConfirmationDialog
                isOpen={showSaveConfirmation}
                onClose={() => setShowSaveConfirmation(false)}
                onConfirm={handleSave}
                title="Save Changes"
                message="Are you sure you want to save these changes?"
                confirmText="Save"
                cancelText="Cancel"
            />

            <ConfirmationDialog
                isOpen={showCancelConfirmation}
                onClose={() => setShowCancelConfirmation(false)}
                onConfirm={() => {
                    setShowCancelConfirmation(false);
                    onClose();
                }}
                title="Discard Changes"
                message="You have unsaved changes. Are you sure you want to discard them?"
                confirmText="Discard"
                cancelText="Keep Editing"
            />
        </div>
    );
}; 