const mongoose = require('mongoose');
const connectDB = require('./db');

async function fixDomainIndexes() {
    try {
        // Connect to MongoDB
        await connectDB();

        const Domain = mongoose.model('Domain');
        const db = mongoose.connection;

        console.log('Connected to MongoDB. Fixing domain indexes...');

        // Drop the old url index if it exists
        try {
            await db.collection('domains').dropIndex('url_1');
            console.log('Successfully dropped old url_1 index');
        } catch (error) {
            if (error.code !== 27) { // 27 is the error code for index not found
                console.error('Error dropping old index:', error);
                process.exit(1);
            }
        }

        // Ensure the compound index exists
        await Domain.collection.createIndex(
            { url: 1, userId: 1 },
            {
                unique: true,
                name: 'url_userId_unique',
                background: true
            }
        );

        console.log('Successfully created compound index on url and userId');
        process.exit(0);
    } catch (error) {
        console.error('Error fixing indexes:', error);
        process.exit(1);
    }
}

fixDomainIndexes(); 