const mongoose = require('mongoose');

const planSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    type: {
        type: String,
        enum: ['Free', 'Basic', 'Pro', 'Enterprise'],
        default: 'Free'
    },
    displayType: {
        type: String,
        enum: ['Free', 'Monthly', 'Yearly', 'Daily'],
        default: 'Free'
    },
    features: {
        maxDomains: {
            type: Number,
            default: 1
        },
        maxArticlesPerMonth: {
            type: Number,
            default: 10
        },
        customBranding: {
            type: Boolean,
            default: false
        }
    },
    startDate: {
        type: Date,
        default: Date.now
    },
    endDate: {
        type: Date
    },
    nextPaymentDate: {
        type: Date
    },
    status: {
        type: String,
        enum: ['active', 'expired', 'cancelled'],
        default: 'active'
    }
}, {
    timestamps: true
});

const Plan = mongoose.model('Plan', planSchema);

module.exports = Plan; 