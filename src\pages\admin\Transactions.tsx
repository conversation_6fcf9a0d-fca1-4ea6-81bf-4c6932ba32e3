import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { transactionService, Transaction } from '@/services/transactionService';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
    Loader2,
    Calendar,
    Globe,
    CreditCard,
    RefreshCw,
    ChevronDown,
    ChevronUp,
    ExternalLink,
    Clock,
    User,
    Search,
    Download,
    Users,
    CreditCard as StripeIcon,
    CalendarClock,
    CreditCard as CardIcon,
    CalendarCheck, // Use CalendarCheck instead of CalendarMonth
    CalendarRange, // Use CalendarRange instead of CalendarDays
    DollarSign,
    ChevronLeft,
    ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { useAdmin } from '@/contexts/AdminContext';

const AdminTransactions: React.FC = () => {
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [expandedTransaction, setExpandedTransaction] = useState<string | null>(null);
    const [loadingInvoiceId, setLoadingInvoiceId] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [syncing, setSyncing] = useState<boolean>(false); // New state for syncing
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize] = useState<number>(10);
    const { toast } = useToast();
    const { admin, token } = useAdmin();
    const navigate = useNavigate();

    // Add state variables to track summary statistics
    const [summaryStats, setSummaryStats] = useState({
        totalTransactions: 0,
        monthlyCount: 0,
        monthlyAmount: 0,
        yearlyCount: 0,
        yearlyAmount: 0,
        totalAmount: 0
    });

    // Format date with proper formatting
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        }).format(date);
    };

    // Add these imports for better styling
    // Add this function to format date with predicted next payment date
    const getNextPaymentDate = (transaction: Transaction) => {
        if (!transaction.stripeSubscriptionId || transaction.status !== 'completed') {
            return null; // Only show for active subscriptions
        }

        const createdDate = new Date(transaction.createdAt);
        const nextPaymentDate = new Date(createdDate);

        // Set next payment date based on plan type
        if (transaction.planType === 'Monthly') {
            nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);
        } else if (transaction.planType === 'Yearly') {
            nextPaymentDate.setFullYear(nextPaymentDate.getFullYear() + 1);
        } else {
            return null; // Free plans don't have next payment date
        }

        return nextPaymentDate;
    };

    // Add this function to format date without time
    const formatDateNoTime = (dateString: string) => {
        return transactionService.formatDateNoTime(dateString);
    };

    const fetchTransactions = async () => {
        try {
            if (!admin || !token) {
                navigate('/admin/login');
                return;
            }

            setLoading(true);
            setSyncing(true); // Set syncing to true when fetching
            const data = await transactionService.getAllTransactionsAdmin();
            setTransactions(data);
            setFilteredTransactions(data);

            // Calculate summary statistics
            const stats = {
                totalTransactions: data.length,
                monthlyCount: 0,
                monthlyAmount: 0,
                yearlyCount: 0,
                yearlyAmount: 0,
                totalAmount: 0
            };

            data.forEach(transaction => {
                if (transaction.status === 'completed') {
                    // Add to total amount
                    stats.totalAmount += transaction.amount;

                    // Count by plan type
                    if (transaction.planType === 'Monthly') {
                        stats.monthlyCount++;
                        stats.monthlyAmount += transaction.amount;
                    } else if (transaction.planType === 'Yearly') {
                        stats.yearlyCount++;
                        stats.yearlyAmount += transaction.amount;
                    }
                }
            });

            setSummaryStats(stats);
        } catch (err) {
            console.error('Error fetching transactions:', err);
            toast({
                title: "Error",
                description: "Failed to load transaction history",
                variant: "destructive"
            });

            // If unauthorized, redirect to login
            if ((err as any)?.response?.status === 401) {
                navigate('/admin/login');
            }
        } finally {
            setLoading(false);
            setSyncing(false); // Set syncing to false after fetching
        }
    };

    useEffect(() => {
        // Check if admin is authenticated
        if (!admin || !token) {
            navigate('/admin/login');
            return;
        }

        fetchTransactions();
    }, [admin, token, navigate]);

    useEffect(() => {
        if (searchTerm.trim() === '') {
            setFilteredTransactions(transactions);
            return;
        }

        const lowerSearchTerm = searchTerm.toLowerCase();
        const filtered = transactions.filter(transaction => {
            // Search in user info
            const userInfo = transaction.userId && typeof transaction.userId !== 'string'
                ? `${transaction.userId.firstName || ''} ${transaction.userId.lastName || ''} ${transaction.userId.email || ''}`.toLowerCase()
                : '';

            // Search in domain info
            const domainInfo = transaction.domainId && typeof transaction.domainId !== 'string'
                ? `${transaction.domainId.name || ''} ${transaction.domainId.url || ''}`.toLowerCase()
                : '';

            // Search in transaction ID and other fields
            const transactionInfo = `${transaction._id} ${transaction.status} ${transaction.planType} ${transaction.transactionType}`.toLowerCase();

            return userInfo.includes(lowerSearchTerm) ||
                domainInfo.includes(lowerSearchTerm) ||
                transactionInfo.includes(lowerSearchTerm);
        });

        setFilteredTransactions(filtered);
    }, [searchTerm, transactions]);

    // Pagination calculation
    const totalPages = Math.ceil(filteredTransactions.length / pageSize);
    const paginatedTransactions = filteredTransactions.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
    );

    const goToNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const goToPreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    useEffect(() => {
        // Reset to first page when search term changes
        setCurrentPage(1);
    }, [searchTerm]);

    const toggleTransactionDetails = (transactionId: string) => {
        if (expandedTransaction === transactionId) {
            setExpandedTransaction(null);
        } else {
            setExpandedTransaction(transactionId);
        }
    };

    const handleFetchInvoice = async (transactionId: string) => {
        // If the transaction already has an invoice URL, just open it
        const transaction = transactions.find(t => t._id === transactionId);
        if (transaction?.invoiceUrl) {
            transactionService.openInvoice(transaction.invoiceUrl);
            return;
        }

        try {
            setLoadingInvoiceId(transactionId);
            const invoiceUrl = await transactionService.generateInvoiceAdmin(transactionId);

            if (invoiceUrl) {
                // Update the transaction in the local state
                setTransactions(prevTransactions =>
                    prevTransactions.map(t =>
                        t._id === transactionId
                            ? { ...t, invoiceUrl }
                            : t
                    )
                );

                // Open the invoice URL
                transactionService.openInvoice(invoiceUrl);
            } else {
                toast({
                    title: "Invoice Not Available",
                    description: "Could not retrieve the invoice for this transaction.",
                    variant: "destructive"
                });
            }
        } catch (error) {
            console.error('Error fetching invoice:', error);
            toast({
                title: "Error",
                description: "Failed to retrieve invoice. Please try again.",
                variant: "destructive"
            });
        } finally {
            setLoadingInvoiceId(null);
        }
    };

    // Add these handler functions to open Stripe pages
    const handleOpenStripeCustomer = (customerId: string) => {
        if (customerId) {
            transactionService.openStripeCustomer(customerId);
        }
    };

    const handleOpenStripeSubscription = (subscriptionId: string) => {
        if (subscriptionId) {
            transactionService.openStripeSubscription(subscriptionId);
        }
    };

    const handleOpenStripePaymentIntent = (paymentIntentId: string) => {
        if (paymentIntentId) {
            transactionService.openStripePaymentIntent(paymentIntentId);
        }
    };

    const handleOpenStripeCharge = (chargeId: string) => {
        if (chargeId) {
            transactionService.openStripeCharge(chargeId);
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'completed':
                return <Badge className="bg-green-500 hover:bg-green-600 text-white font-medium px-3 py-1 rounded-full">Completed</Badge>;
            case 'pending':
                return <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white font-medium px-3 py-1 rounded-full">Pending</Badge>;
            case 'failed':
                return <Badge className="bg-red-500 hover:bg-red-600 text-white font-medium px-3 py-1 rounded-full">Failed</Badge>;
            case 'refunded':
                return <Badge className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-3 py-1 rounded-full">Refunded</Badge>;
            default:
                return <Badge className="px-3 py-1 rounded-full">{status}</Badge>;
        }
    };

    // Add the summary cards component
    const TransactionSummaryCards = () => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* Total Subscriptions Card */}
            <Card className="border-0 shadow-md">
                <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="text-sm font-medium text-gray-500">Total Subscriptions</p>
                            <h3 className="text-2xl font-bold text-gray-800 mt-1">{summaryStats.totalTransactions}</h3>
                        </div>
                        <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <CreditCard className="h-6 w-6 text-blue-600" />
                        </div>
                    </div>
                    <p className="text-sm text-green-600 font-medium mt-2">
                        {transactionService.formatCurrency(summaryStats.totalAmount)}
                    </p>
                </CardContent>
            </Card>

            {/* Monthly Subscriptions Card */}
            <Card className="border-0 shadow-md">
                <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="text-sm font-medium text-gray-500">Monthly Subscriptions</p>
                            <h3 className="text-2xl font-bold text-gray-800 mt-1">{summaryStats.monthlyCount}</h3>
                        </div>
                        <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <CalendarCheck className="h-6 w-6 text-blue-600" />
                        </div>
                    </div>
                    <p className="text-sm text-green-600 font-medium mt-2">
                        {transactionService.formatCurrency(summaryStats.monthlyAmount)}
                    </p>
                </CardContent>
            </Card>

            {/* Yearly Subscriptions Card */}
            <Card className="border-0 shadow-md">
                <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="text-sm font-medium text-gray-500">Yearly Subscriptions</p>
                            <h3 className="text-2xl font-bold text-gray-800 mt-1">{summaryStats.yearlyCount}</h3>
                        </div>
                        <div className="h-12 w-12 bg-violet-100 rounded-full flex items-center justify-center">
                            <CalendarRange className="h-6 w-6 text-violet-600" />
                        </div>
                    </div>
                    <p className="text-sm text-green-600 font-medium mt-2">
                        {transactionService.formatCurrency(summaryStats.yearlyAmount)}
                    </p>
                </CardContent>
            </Card>

            {/* Total Revenue Card */}
            <Card className="border-0 shadow-md">
                <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                            <h3 className="text-2xl font-bold text-gray-800 mt-1">
                                {transactionService.formatCurrency(summaryStats.totalAmount)}
                            </h3>
                        </div>
                        <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                            <DollarSign className="h-6 w-6 text-green-600" />
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );

    if (loading) {
        return (
            <Card className="w-full shadow-lg border-0">
                <CardContent className="flex justify-center py-12">
                    <Loader2 className="h-10 w-10 animate-spin text-orange-500" />
                </CardContent>
            </Card>
        );
    }

    return (
        <div>
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">Transaction History</h1>
                <p className="text-gray-600">View and manage all user transactions</p>
            </div>

            {/* Add Summary Cards */}
            {!loading && <TransactionSummaryCards />}

            <Card className="w-full shadow-lg border-0 mb-8">
                <CardContent className="p-6">
                    {/* Update the search and refresh section to be more user-friendly */}
                    <div className="flex justify-between items-center mb-6">
                        <div className="relative w-full max-w-md">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                                placeholder="Search by user, domain, transaction ID..."
                                className="pl-10 pr-4 py-2 w-full border-gray-200 focus:border-blue-300"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                        <div className="flex gap-2">
                            <Button
                                onClick={fetchTransactions}
                                variant="outline"
                                className="flex items-center gap-2 border-gray-200 hover:bg-gray-50"
                                disabled={syncing}
                            >
                                {syncing ? (
                                    <>
                                        <Loader2 className="h-4 w-4 animate-spin" /> Syncing...
                                    </>
                                ) : (
                                    <>
                                        <RefreshCw className="h-4 w-4" /> Refresh
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>

                    {/* Add a summary section above the table */}
                    {filteredTransactions.length > 0 && (
                        <div className="mb-4 flex justify-between items-center">
                            <div className="text-sm text-gray-600">
                                Showing <span className="font-medium">{filteredTransactions.length}</span> transactions
                                {searchTerm && (
                                    <> filtered by <span className="font-medium">"{searchTerm}"</span></>
                                )}
                            </div>
                        </div>
                    )}

                    {filteredTransactions.length === 0 ? (
                        <div className="text-center py-12 text-gray-500 bg-gray-50 rounded-lg">
                            <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                            <p className="font-medium">No transactions found</p>
                            <p className="text-sm mt-1">Try adjusting your search criteria</p>
                        </div>
                    ) : (
                        <div className="rounded-lg overflow-hidden border border-gray-100">
                            <Table>
                                <TableHeader className="bg-gray-50">
                                    <TableRow>
                                        <TableHead className="font-semibold text-gray-700">Date</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Subscription ID</TableHead>
                                        <TableHead className="font-semibold text-gray-700">User</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Domain</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Plan</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Subscription Type</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Amount</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Status</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Invoice</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Details</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {paginatedTransactions.map((transaction) => (
                                        <React.Fragment key={transaction._id}>
                                            <TableRow className="hover:bg-gray-50">
                                                <TableCell className="font-medium text-gray-800">
                                                    <div className="flex items-center gap-2">
                                                        <Calendar className="h-4 w-4 text-gray-500" />
                                                        <span className="font-semibold text-gray-800">{transactionService.formatDateNoTime(transaction.createdAt)}</span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {transaction.stripeSubscriptionId ? (
                                                        <button
                                                            onClick={() => handleOpenStripeSubscription(transaction.stripeSubscriptionId!)}
                                                            className="flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline"
                                                        >
                                                            <span className="text-xs font-mono truncate max-w-[100px]" title={transaction.stripeSubscriptionId}>
                                                                {transaction.stripeSubscriptionId}
                                                            </span>
                                                            <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                        </button>
                                                    ) : (
                                                        <span className="text-xs text-gray-500">-</span>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-3">
                                                        {transaction.userId && typeof transaction.userId !== 'string' ? (
                                                            <>
                                                                <div className="flex-shrink-0">
                                                                    {transaction.userId.avatar ? (
                                                                        <img
                                                                            src={transaction.userId.avatar}
                                                                            alt={`${transaction.userId.firstName} ${transaction.userId.lastName}`}
                                                                            className="h-10 w-10 rounded-full object-cover border border-gray-200"
                                                                        />
                                                                    ) : (
                                                                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                                                                            <User className="h-5 w-5" />
                                                                        </div>
                                                                    )}
                                                                </div>
                                                                <div>
                                                                    <Link
                                                                        to={`/admin/users/${transaction.userId._id}`}
                                                                        className="text-blue-600 hover:underline flex items-center gap-1"
                                                                    >
                                                                        {transaction.userId.firstName} {transaction.userId.lastName}
                                                                        <ExternalLink className="h-3 w-3" />
                                                                    </Link>
                                                                    <div className="text-xs text-gray-500 mt-0.5">
                                                                        {transaction.userId.email}
                                                                    </div>
                                                                </div>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                                                                    <User className="h-5 w-5" />
                                                                </div>
                                                                <span>Unknown User</span>
                                                            </>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        {transaction.domainId && typeof transaction.domainId !== 'string' ? (
                                                            <span className="px-2 py-0.5 bg-blue-50 text-blue-700 text-sm rounded-md border border-blue-100 inline-flex items-center">
                                                                <Globe className="h-3 w-3 mr-1 text-blue-500" />
                                                                {transaction.domainId.url}
                                                            </span>
                                                        ) : (
                                                            <span>Unknown Domain</span>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="font-medium text-gray-700">
                                                    <span className={`px-2 py-1 rounded text-xs font-semibold ${transaction.planType === 'Monthly' ? 'bg-blue-100 text-blue-700' :
                                                        transaction.planType === 'Yearly' ? 'bg-violet-100 text-violet-700' :
                                                            'bg-gray-100 text-gray-700'
                                                        }`}>
                                                        {transaction.planType}
                                                    </span>
                                                    {transaction.metadata?.isProrated && (
                                                        <span className="ml-2 px-2 py-0.5 bg-amber-50 text-amber-700 text-xs rounded border border-amber-200 inline-flex items-center">
                                                            <RefreshCw className="h-3 w-3 mr-1" /> Prorated
                                                        </span>
                                                    )}
                                                </TableCell>
                                                <TableCell className="font-medium text-gray-700">
                                                    <span className={`px-2 py-1 rounded text-xs font-semibold ${transaction.transactionType === 'New Subscription'
                                                        ? 'bg-green-50 text-green-700 border border-green-200'
                                                        : 'bg-blue-50 text-blue-700 border border-blue-200'
                                                        }`}>
                                                        {transaction.transactionType}
                                                    </span>
                                                </TableCell>
                                                <TableCell className="font-semibold text-gray-800">
                                                    <span className={`${transaction.amount >= 100 ? 'text-green-700' : 'text-green-600'} font-bold`}>
                                                        {transactionService.formatCurrency(transaction.amount, transaction.currency)}
                                                    </span>
                                                </TableCell>
                                                <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                                                <TableCell>
                                                    {transaction.status === 'completed' && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => handleFetchInvoice(transaction._id)}
                                                            disabled={loadingInvoiceId === transaction._id}
                                                            className="flex items-center gap-1 p-1 text-green-600 hover:text-green-800"
                                                        >
                                                            {loadingInvoiceId === transaction._id ? (
                                                                <Loader2 className="h-4 w-4 animate-spin" />
                                                            ) : (
                                                                <>
                                                                    <Download className="h-4 w-4" />
                                                                    <span className="text-xs">Invoice</span>
                                                                </>
                                                            )}
                                                        </Button>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => toggleTransactionDetails(transaction._id)}
                                                        className="flex items-center gap-1 p-1"
                                                    >
                                                        {expandedTransaction === transaction._id ? (
                                                            <ChevronUp className="h-4 w-4" />
                                                        ) : (
                                                            <ChevronDown className="h-4 w-4" />
                                                        )}
                                                        <span className="text-xs">Details</span>
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                            {expandedTransaction === transaction._id && (
                                                <TableRow>
                                                    <TableCell colSpan={10} className="bg-gray-50 p-4">
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                                                            <div>
                                                                <h4 className="font-semibold mb-4 border-b pb-2 text-gray-800">Transaction Details</h4>
                                                                <div className="space-y-3">
                                                                    {/* <div className="flex items-start">
                                                                        <p className="font-medium text-gray-700 w-32">Transaction ID:</p>
                                                                        <p className="text-gray-800 break-all bg-yellow-50 px-2 py-1 rounded border border-yellow-200">{transaction._id}</p>
                                                                    </div> */}

                                                                    <div className="flex items-start">
                                                                        <p className="font-medium text-gray-700 w-32">Created:</p>
                                                                        <div className="flex items-center">
                                                                            <Calendar className="h-4 w-4 text-blue-500 mr-1" />
                                                                            <p className="text-gray-800">{transactionService.formatDate(transaction.createdAt)}</p>
                                                                        </div>
                                                                    </div>

                                                                    <div className="flex items-start">
                                                                        <p className="font-medium text-gray-700 w-32">Updated:</p>
                                                                        <div className="flex items-center">
                                                                            <Clock className="h-4 w-4 text-blue-500 mr-1" />
                                                                            <p className="text-gray-800">{transactionService.formatDate(transaction.updatedAt)}</p>
                                                                        </div>
                                                                    </div>

                                                                    {getNextPaymentDate(transaction) && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Next Payment:</p>
                                                                            <div className="flex items-center">
                                                                                <CalendarClock className="h-4 w-4 text-green-600 mr-1" />
                                                                                <p className="text-green-600 font-medium">{formatDateNoTime(getNextPaymentDate(transaction)!.toISOString())}</p>
                                                                            </div>
                                                                        </div>
                                                                    )}

                                                                    <div className="flex items-start">
                                                                        <p className="font-medium text-gray-700 w-32">Payment Method:</p>
                                                                        <div className="flex items-center">
                                                                            <CardIcon className="h-4 w-4 text-gray-500 mr-1" />
                                                                            <p className="text-gray-800 capitalize">{transaction.paymentMethod || 'N/A'}</p>
                                                                        </div>
                                                                    </div>

                                                                    {/* <div className="flex items-start">
                                                                        <p className="font-medium text-gray-700 w-32">Subscription:</p>
                                                                        <Badge className={`${transaction.transactionType === 'New Subscription'
                                                                            ? 'bg-green-50 text-green-700 border border-green-200'
                                                                            : 'bg-blue-50 text-blue-700 border border-blue-200'
                                                                            }`}>
                                                                            {transaction.transactionType}
                                                                        </Badge>
                                                                    </div> */}
                                                                </div>
                                                            </div>

                                                            <div>
                                                                <h4 className="font-semibold mb-4 border-b pb-2 text-gray-800">Stripe Information</h4>
                                                                <div className="space-y-3">
                                                                    {transaction.stripeSessionId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Session ID:</p>
                                                                            <p className="text-gray-800 break-all bg-gray-50 px-2 py-1 rounded border border-gray-200 text-xs font-mono">{transaction.stripeSessionId}</p>
                                                                        </div>
                                                                    )}

                                                                    {transaction.stripeCustomerId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Customer ID:</p>
                                                                            <button
                                                                                onClick={() => handleOpenStripeCustomer(transaction.stripeCustomerId!)}
                                                                                className="text-blue-600 hover:underline flex items-center gap-1 bg-blue-50 px-2 py-1 rounded border border-blue-200 text-xs font-mono"
                                                                            >
                                                                                <span className="break-all">{transaction.stripeCustomerId}</span>
                                                                                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                            </button>
                                                                        </div>
                                                                    )}

                                                                    {transaction.stripePaymentIntentId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Payment Intent:</p>
                                                                            <button
                                                                                onClick={() => handleOpenStripePaymentIntent(transaction.stripePaymentIntentId!)}
                                                                                className="text-blue-600 hover:underline flex items-center gap-1 bg-blue-50 px-2 py-1 rounded border border-blue-200 text-xs font-mono"
                                                                            >
                                                                                <span className="break-all">{transaction.stripePaymentIntentId}</span>
                                                                                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                            </button>
                                                                        </div>
                                                                    )}

                                                                    {transaction.stripeChargeId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Charge ID:</p>
                                                                            <button
                                                                                onClick={() => handleOpenStripeCharge(transaction.stripeChargeId!)}
                                                                                className="text-blue-600 hover:underline flex items-center gap-1 bg-blue-50 px-2 py-1 rounded border border-blue-200 text-xs font-mono"
                                                                            >
                                                                                <span className="break-all">{transaction.stripeChargeId}</span>
                                                                                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                            </button>
                                                                        </div>
                                                                    )}

                                                                    {/* {transaction.invoiceUrl && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Invoice:</p>
                                                                            <a
                                                                                href={transaction.invoiceUrl}
                                                                                target="_blank"
                                                                                rel="noopener noreferrer"
                                                                                className="text-blue-600 hover:underline flex items-center gap-1 bg-blue-50 px-2 py-1 rounded border border-blue-200"
                                                                            >
                                                                                View Invoice <ExternalLink className="h-3 w-3" />
                                                                            </a>
                                                                        </div>
                                                                    )} */}
                                                                </div>
                                                            </div>

                                                            {transaction.billingDetails && Object.keys(transaction.billingDetails).length > 0 && (
                                                                <div className="col-span-1 md:col-span-2">
                                                                    <h4 className="font-semibold mb-4 border-b pb-2 text-gray-800">Billing Details</h4>
                                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                        {transaction.billingDetails.name && (
                                                                            <div className="flex items-start">
                                                                                <p className="font-medium text-gray-700 w-32">Name:</p>
                                                                                <p className="text-gray-800">{transaction.billingDetails.name}</p>
                                                                            </div>
                                                                        )}

                                                                        {transaction.billingDetails.email && (
                                                                            <div className="flex items-start">
                                                                                <p className="font-medium text-gray-700 w-32">Email:</p>
                                                                                <p className="text-gray-800">{transaction.billingDetails.email}</p>
                                                                            </div>
                                                                        )}

                                                                        {transaction.billingDetails.address && (
                                                                            <>
                                                                                {transaction.billingDetails.address.line1 && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">Address:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.line1}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.line2 && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">Address 2:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.line2}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.city && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">City:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.city}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.state && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">State:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.state}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.postal_code && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">Postal Code:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.postal_code}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.country && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">Country:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.country}</p>
                                                                                    </div>
                                                                                )}
                                                                            </>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </React.Fragment>
                                    ))}
                                </TableBody>
                            </Table>

                            {/* Pagination Controls */}
                            {filteredTransactions.length > pageSize && (
                                <div className="flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-100">
                                    <div className="text-sm text-gray-600">
                                        Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, filteredTransactions.length)} of {filteredTransactions.length} transactions
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={goToPreviousPage}
                                            disabled={currentPage === 1}
                                            className="flex items-center gap-1"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            <span>Previous</span>
                                        </Button>
                                        <div className="text-sm font-medium text-gray-700">
                                            Page {currentPage} of {totalPages}
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={goToNextPage}
                                            disabled={currentPage === totalPages}
                                            className="flex items-center gap-1"
                                        >
                                            <span>Next</span>
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default AdminTransactions; 