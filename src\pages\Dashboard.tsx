import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { ArticleList } from '@/components/dashboard/ArticleList';
import { articleService, type Article, type ArticleStats } from '@/services/articleService';
import { toast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

const Dashboard = () => {
    const navigate = useNavigate();
    const [articles, setArticles] = useState<Article[]>([]);
    const [stats, setStats] = useState<ArticleStats>({
        all: 0,
        draft: 0,
        scheduled: 0,
        generated: 0,
        published: 0,
    });
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchData = async () => {
        try {
            setIsLoading(true);
            setError(null);

            // Fetch articles and stats in parallel
            const [articlesData, statsData] = await Promise.all([
                articleService.getArticles(),
                articleService.getArticleStats()
            ]);

            setArticles(articlesData);
            setStats(statsData);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data';
            setError(errorMessage);
            toast({
                variant: "destructive",
                title: "Error",
                description: errorMessage,
            });
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    const handleEdit = async (article: Article) => {
        navigate(`/dashboard/edit/${article.id}`);
    };

    const handleDelete = async (article: Article) => {
        try {
            await articleService.deleteArticle(article.id);
            toast({
                title: "Success",
                description: "Article deleted successfully",
            });
            // Refresh data after deletion
            fetchData();
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to delete article';
            toast({
                variant: "destructive",
                title: "Error",
                description: errorMessage,
            });
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center space-y-4">
                    <h2 className="text-2xl font-bold text-destructive">Error Loading Dashboard</h2>
                    <p className="text-muted-foreground">{error}</p>
                    <button
                        onClick={fetchData}
                        className="text-primary hover:underline"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <DashboardLayout stats={stats}>
            <ArticleList
                articles={articles}
                onEdit={handleEdit}
                onDelete={handleDelete}
            />
        </DashboardLayout>
    );
};

export default Dashboard; 