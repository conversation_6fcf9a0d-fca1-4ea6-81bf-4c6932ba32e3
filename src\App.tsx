import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { DomainProvider, useDomain } from "@/contexts/DomainContext";
import Index from "./pages/Index";
import ArticlePage from "./pages/ArticlePage";
import NotFound from "./pages/NotFound";
import { Dashboard } from "./components/dashboard/Dashboard";
import { ArticleManagement } from "./components/dashboard/ArticleManagement";
import { SettingsView } from "./components/dashboard/SettingsView";
import { WritePage } from "./components/dashboard/WritePage";
import { BulkGenerateView } from "./components/dashboard/BulkGenerateView";
import { OnboardingFlow } from "./components/onboarding/OnboardingFlow";
import { DomainEntry } from "./components/onboarding/DomainEntry";
import AccountPage from '@/pages/settings/AccountPage';
import { ArticlePreviewPage } from './components/preview/ArticlePreviewPage';
import { EditArticlePage } from '@/pages/dashboard/EditArticlePage';
import BlogPreviewPage from './components/preview/BlogPreviewPage';
import { ArticleSingleView } from './components/article/ArticleSingleView';
import PlansPage from './pages/settings/PlansPage';
import PaymentSuccessPage from './pages/dashboard/PaymentSuccessPage';
import TransactionsPage from './pages/dashboard/TransactionsPage';

// Auth Pages
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import GoogleCallback from './pages/auth/GoogleCallback';
import ForgotPassword from './pages/auth/ForgotPassword';
import ResetPassword from './pages/auth/ResetPassword';

// Admin Pages
import AdminLogin from './pages/admin/Login';
import AdminDashboard from './pages/admin/Dashboard';
import AdminLayout from './components/admin/AdminLayout';
import AdminProfile from './pages/admin/Profile';
import AdminProtectedRoute from './components/admin/AdminProtectedRoute';
import { AdminProvider } from './contexts/AdminContext';
import AdminUsers from './pages/admin/Users';
import AdminUserView from './pages/admin/UserView';
import AdminArticlePreview from './pages/admin/ArticlePreview';
import AdminTransactions from './pages/admin/Transactions';
import { Loader2 } from 'lucide-react';

const queryClient = new QueryClient();

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-[#fff7ed] p-4">
        <div className="text-center">
          <Loader2 className="h-12 w-12 text-orange-500 animate-spin mx-auto mb-4" />
          <h1 className="text-2xl font-semibold mb-2">Loading...</h1>
          <p className="text-gray-600">Please wait while we load your data.</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" />;
  }

  return <>{children}</>;
};

// Public Route Component (redirects to dashboard if logged in, or onboarding if new user or has no domains)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading, isNewUser } = useAuth();
  const { domains, isLoading } = useDomain();

  if (loading || isLoading) {
    return <div>Loading...</div>;
  }

  if (user) {
    // If user is new, redirect to onboarding
    if (isNewUser) {
      return <Navigate to="/onboarding" />;
    }
    
    // If user has no domains, redirect to onboarding
    if (domains.length === 0) {
      return <Navigate to="/onboarding" />;
    }
    
    // Otherwise redirect to dashboard
    return <Navigate to="/dashboard/articles" />;
  }

  return <>{children}</>;
};

function App() {
  return (
    <Router>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <DomainProvider>
            <AdminProvider>
              <Toaster position="top-right" />
              <Sonner />
              <Routes>
                {/* Public Routes */}
                <Route
                  path="/login"
                  element={
                    <PublicRoute>
                      <Login />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/register"
                  element={
                    <PublicRoute>
                      <Register />
                    </PublicRoute>
                  }
                />
                <Route path="/auth/google-callback" element={<GoogleCallback />} />

                {/* Forgot Password Routes */}
                <Route
                  path="/forgot-password"
                  element={
                    <PublicRoute>
                      <ForgotPassword />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/reset-password/:token"
                  element={
                    <PublicRoute>
                      <ResetPassword />
                    </PublicRoute>
                  }
                />

                {/* Admin Routes */}
                <Route path="/admin/login" element={<AdminLogin />} />
                <Route
                  path="/admin"
                  element={
                    <AdminProtectedRoute>
                      <AdminLayout />
                    </AdminProtectedRoute>
                  }
                >
                  <Route path="dashboard" element={<AdminDashboard />} />
                  <Route path="profile" element={<AdminProfile />} />
                  <Route path="users" element={<AdminUsers />} />
                  <Route path="users/:id" element={<AdminUserView />} />
                  <Route path="transactions" element={<AdminTransactions />} />
                  {/* Add more admin routes here */}
                </Route>

                {/* Admin Article Preview Route - Outside AdminLayout */}
                <Route
                  path="/admin/articles/preview/:slug"
                  element={
                    <AdminProtectedRoute>
                      <AdminArticlePreview />
                    </AdminProtectedRoute>
                  }
                />

                {/* Protected Routes */}
                <Route
                  path="/dashboard/*"
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  }
                >
                  <Route index element={<Navigate to="/dashboard/articles" replace />} />
                  <Route path="articles" element={<ArticleManagement />} />
                  <Route path="domain/:id" element={<ArticleManagement />} />
                  <Route path="articles/edit/:id" element={<EditArticlePage />} />
                  <Route path="bulk-generate" element={<BulkGenerateView />} />
                  <Route path="settings" element={<SettingsView />} />
                  <Route path="account" element={<AccountPage />} />
                  <Route path="write" element={<WritePage />} />
                  <Route path="write/:id" element={<WritePage />} />
                  <Route path="plans" element={<PlansPage />} />
                  <Route path="transactions" element={<TransactionsPage />} />
                </Route>

                {/* Onboarding Routes */}
                <Route
                  path="/onboarding"
                  element={
                    <ProtectedRoute>
                      <OnboardingFlow onComplete={() => { }} onSkip={() => { }} />
                    </ProtectedRoute>
                  }
                />

                {/* Redirect root to login */}
                <Route path="/" element={<Navigate to="/login" replace />} />

                {/* Article Routes */}
                <Route path="/article/:id" element={<ArticlePage />} />

                {/* Article Single View Route */}
                <Route path="/articles/:slug" element={<ArticleSingleView />} />

                {/* Article Preview Routes */}
                <Route path="/article/:slug" element={<ArticlePreviewPage />} />

                {/* Blog Preview Routes */}
                <Route path="/preview/blog/:blogUrl" element={<BlogPreviewPage />} />
                <Route path="/:blogUrl" element={<BlogPreviewPage />} />

                {/* Error Routes */}
                <Route path="/404" element={<NotFound />} />
                <Route path="*" element={<Navigate to="/404" replace />} />
              </Routes>
            </AdminProvider>
          </DomainProvider>
        </AuthProvider>
      </QueryClientProvider>
    </Router>
  );
}

export default App;
