import { useState } from 'react';
import { Routes, Route, useNavigate, useLocation, Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { WriteOptionsModal } from './WriteOptionsModal';
import { UpgradeModal } from './UpgradeModal';
import PaymentSuccessModal from '@/components/PaymentSuccessModal';
import { useDomain } from '@/contexts/DomainContext';

export const Dashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showWriteOptions, setShowWriteOptions] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const { paymentSuccess, paymentPlanType, clearPaymentSuccess } = useDomain();

  const getActiveView = () => {
    const path = location.pathname;
    if (path.includes('/dashboard/topics')) return 'topics';
    if (path.includes('/dashboard/settings')) return 'settings';
    return 'articles';
  };

  const handleViewChange = (view: string) => {
    switch (view) {
      case 'topics':
        navigate('/dashboard/topics');
        break;
      case 'settings':
        navigate('/dashboard/settings');
        break;
      default:
        navigate('/dashboard/articles');
    }
  };

  const handleWrite = () => {
    setShowWriteOptions(true);
  };

  const handleGenerateWithAI = () => {
    setShowWriteOptions(false);
    setShowUpgradeModal(true);
  };

  const handleWriteManually = () => {
    setShowWriteOptions(false);
    navigate('/dashboard/write');
  };

  const handleSelectPlan = () => {
    setShowUpgradeModal(false);
    // Handle plan selection
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar
        activeView={getActiveView()}
        onViewChange={handleViewChange}
        onWrite={handleWrite}
      />
      <div className="flex-1 flex flex-col ml-64">
        <Header />
        <main className="flex-1 p-6">
          <Outlet />
        </main>
      </div>

      {showWriteOptions && (
        <WriteOptionsModal
          onClose={() => setShowWriteOptions(false)}
          onGenerateWithAI={handleGenerateWithAI}
          onWriteManually={handleWriteManually}
        />
      )}

      {showUpgradeModal && (
        <UpgradeModal
          onClose={() => setShowUpgradeModal(false)}
          onSelectPlan={handleSelectPlan}
        />
      )}

      <PaymentSuccessModal
        isOpen={paymentSuccess}
        onClose={clearPaymentSuccess}
        planType={paymentPlanType || 'Monthly'}
      />
    </div>
  );
};
