import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Loader2, CheckCircle, RefreshCw, ExternalLink } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { transactionService, Transaction } from '@/services/transactionService';
import { paymentService } from '@/services/paymentService';
import { Button } from '@/components/ui/button';
import apiClient from '@/lib/api-client';
import { useDomain } from '@/contexts/DomainContext';

const PaymentSuccessPage = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [transaction, setTransaction] = useState<Transaction | null>(null);
    const [subscription, setSubscription] = useState<any>(null);
    const [isProrated, setIsProrated] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const { refreshDomains } = useDomain();

    const sessionId = searchParams.get('session_id');
    const domainId = searchParams.get('domain_id');
    const planType = searchParams.get('plan_type');

    const fetchTransactionDetails = async () => {
        if (!sessionId) return;

        try {
            // Fetch transactions to find the one matching this session
            const transactions = await transactionService.getTransactions();
            const currentTransaction = transactions.find(t => t.stripeSessionId === sessionId);

            if (currentTransaction) {
                setTransaction(currentTransaction);

                // Check if this was a prorated payment
                if (currentTransaction.metadata?.isProrated === true ||
                    currentTransaction.metadata?.isProrated === 'true') {
                    setIsProrated(true);
                }

                return currentTransaction;
            }
            return null;
        } catch (error) {
            console.error('Error fetching transaction details:', error);
            return null;
        }
    };

    const handleRefreshStatus = async () => {
        if (!sessionId) return;

        setRefreshing(true);
        try {
            // Verify payment status with Stripe
            const paymentInfo = await transactionService.verifyStripePaymentStatus(sessionId);

            // Remove automatic sync with Stripe to prevent unnecessary API calls
            // await transactionService.syncTransactionStatuses();

            // Refresh transaction details
            const updatedTransaction = await fetchTransactionDetails();

            if (updatedTransaction) {
                toast({
                    title: "Transaction Updated",
                    description: `Payment status: ${paymentInfo.status}${paymentInfo.paymentIntentId ? `. Payment ID: ${paymentInfo.paymentIntentId.substring(0, 10)}...` : ''}`,
                });

                // If this was a prorated payment and status is completed, update the plan
                if (updatedTransaction.status === 'completed' &&
                    (updatedTransaction.metadata?.isProrated === true ||
                        updatedTransaction.metadata?.isProrated === 'true')) {
                    try {
                        await apiClient.post('/payments/update-plan-after-proration', { sessionId });
                        console.log('Plan updated after proration payment refresh');
                    } catch (error) {
                        console.error('Failed to update plan after proration:', error);
                    }
                }

                // Fetch subscription details again
                if (domainId) {
                    const subData = await paymentService.getSubscription(domainId);
                    setSubscription(subData.subscription);

                    // Force refresh subscription data
                    try {
                        await paymentService.refreshSubscriptionData(domainId);
                        console.log('Subscription data refreshed after payment verification');
                    } catch (err) {
                        console.error('Error refreshing subscription data:', err);
                    }
                }
            } else {
                toast({
                    title: "Transaction Updated",
                    description: `Payment status: ${paymentInfo.status}`,
                });
            }
        } catch (error) {
            console.error('Error refreshing payment status:', error);
            toast({
                title: "Error",
                description: "Failed to refresh payment status. Please try again.",
                variant: 'destructive'
            });
        } finally {
            setRefreshing(false);
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);

                // Show a toast notification immediately
                toast({
                    title: 'Payment Processing',
                    description: `Processing your ${planType} subscription payment...`,
                });

                // Verify payment status first
                if (sessionId) {
                    const paymentInfo = await transactionService.verifyStripePaymentStatus(sessionId);
                    console.log('Payment status:', paymentInfo);

                    // Remove automatic sync with Stripe to prevent unnecessary API calls
                    // await transactionService.syncTransactionStatuses();
                }

                // Fetch transaction details
                const currentTransaction = await fetchTransactionDetails();

                if (currentTransaction) {
                    // If this was a prorated payment, we need to update the plan manually
                    if (currentTransaction.metadata?.isProrated === true ||
                        currentTransaction.metadata?.isProrated === 'true') {
                        try {
                            await apiClient.post('/payments/update-plan-after-proration', { sessionId });
                            console.log('Plan updated after proration payment');
                        } catch (error) {
                            console.error('Failed to update plan after proration:', error);
                        }
                    }

                    // Show success notification if transaction is completed
                    if (currentTransaction.status === 'completed') {
                        toast({
                            title: 'Payment Successful',
                            description: `Your ${planType} subscription payment was successful.`,
                        });
                    }
                } else {
                    // If transaction not found, verify payment status
                    await handleRefreshStatus();
                }

                // Fetch subscription details
                if (domainId) {
                    const subData = await paymentService.getSubscription(domainId);
                    setSubscription(subData.subscription);

                    // Force refresh subscription data
                    try {
                        await paymentService.refreshSubscriptionData(domainId);
                        console.log('Subscription data refreshed in payment success page');
                    } catch (err) {
                        console.error('Error refreshing subscription data:', err);
                    }
                }

                // Refresh domain data to ensure plan information is up to date
                await refreshDomains();

                // Set a timer to redirect
                const redirectTimer = setTimeout(() => {
                    navigate(`/dashboard/domain/${domainId}?payment_success=true&plan_type=${planType || 'Monthly'}`);
                }, 5000);

                return () => clearTimeout(redirectTimer);
            } catch (error) {
                console.error('Error fetching payment details:', error);
                toast({
                    title: 'Error',
                    description: 'There was an error processing your payment. Please contact support.',
                    variant: 'destructive'
                });
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [navigate, planType, sessionId, domainId, refreshDomains, toast]);

    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    {loading ? (
                        <Loader2 className="h-12 w-12 text-orange-500 animate-spin mx-auto mb-4" />
                    ) : (
                        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    )}
                    <CardTitle className="text-2xl font-semibold">
                        {loading ? 'Processing Payment' : 'Payment Successful!'}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <p className="text-center text-gray-600">Verifying your payment details...</p>
                    ) : (
                        <div className="space-y-4">
                            <div className="bg-green-50 p-4 rounded-md border border-green-200 text-green-800">
                                <p className="font-medium">Your account has been upgraded!</p>
                                <p className="text-sm mt-2">
                                    You now have access to all the premium features of your {planType} plan.
                                </p>
                                {transaction ? (
                                    <div className="mt-4 text-sm">
                                        {isProrated && (
                                            <p className="text-blue-600 font-medium mb-2">
                                                Plan changed with proration applied
                                                {transaction.metadata?.fromPlan && ` from ${transaction.metadata.fromPlan} plan`}
                                                {transaction.metadata?.discount && ` (Discount: ${transactionService.formatCurrency(parseFloat(transaction.metadata.discount), transaction.currency)})`}
                                            </p>
                                        )}
                                        <p><span className="font-medium">Transaction ID:</span> {transaction._id}</p>
                                        <p><span className="font-medium">Amount:</span> {transactionService.formatCurrency(transaction.amount, transaction.currency)}</p>
                                        <p><span className="font-medium">Date:</span> {transactionService.formatDate(transaction.createdAt)}</p>
                                        {transaction.stripePaymentIntentId && (
                                            <div className="mt-2">
                                                <p className="font-medium mb-1">Payment ID:</p>
                                                <button
                                                    onClick={() => transactionService.openStripePaymentIntent(transaction.stripePaymentIntentId)}
                                                    className="flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline w-full"
                                                >
                                                    <div className="bg-blue-50 border border-blue-100 p-2 rounded text-xs break-all font-mono flex-grow text-left text-blue-700 font-bold">
                                                        {transaction.stripePaymentIntentId}
                                                    </div>
                                                    <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                </button>
                                            </div>
                                        )}
                                        {transaction.stripeChargeId && (
                                            <div className="mt-2">
                                                <p className="font-medium mb-1">Charge ID:</p>
                                                <button
                                                    onClick={() => transactionService.openStripeCharge(transaction.stripeChargeId)}
                                                    className="flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline w-full"
                                                >
                                                    <div className="bg-blue-50 border border-blue-100 p-2 rounded text-xs break-all font-mono flex-grow text-left text-blue-700 font-bold">
                                                        {transaction.stripeChargeId}
                                                    </div>
                                                    <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                </button>
                                            </div>
                                        )}
                                        {!transaction.stripePaymentIntentId && !transaction.stripeChargeId && (
                                            <div className="mt-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={handleRefreshStatus}
                                                    disabled={refreshing}
                                                    className="flex items-center gap-2 text-xs"
                                                >
                                                    {refreshing ? (
                                                        <>
                                                            <Loader2 className="h-3 w-3 animate-spin" />
                                                            Refreshing...
                                                        </>
                                                    ) : (
                                                        <>
                                                            <RefreshCw className="h-3 w-3" />
                                                            Refresh Payment Details
                                                        </>
                                                    )}
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <div className="mt-4 text-sm">
                                        <p>Processing your transaction details...</p>
                                        <div className="mt-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={handleRefreshStatus}
                                                disabled={refreshing}
                                                className="flex items-center gap-2 text-xs"
                                            >
                                                {refreshing ? (
                                                    <>
                                                        <Loader2 className="h-3 w-3 animate-spin" />
                                                        Refreshing...
                                                    </>
                                                ) : (
                                                    <>
                                                        <RefreshCw className="h-3 w-3" />
                                                        Refresh Payment Details
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <p className="text-center text-gray-600">
                                Redirecting to dashboard in a few seconds...
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default PaymentSuccessPage; 