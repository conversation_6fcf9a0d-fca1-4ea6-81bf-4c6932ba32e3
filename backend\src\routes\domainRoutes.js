const express = require('express');
const router = express.Router();
const {
    getDomains,
    getDomain,
    createDomain,
    updateDomain,
    deleteDomain,
    getArticlesByDomain,
    setDefaultDomain,
    deleteArticlesByDomain,
    updateDomainBrandInfo,
    updateDomainArticleSettings,
    updateDomainHosting,
    verifyDomainHosting,
    updateDomainDesign,
    updateDomainNavigation
} = require('../controllers/domainController');
const { protect } = require('../middleware/auth');
const Domain = require('../models/Domain');

// Domain routes
router.route('/')
    .get(getDomains)
    .post(createDomain);

router.route('/:id')
    .get(getDomain)
    .put(updateDomain)
    .delete(deleteDomain);

router.route('/:id/articles')
    .get(getArticlesByDomain)
    .delete(deleteArticlesByDomain);

// Domain setting routes
router.route('/:id/default')
    .put(setDefaultDomain);

router.route('/:id/brand-info')
    .put(updateDomainBrandInfo);

router.route('/:id/article-settings')
    .put(updateDomainArticleSettings);

router.route('/:id/hosting')
    .put(updateDomainHosting);

router.route('/:id/verify-hosting')
    .post(verifyDomainHosting);

router.route('/:id/design')
    .put(updateDomainDesign);

router.route('/:id/navigation')
    .put(updateDomainNavigation);

// Get domain credits
router.get('/:id/credits', protect, async (req, res) => {
    try {
        const domain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });

        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        res.json({
            domainId: domain._id,
            name: domain.name,
            subscription: {
                active: domain.subscription?.active || false,
                planType: domain.subscription?.planType || 'Free',
                credits: domain.subscription?.credits || 0,
                expiresAt: domain.subscription?.expiresAt
            }
        });
    } catch (error) {
        console.error('Error fetching domain credits:', error);
        res.status(500).json({ error: 'Failed to fetch domain credits', details: error.message });
    }
});

// Reset domain credits (admin only)
router.post('/:id/reset-credits', protect, async (req, res) => {
    try {
        const domain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });

        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        // Only allow resetting credits if domain has an active subscription
        if (!domain.subscription?.active) {
            return res.status(400).json({ error: 'Domain does not have an active subscription' });
        }

        // Set credits based on plan type
        let credits = 0;
        if (domain.subscription.planType === 'Daily') {
            credits = 1;
        } else if (domain.subscription.planType === 'Monthly') {
            credits = 30;
        } else if (domain.subscription.planType === 'Yearly') {
            credits = 365;
        }

        // Update domain credits
        await Domain.findByIdAndUpdate(domain._id, {
            'subscription.credits': credits
        });

        res.json({
            success: true,
            message: `Credits reset successfully for ${domain.name}`,
            credits: credits
        });
    } catch (error) {
        console.error('Error resetting domain credits:', error);
        res.status(500).json({ error: 'Failed to reset domain credits', details: error.message });
    }
});

module.exports = router;
