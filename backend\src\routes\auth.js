const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { protect } = require('../middleware/auth');
const { updateProfile, changePassword, deleteAccount, getUserById, updateProfilePicture, forgotPassword, resetPassword, sendWelcomeEmail } = require('../controllers/userController');
const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;

// Configure passport with Google strategy
passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: `${process.env.API_URL || 'http://localhost:5000'}/api/auth/google/callback`,
    scope: ['profile', 'email']
}, async (accessToken, refreshToken, profile, done) => {
    try {
        // Check if user exists by email
        let user = await User.findOne({ email: profile.emails[0].value });

        if (user) {
            // If user exists but doesn't have googleId, update it
            if (!user.googleId) {
                user.googleId = profile.id;
                await user.save();
            }
            return done(null, user);
        } else {
            // If user doesn't exist, create a new user
            // Get name from profile
            let firstName = profile.name?.givenName || profile.displayName || '';
            let lastName = profile.name?.familyName || '';

            // Generate a random secure password for the account
            const randomPassword = Math.random().toString(36).slice(-8) +
                Math.random().toString(36).toUpperCase().slice(-4) +
                Math.floor(Math.random() * 10000).toString();

            const newUser = await User.create({
                googleId: profile.id,
                firstName,
                lastName,
                email: profile.emails[0].value,
                avatar: profile.photos?.[0]?.value || '',
                password: randomPassword // Required field, but won't be used for Google auth
            });

            return done(null, newUser);
        }
    } catch (error) {
        console.error('Google auth error:', error);
        return done(error, null);
    }
}));

// Serialize and deserialize user
passport.serializeUser((user, done) => {
    done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
    try {
        const user = await User.findById(id);
        done(null, user);
    } catch (error) {
        done(error, null);
    }
});

// Generate JWT Token
const generateToken = (id) => {
    return jwt.sign({ id }, process.env.JWT_SECRET, {
        expiresIn: '30d'
    });
};

// @route   POST /api/auth/register
// @desc    Register user
// @access  Public
router.post('/register', async (req, res) => {
    try {
        const { name, email, password } = req.body;

        // Validate required fields
        if (!name || !name.trim()) {
            return res.status(400).json({
                success: false,
                message: 'Name is required'
            });
        }

        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email is required'
            });
        }

        if (!password) {
            return res.status(400).json({
                success: false,
                message: 'Password is required'
            });
        }

        // Split name into firstName and lastName
        let firstName = name.trim();
        let lastName = '';

        // If name contains a space, split it into firstName and lastName
        if (name.includes(' ')) {
            const nameParts = name.trim().split(' ');
            firstName = nameParts[0];
            lastName = nameParts.slice(1).join(' ');
        }

        // Check if user exists
        const userExists = await User.findOne({ email });
        if (userExists) {
            return res.status(400).json({
                success: false,
                message: 'User already exists'
            });
        }

        // Create user
        const user = await User.create({
            firstName,
            lastName,
            email,
            password
        });

        if (user) {
            // For display purposes, combine firstName and lastName
            const displayName = user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;

            // Send welcome email
            sendWelcomeEmail(user).catch(err => console.error('Error sending welcome email:', err));

            res.status(201).json({
                success: true,
                token: generateToken(user._id),
                user: {
                    id: user._id,
                    name: displayName, // Return combined name
                    firstName: user.firstName,
                    lastName: user.lastName,
                    email: user.email,
                    avatar: user.avatar
                }
            });
        }
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Error registering user'
        });
    }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        // Check if email and password are provided
        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Please provide email and password'
            });
        }

        // Find user by email and explicitly select password
        const user = await User.findOne({ email }).select('+password');

        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Check if this is a Google-authenticated user without a password
        if (user.googleId && !user.password) {
            return res.status(400).json({
                success: false,
                message: 'This account uses Google Sign-In. Please sign in with Google.',
                useGoogle: true
            });
        }

        // Check if password matches
        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Update last login
        user.lastLogin = Date.now();
        await user.save();

        // Create token
        const token = generateToken(user._id);

        // Remove password from response
        const userResponse = user.toObject();
        delete userResponse.password;

        // For display purposes, combine firstName and lastName
        const displayName = user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;
        userResponse.name = displayName;

        res.status(200).json({
            success: true,
            token,
            user: userResponse
        });
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Error logging in'
        });
    }
});

// @route   GET /api/auth/google
// @desc    Auth with Google
// @access  Public
router.get('/google', passport.authenticate('google', {
    scope: ['profile', 'email']
}));

// @route   GET /api/auth/google/callback
// @desc    Google auth callback
// @access  Public
router.get('/google/callback',
    passport.authenticate('google', { failureRedirect: `${process.env.FRONTEND_URL}/login?error=google_auth_failed` }),
    async (req, res) => {
        try {
            // Check if this is a new user (just created) or existing user
            const user = req.user;
            const isNewUser = user.createdAt &&
                ((new Date() - new Date(user.createdAt)) < 10000); // Less than 10 seconds old = new user

            // Generate JWT token
            const token = generateToken(user._id);

            // Update last login time
            user.lastLogin = Date.now();
            await user.save();

            // Send welcome email for new users
            if (isNewUser) {
                sendWelcomeEmail(user).catch(err => console.error('Error sending welcome email:', err));
            }

            // Log the redirect URL for debugging
            const redirectUrl = `${process.env.FRONTEND_URL}/auth/google-callback?token=${token}${isNewUser ? '&new_user=true' : ''}`;
            console.log('Redirecting to:', redirectUrl);

            // Redirect to frontend with token and new_user flag if applicable
            res.redirect(redirectUrl);
        } catch (error) {
            console.error('Google callback error:', error);
            res.redirect(`${process.env.FRONTEND_URL}/login?error=google_auth_failed`);
        }
    }
);

// @route   POST /api/auth/forgot-password
// @desc    Send password reset email
// @access  Public
router.post('/forgot-password', forgotPassword);

// @route   POST /api/auth/reset-password
// @desc    Reset password with token
// @access  Public
router.post('/reset-password', resetPassword);

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', protect, async (req, res) => {
    try {
        // Get fresh user data
        const user = await User.findById(req.user.id).select('-password');
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Add combined name field
        const userObj = user.toObject();
        userObj.name = user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;

        res.json({
            success: true,
            user: userObj
        });
    } catch (error) {
        console.error('Error fetching user:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching user data'
        });
    }
});

// Protected routes
router.put('/profile', protect, updateProfile);
router.put('/profile/picture', protect, updateProfilePicture);
router.put('/change-password', protect, changePassword);
router.delete('/delete-account', protect, deleteAccount);

// @route   GET /api/auth/user/:id
// @desc    Get user by ID (public data)
// @access  Public
router.get('/user/:id', getUserById);

module.exports = router; 