.article-video-container {
    margin: 2rem 0;
    width: 100%;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    background-color: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.article-video {
    width: 100%;
    height: auto;
    display: block;
}

.video-caption {
    padding: 1rem;
    margin: 0;
    font-style: italic;
    color: #666;
    font-size: 0.9rem;
}

.video-credit {
    padding: 0.5rem 1rem;
    margin: 0;
    font-size: 0.8rem;
    color: #888;
    border-top: 1px solid #eee;
}

.video-credit a {
    color: #0066cc;
    text-decoration: none;
}

.video-credit a:hover {
    text-decoration: underline;
}

/* Article Preview Styles */
.article-preview-content {
    width: 100%;
    max-width: none !important;
}

.article-preview-content .prose {
    max-width: none !important;
    width: 100% !important;
}

.article-preview-content img {
    width: 100%;
    height: auto;
    max-width: 100%;
    margin: 2rem auto;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.article-preview-content p {
    width: 100%;
    margin: 1.5rem 0;
    line-height: 1.8;
    font-size: 1.1rem;
    max-width: none !important;
}

.article-preview-content h1,
.article-preview-content h2,
.article-preview-content h3,
.article-preview-content h4,
.article-preview-content h5,
.article-preview-content h6 {
    width: 100%;
    margin: 2rem 0 1rem;
    line-height: 1.4;
    max-width: none !important;
}

.article-preview-content ul,
.article-preview-content ol {
    width: 100%;
    padding-left: 2rem;
    margin: 1.5rem 0;
    max-width: none !important;
}

.article-preview-content blockquote {
    width: 100%;
    margin: 2rem 0;
    padding: 1rem 2rem;
    border-left: 4px solid #e2e8f0;
    background-color: #f8fafc;
    font-style: italic;
    max-width: none !important;
}

/* Override any Tailwind prose max-width constraints */
.prose {
    max-width: none !important;
    width: 100% !important;
}

.prose * {
    max-width: none !important;
} 