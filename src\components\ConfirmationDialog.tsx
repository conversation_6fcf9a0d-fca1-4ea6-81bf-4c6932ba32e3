import React, { ReactNode } from 'react';
import {
    <PERSON><PERSON>,
    Di<PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>ooter,
    Di<PERSON>Header,
    DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface ConfirmationDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    message: ReactNode;
    confirmText?: string;
    cancelText?: string;
    isDestructive?: boolean;
    isLoading?: boolean;
}

export function ConfirmationDialog({
    isOpen,
    onClose,
    onConfirm,
    title,
    message,
    confirmText = 'Confirm',
    cancelText = 'Cancel',
    isDestructive = false,
    isLoading = false,
}: ConfirmationDialogProps) {
    const handleClose = () => {
        if (!isLoading) {
            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>{title}</DialogTitle>
                    {/* Removed DialogDescription and replaced with a div to avoid hydration errors */}
                    <div className="text-sm text-muted-foreground mt-2">
                        {message}
                    </div>
                </DialogHeader>
                <DialogFooter>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={handleClose} disabled={isLoading}>
                            {cancelText}
                        </Button>
                        <Button
                            variant={isDestructive ? "destructive" : "default"}
                            onClick={onConfirm}
                            className={isDestructive ? "bg-red-500 hover:bg-red-600" : ""}
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Processing...
                                </>
                            ) : (
                                confirmText
                            )}
                        </Button>
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
} 