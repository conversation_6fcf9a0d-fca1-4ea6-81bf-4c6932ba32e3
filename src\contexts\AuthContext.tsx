import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import apiClient from '../lib/api-client';
import { userService, UserResponse, ForgotPasswordData, ResetPasswordData } from '../services/userService';

interface User {
    id: string;
    name: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
}

interface AuthContextType {
    user: User | null;
    token: string | null;
    loading: boolean;
    error: string | null;
    isNewUser: boolean;
    login: (email: string, password: string) => Promise<void>;
    loginWithGoogle: () => Promise<void>;
    register: (name: string, email: string, password: string) => Promise<void>;
    logout: () => void;
    setAuthToken: (token: string) => void;
    updateNewUserStatus: (value: boolean) => void;
    changePassword: (currentPassword: string, newPassword: string) => Promise<UserResponse>;
    updateProfile: (firstName: string, lastName: string) => Promise<UserResponse>;
    updateProfilePicture: (avatar: string) => Promise<UserResponse>;
    deleteAccount: () => Promise<void>;
    forgotPassword: (email: string) => Promise<UserResponse>;
    resetPassword: (token: string, password: string) => Promise<UserResponse>;
    clearNewUserFlag: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [user, setUser] = useState<User | null>(null);
    const [token, setToken] = useState<string | null>(localStorage.getItem('token'));
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isNewUser, setIsNewUser] = useState<boolean>(localStorage.getItem('isNewUser') === 'true');

    // Configure axios
    apiClient.defaults.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

    // Load user on mount or when token changes
    useEffect(() => {
        let isMounted = true;

        const fetchUser = async () => {
            // Only try to fetch user if we have a token
            if (!token) {
                if (isMounted) {
                    setLoading(false);
                    setUser(null);
                }
                return;
            }

            // Set loading to true when starting to fetch user data
            if (isMounted) {
                setLoading(true);
            }

            try {
                // The API client interceptor will automatically add the Authorization header from localStorage
                // No need to manually set it here since we already stored the token in localStorage
                const response = await apiClient.get('/auth/me');
                if (response.data.success && isMounted) {
                    const userData = {
                        id: response.data.user._id,
                        name: response.data.user.name || `${response.data.user.firstName} ${response.data.user.lastName}`.trim(),
                        firstName: response.data.user.firstName,
                        lastName: response.data.user.lastName,
                        email: response.data.user.email,
                        avatar: response.data.user.avatar
                    };
                    setUser(userData);
                }
            } catch (error) {
                if (isMounted) {
                    // Clear invalid token
                    if (axios.isAxiosError(error) && error.response?.status === 401) {
                        localStorage.removeItem('token');
                        setToken(null);
                        setUser(null);
                        // The API client interceptor will handle the absence of token automatically
                    }
                }
            } finally {
                if (isMounted) {
                    setLoading(false);
                }
            }
        };

        fetchUser();

        // Cleanup function to prevent state updates after unmount
        return () => {
            isMounted = false;
        };
    }, [token]); // Only re-run when token changes

    const login = async (email: string, password: string) => {
        try {
            setError(null);
            const response = await apiClient.post('/auth/login', { email, password });
            const { token } = response.data;

            // First set the token in localStorage
            localStorage.setItem('token', token);

            // Set the token state - this will trigger the useEffect to fetch user data
            setToken(token);

            // Don't set user directly - let the useEffect fetch fresh data via /auth/me
            // This ensures consistent data loading and proper triggering of dependent contexts
        } catch (error: any) {
            setError(error.response?.data?.message || 'An error occurred during login');
            throw error;
        }
    };

    const loginWithGoogle = async () => {
        try {
            setError(null);
            // Get the API URL from environment variables
            const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

            // Redirect to Google OAuth endpoint
            window.location.href = `${apiUrl}/auth/google`;

            // The rest of the authentication flow will be handled by the GoogleCallback component
            // after Google redirects back to our application
        } catch (error: any) {
            setError(error.response?.data?.message || 'An error occurred during Google login');
            throw error;
        }
    };

    const register = async (name: string, email: string, password: string) => {
        try {
            setError(null);
            const response = await apiClient.post('/auth/register', {
                name,
                email,
                password
            });

            const { token } = response.data;

            // First set the token in localStorage
            localStorage.setItem('token', token);
            
            // Set isNewUser flag to true and store in localStorage
            setIsNewUser(true);
            localStorage.setItem('isNewUser', 'true');

            // Set the token state - this will trigger the useEffect to fetch user data
            setToken(token);

            // Don't set user directly - let the useEffect fetch fresh data via /auth/me
            // This ensures consistent data loading and proper triggering of dependent contexts
        } catch (error: any) {
            setError(error.response?.data?.message || 'An error occurred during registration');
            throw error;
        }
    };

    const logout = () => {
        // First remove the token
        localStorage.removeItem('token');
        localStorage.removeItem('isNewUser');
        setToken(null);
        setIsNewUser(false);

        // Then clear the user
        setUser(null);
    };

    const setAuthToken = (authToken: string) => {
        // Store token and update state - this will trigger useEffect to fetch user data
        localStorage.setItem('token', authToken);
        setToken(authToken);
    };

    const changePassword = async (currentPassword: string, newPassword: string) => {
        try {
            const response = await userService.changePassword({
                currentPassword,
                newPassword
            });
            return response;
        } catch (error) {
            console.error('Error changing password:', error);
            throw error;
        }
    };

    const updateProfile = async (firstName: string, lastName: string) => {
        try {
            const response = await userService.updateProfile({ firstName, lastName });
            setUser(prevUser => {
                if (!prevUser) return null;
                const name = lastName ? `${firstName} ${lastName}` : firstName;
                return {
                    ...prevUser,
                    name,
                    firstName,
                    lastName
                };
            });
            return response;
        } catch (error) {
            console.error('Error updating profile:', error);
            throw error;
        }
    };

    const updateProfilePicture = async (avatar: string) => {
        try {
            const response = await userService.updateProfilePicture(avatar);
            setUser(prevUser => ({
                ...prevUser!,
                avatar
            }));
            return response;
        } catch (error) {
            console.error('Error updating profile picture:', error);
            throw error;
        }
    };

    const deleteAccount = async () => {
        try {
            await userService.deleteAccount();
            // Clear all user data and log out
            localStorage.clear(); // Clear all localStorage data
            setToken(null);
            setUser(null);
        } catch (error) {
            console.error('Error deleting account:', error);
            throw error;
        }
    };

    const forgotPassword = async (email: string) => {
        try {
            return await userService.forgotPassword({ email });
        } catch (error) {
            console.error('Error in forgot password:', error);
            throw error;
        }
    };

    const resetPassword = async (token: string, password: string) => {
        try {
            return await userService.resetPassword({ token, password });
        } catch (error) {
            console.error('Error in reset password:', error);
            throw error;
        }
    };

    const clearNewUserFlag = () => {
        setIsNewUser(false);
        localStorage.removeItem('isNewUser');
    };
    
    const updateNewUserStatus = (value: boolean) => {
        localStorage.setItem('isNewUser', value ? 'true' : 'false');
        setIsNewUser(value);
    };

    return (
        <AuthContext.Provider value={{
            user,
            token,
            loading,
            error,
            isNewUser,
            login,
            loginWithGoogle,
            register,
            logout,
            setAuthToken,
            updateNewUserStatus,
            changePassword,
            updateProfile,
            updateProfilePicture,
            deleteAccount,
            forgotPassword,
            resetPassword,
            clearNewUserFlag
        }}>
            {children}
        </AuthContext.Provider>
    );
};

export default AuthContext; 