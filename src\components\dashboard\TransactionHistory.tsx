import React, { useEffect, useState } from 'react';
import { transactionService, Transaction } from '@/services/transactionService';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Loader2, Calendar, Globe, CreditCard, RefreshCw, ChevronDown, ChevronUp, ExternalLink, Clock, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useToast } from '@/components/ui/use-toast';
import { useDomain } from '@/contexts/DomainContext';
import { paymentService } from '@/services/paymentService';

const TransactionHistory: React.FC = () => {
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [syncing, setSyncing] = useState<boolean>(false);
    const [refreshingId, setRefreshingId] = useState<string | null>(null);
    const [loadingInvoiceId, setLoadingInvoiceId] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [expandedTransaction, setExpandedTransaction] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize] = useState<number>(10);
    const { toast } = useToast();
    const { refreshDomains } = useDomain();

    // Format date with proper formatting
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('en-US', {
            weekday: 'short',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }).format(date);
    };

    // Format date without time (for next payment date)
    const formatDateNoTime = (dateString: string) => {
        return transactionService.formatDateNoTime(dateString);
    };

    // Calculate next payment date based on transaction date and plan type
    const getNextPaymentDate = (transaction: Transaction) => {
        if (!transaction.stripeSubscriptionId || transaction.status !== 'completed') {
            return null; // Only show for active subscriptions
        }

        const createdDate = new Date(transaction.createdAt);
        const nextPaymentDate = new Date(createdDate);

        // Set next payment date based on plan type
        if (transaction.planType === 'Monthly') {
            nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);
        } else if (transaction.planType === 'Yearly') {
            nextPaymentDate.setFullYear(nextPaymentDate.getFullYear() + 1);
        } else {
            return null; // Free plans don't have next payment date
        }

        return nextPaymentDate;
    };

    const fetchTransactions = async (syncWithStripe = false) => {
        try {
            setLoading(true);
            let data;

            if (syncWithStripe) {
                setSyncing(true);
                data = await transactionService.syncTransactionStatuses();
                setSyncing(false);
            } else {
                data = await transactionService.getTransactions();
            }

            setTransactions(data);
            setError(null);
            // Reset to first page when fetching new data
            setCurrentPage(1);
        } catch (err) {
            setError('Failed to load transaction history');
            console.error('Error fetching transactions:', err);
        } finally {
            setLoading(false);
        }
    };

    // Pagination handlers
    const totalPages = Math.ceil(transactions.length / pageSize);
    const paginatedTransactions = transactions.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
    );

    const goToNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const goToPreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    useEffect(() => {
        fetchTransactions(true); // Sync with Stripe on initial load

        // Remove the automatic refresh interval to prevent unnecessary API calls
        // const intervalId = setInterval(() => {
        //     fetchTransactions(true);
        // }, 30000);

        // // Clean up the interval when the component unmounts
        // return () => clearInterval(intervalId);
    }, []);

    const handleRefresh = () => {
        fetchTransactions(true);
    };

    const toggleTransactionDetails = (transactionId: string) => {
        if (expandedTransaction === transactionId) {
            setExpandedTransaction(null);
        } else {
            setExpandedTransaction(transactionId);
        }
    };

    const handleRefreshTransaction = async (sessionId: string, transactionId: string) => {
        try {
            setRefreshingId(transactionId);

            // Verify the payment status with Stripe
            const paymentInfo = await transactionService.verifyStripePaymentStatus(sessionId);

            // Refresh the transaction list
            const updatedTransactions = await transactionService.getTransactions();
            setTransactions(updatedTransactions);

            // Find the updated transaction
            const updatedTransaction = updatedTransactions.find(t => t._id === transactionId);

            let toastMessage = `Payment status: ${paymentInfo.status}`;

            // Check if payment ID was retrieved
            if (paymentInfo.paymentIntentId && (!updatedTransaction?.stripePaymentIntentId ||
                updatedTransaction?.stripePaymentIntentId !== paymentInfo.paymentIntentId)) {
                toastMessage += `. Payment ID retrieved.`;

                // Automatically expand the transaction details to show the new payment ID
                if (expandedTransaction !== transactionId) {
                    setExpandedTransaction(transactionId);
                }
            }

            // Check if charge ID was retrieved
            if (paymentInfo.chargeId && (!updatedTransaction?.stripeChargeId ||
                updatedTransaction?.stripeChargeId !== paymentInfo.chargeId)) {
                toastMessage += `. Charge ID retrieved.`;

                // Automatically expand the transaction details to show the new charge ID
                if (expandedTransaction !== transactionId) {
                    setExpandedTransaction(transactionId);
                }
            }

            // For prorated payments, update the status manually if needed
            if (updatedTransaction?.metadata?.isProrated && updatedTransaction.status === 'pending' &&
                paymentInfo.status === 'completed') {

                // Call the API to update plan after proration if needed
                try {
                    await transactionService.updatePlanAfterProration(sessionId);
                    toastMessage += `. Plan updated successfully.`;

                    // Refresh domain data to ensure plan information is up to date
                    await refreshDomains();

                    // Force refresh subscription data
                    if (updatedTransaction.domainId) {
                        try {
                            await paymentService.refreshSubscriptionData(updatedTransaction.domainId);
                            console.log('Subscription data refreshed after proration payment');
                        } catch (err) {
                            console.error('Error refreshing subscription data:', err);
                        }
                    }
                } catch (error) {
                    console.error('Error updating plan after proration:', error);
                }
            } else if (paymentInfo.status === 'completed' && updatedTransaction?.status === 'completed') {
                // Also refresh domains when a payment is completed
                await refreshDomains();

                // Force refresh subscription data
                if (updatedTransaction.domainId) {
                    try {
                        await paymentService.refreshSubscriptionData(updatedTransaction.domainId);
                        console.log('Subscription data refreshed after completed payment');
                    } catch (err) {
                        console.error('Error refreshing subscription data:', err);
                    }
                }
            }

            toast({
                title: "Payment Status Updated",
                description: toastMessage,
                variant: paymentInfo.status === 'completed' ? 'default' : 'destructive'
            });
        } catch (error) {
            console.error('Error refreshing transaction:', error);
            toast({
                title: "Error",
                description: "Failed to refresh payment status. Please try again.",
                variant: 'destructive'
            });
        } finally {
            setRefreshingId(null);
        }
    };

    const handleFetchInvoice = async (transactionId: string) => {
        // If the transaction already has an invoice URL, just open it
        const transaction = transactions.find(t => t._id === transactionId);
        if (transaction?.invoiceUrl) {
            transactionService.openInvoice(transaction.invoiceUrl);
            return;
        }

        try {
            setLoadingInvoiceId(transactionId);
            const invoiceUrl = await transactionService.getInvoiceUrl(transactionId);

            if (invoiceUrl) {
                // Update the transaction in the local state
                setTransactions(prevTransactions =>
                    prevTransactions.map(t =>
                        t._id === transactionId
                            ? { ...t, invoiceUrl }
                            : t
                    )
                );

                // Open the invoice URL
                transactionService.openInvoice(invoiceUrl);
            } else {
                toast({
                    title: "Invoice Not Available",
                    description: "Could not retrieve the invoice for this transaction.",
                    variant: "destructive"
                });
            }
        } catch (error) {
            console.error('Error fetching invoice:', error);
            toast({
                title: "Error",
                description: "Failed to retrieve invoice. Please try again.",
                variant: "destructive"
            });
        } finally {
            setLoadingInvoiceId(null);
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'completed':
                return <Badge className="bg-green-500 hover:bg-green-600 text-white font-medium px-3 py-1 rounded-full">Completed</Badge>;
            case 'pending':
                return <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white font-medium px-3 py-1 rounded-full">Pending</Badge>;
            case 'failed':
                return <Badge className="bg-red-500 hover:bg-red-600 text-white font-medium px-3 py-1 rounded-full">Failed</Badge>;
            case 'refunded':
                return <Badge className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-3 py-1 rounded-full">Refunded</Badge>;
            default:
                return <Badge className="px-3 py-1 rounded-full">{status}</Badge>;
        }
    };

    if (loading) {
        return (
            <Card className="w-full shadow-lg border-0">
                <CardContent className="flex justify-center py-12">
                    <Loader2 className="h-10 w-10 animate-spin text-orange-500" />
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Card className="w-full shadow-lg border-0">
                <CardContent className="p-8">
                    <div className="text-center text-red-500 font-medium">{error}</div>
                    <div className="flex justify-center mt-4">
                        <Button onClick={handleRefresh} variant="outline" className="flex items-center gap-2">
                            <RefreshCw className="h-4 w-4" /> Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="w-full shadow-lg border-0">
            <CardContent className="p-8">
                <div className="mb-8 flex justify-between items-center">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                            <CreditCard className="h-6 w-6 text-orange-500" />
                            Payment History
                        </h2>
                        <p className="text-gray-600 mt-2">
                            View your subscription payment history
                        </p>
                    </div>
                    <Button
                        onClick={handleRefresh}
                        variant="outline"
                        className="flex items-center gap-2"
                        disabled={syncing}
                    >
                        {syncing ? (
                            <>
                                <Loader2 className="h-4 w-4 animate-spin" /> Syncing...
                            </>
                        ) : (
                            <>
                                <RefreshCw className="h-4 w-4" /> Refresh Status
                            </>
                        )}
                    </Button>
                </div>
                {transactions.length === 0 ? (
                    <div className="text-center py-12 text-gray-500 bg-gray-50 rounded-lg">
                        <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                        <p className="font-medium">No transactions found</p>
                        <p className="text-sm mt-1">Your payment history will appear here</p>
                    </div>
                ) : (
                    <div className="rounded-lg overflow-hidden border border-gray-100">
                        <Table>
                            <TableHeader className="bg-gray-50">
                                <TableRow>
                                    <TableHead className="font-semibold text-gray-700">Date</TableHead>
                                    <TableHead className="font-semibold text-gray-700">Subscription ID</TableHead>
                                    <TableHead className="font-semibold text-gray-700">Domain</TableHead>
                                    <TableHead className="font-semibold text-gray-700">Plan</TableHead>
                                    <TableHead className="font-semibold text-gray-700">Subscription Type</TableHead>
                                    <TableHead className="font-semibold text-gray-700">Amount</TableHead>
                                    <TableHead className="font-semibold text-gray-700">Status</TableHead>
                                    <TableHead className="font-semibold text-gray-700">Invoice</TableHead>
                                    <TableHead className="font-semibold text-gray-700">Details</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {paginatedTransactions.map((transaction) => (
                                    <React.Fragment key={transaction._id}>
                                        <TableRow className="hover:bg-gray-50">
                                            <TableCell className="font-medium text-gray-800">
                                                <div className="flex items-center gap-2">
                                                    <Calendar className="h-4 w-4 text-gray-500" />
                                                    <span className="font-semibold text-gray-800">{transactionService.formatDateNoTime(transaction.createdAt)}</span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {transaction.stripeSubscriptionId ? (
                                                    <button
                                                        onClick={() => transactionService.openStripeSubscription(transaction.stripeSubscriptionId!)}
                                                        className="flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline"
                                                    >
                                                        <span className="text-xs font-mono truncate max-w-[100px]" title={transaction.stripeSubscriptionId}>
                                                            {transaction.stripeSubscriptionId}
                                                        </span>
                                                        <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                    </button>
                                                ) : (
                                                    <span className="text-xs text-gray-500">-</span>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">

                                                    <span className="text-gray-700">
                                                        {typeof transaction.domainId === 'object' && transaction.domainId?.url ? (
                                                            <span className="px-2 py-0.5 bg-blue-50 text-blue-700 text-sm rounded-md border border-blue-100 inline-flex items-center">
                                                                <Globe className="h-3 w-3 mr-1 text-blue-500" />
                                                                {transaction.domainId.url}
                                                            </span>
                                                        ) : (
                                                            <span className="font-semibold">Unknown Domain</span>
                                                        )}
                                                    </span>
                                                </div>
                                            </TableCell>
                                            <TableCell className="font-medium text-gray-700">
                                                <span className={`px-2 py-1 rounded text-xs font-semibold ${transaction.planType === 'Monthly' ? 'bg-blue-100 text-blue-700' :
                                                    transaction.planType === 'Yearly' ? 'bg-violet-100 text-violet-700' :
                                                        'bg-gray-100 text-gray-700'
                                                    }`}>
                                                    {transaction.planType}
                                                </span>
                                                {transaction.metadata?.isProrated && (
                                                    <span className="ml-2 px-2 py-0.5 bg-amber-50 text-amber-700 text-xs rounded border border-amber-200 inline-flex items-center">
                                                        <RefreshCw className="h-3 w-3 mr-1" /> Prorated
                                                    </span>
                                                )}
                                            </TableCell>
                                            <TableCell className="font-medium text-gray-700">
                                                <span className={`px-2 py-1 rounded text-xs font-semibold ${transaction.transactionType === 'New Subscription'
                                                    ? 'bg-green-50 text-green-700 border border-green-200'
                                                    : 'bg-blue-50 text-blue-700 border border-blue-200'
                                                    }`}>
                                                    {transaction.transactionType}
                                                </span>
                                            </TableCell>
                                            <TableCell className="font-semibold text-gray-800">
                                                <span className={`${transaction.amount >= 100 ? 'text-green-700' : 'text-green-600'} font-bold`}>
                                                    {transactionService.formatCurrency(transaction.amount, transaction.currency)}
                                                </span>
                                            </TableCell>
                                            <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                                            <TableCell>
                                                {transaction.status === 'completed' && (
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleFetchInvoice(transaction._id)}
                                                        disabled={loadingInvoiceId === transaction._id}
                                                        className="flex items-center gap-1 p-1 text-green-600 hover:text-green-800"
                                                    >
                                                        {loadingInvoiceId === transaction._id ? (
                                                            <Loader2 className="h-4 w-4 animate-spin" />
                                                        ) : (
                                                            <>
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                                    <polyline points="7 10 12 15 17 10"></polyline>
                                                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                                                </svg>
                                                                <span className="text-xs">Download</span>
                                                            </>
                                                        )}
                                                    </Button>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => toggleTransactionDetails(transaction._id)}
                                                    className="flex items-center gap-1 p-1"
                                                >
                                                    {expandedTransaction === transaction._id ? (
                                                        <ChevronUp className="h-4 w-4" />
                                                    ) : (
                                                        <ChevronDown className="h-4 w-4" />
                                                    )}
                                                    <span className="text-xs">Details</span>
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                        {expandedTransaction === transaction._id && (
                                            <TableRow>
                                                <TableCell colSpan={9} className="bg-gray-50 p-4">
                                                    <div className="flex justify-end mb-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => handleRefreshTransaction(transaction.stripeSessionId, transaction._id)}
                                                            disabled={refreshingId === transaction._id}
                                                            className="text-xs flex items-center gap-1"
                                                        >
                                                            {refreshingId === transaction._id ? (
                                                                <>
                                                                    <Loader2 className="h-3 w-3 animate-spin" /> Refreshing...
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <RefreshCw className="h-3 w-3" /> Refresh Payment Details
                                                                </>
                                                            )}
                                                        </Button>
                                                    </div>
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                                        <div>
                                                            <h4 className="font-semibold mb-4 border-b pb-2">Transaction Details</h4>
                                                            <div className="space-y-4">
                                                                <div className="flex items-center">
                                                                    <div className="w-7 mr-2">
                                                                        <Calendar className="h-5 w-5 text-gray-500" />
                                                                    </div>
                                                                    <div className="flex">
                                                                        <p className="font-medium text-gray-700 w-32">Created:</p>
                                                                        <p className="text-gray-800 font-semibold">{transactionService.formatDate(transaction.createdAt)}</p>
                                                                    </div>
                                                                </div>

                                                                <div className="flex items-center">
                                                                    <div className="w-7 mr-2">
                                                                        <Clock className="h-5 w-5 text-gray-500" />
                                                                    </div>
                                                                    <div className="flex">
                                                                        <p className="font-medium text-gray-700 w-32">Last Updated:</p>
                                                                        <p className="text-gray-800 font-semibold">{transactionService.formatDate(transaction.updatedAt)}</p>
                                                                    </div>
                                                                </div>

                                                                {getNextPaymentDate(transaction) && (
                                                                    <div className="flex items-center">
                                                                        <div className="w-7 mr-2">
                                                                            <Calendar className="h-5 w-5 text-blue-600" />
                                                                        </div>
                                                                        <div className="flex">
                                                                            <p className="font-medium text-gray-700 w-32">Next Payment:</p>
                                                                            <p className="text-blue-600 font-bold">{formatDateNoTime(getNextPaymentDate(transaction)!.toISOString())}</p>
                                                                        </div>
                                                                    </div>
                                                                )}

                                                                <div className="flex items-center">
                                                                    <div className="w-7 mr-2">
                                                                        <CreditCard className="h-5 w-5 text-gray-500" />
                                                                    </div>
                                                                    <div className="flex">
                                                                        <p className="font-medium text-gray-700 w-32">Payment Method:</p>
                                                                        <p className="text-gray-800 font-semibold capitalize">{transaction.paymentMethod}</p>
                                                                    </div>
                                                                </div>

                                                                {/* <div className="flex items-center">
                                                                    <div className="w-7 mr-2">
                                                                        {transaction.transactionType === 'New Subscription' ? (
                                                                            <svg className="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                                <path d="M12 5v14M5 12h14" />
                                                                            </svg>
                                                                        ) : (
                                                                            <RefreshCw className="h-5 w-5 text-blue-600" />
                                                                        )}
                                                                    </div>
                                                                    <div className="flex">
                                                                        <p className="font-medium text-gray-700 w-32">Subscription Type:</p>
                                                                        <p className={`font-bold ${transaction.transactionType === 'New Subscription'
                                                                            ? 'text-green-700'
                                                                            : 'text-blue-700'
                                                                            }`}>
                                                                            {transaction.transactionType}
                                                                        </p>
                                                                    </div>
                                                                </div> */}

                                                                {/* Display proration information if available */}
                                                                {transaction.metadata?.isProrated && (
                                                                    <>
                                                                        <div className="flex items-center">
                                                                            <div className="w-7 mr-2">
                                                                                <RefreshCw className="h-5 w-5 text-violet-600" />
                                                                            </div>
                                                                            <div className="flex">
                                                                                <p className="font-medium text-gray-700 w-32">Plan Change:</p>
                                                                                <p className="text-violet-700 font-bold">
                                                                                    {transaction.metadata?.fromPlan || 'Previous plan'} → {transaction.planType}
                                                                                </p>
                                                                            </div>
                                                                        </div>

                                                                        {transaction.metadata?.discount && (
                                                                            <div className="flex items-center">
                                                                                <div className="w-7 mr-2">
                                                                                    <svg className="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                                        <polyline points="20 6 9 17 4 12"></polyline>
                                                                                    </svg>
                                                                                </div>
                                                                                <div className="flex">
                                                                                    <p className="font-medium text-gray-700 w-32">Prorated Discount:</p>
                                                                                    <p className="text-green-600 font-bold">
                                                                                        {transactionService.formatCurrency(parseFloat(transaction.metadata.discount), transaction.currency)}
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        )}

                                                                        {transaction.metadata?.remainingDays && (
                                                                            <div className="flex items-center">
                                                                                <div className="w-7 mr-2">
                                                                                    <Clock className="h-5 w-5 text-blue-600" />
                                                                                </div>
                                                                                <div className="flex">
                                                                                    <p className="font-medium text-gray-700 w-32">Days Remaining:</p>
                                                                                    <p className="text-blue-700 font-bold">
                                                                                        {transaction.metadata.remainingDays} days credited
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                    </>
                                                                )}
                                                            </div>
                                                        </div>

                                                        <div>
                                                            <h4 className="font-semibold mb-3 border-b pb-1">Stripe Information</h4>
                                                            <div className="space-y-3">
                                                                <div>
                                                                    <p className="font-medium text-gray-700 mb-1">Session ID</p>
                                                                    <div className="bg-gray-100 p-2 rounded text-xs break-all font-mono text-gray-800 font-semibold">{transaction.stripeSessionId}</div>
                                                                </div>

                                                                {transaction.stripePaymentIntentId && (
                                                                    <div>
                                                                        <p className="font-medium text-gray-700 mb-1">Payment Intent ID</p>
                                                                        <button
                                                                            onClick={() => transactionService.openStripePaymentIntent(transaction.stripePaymentIntentId)}
                                                                            className="flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline w-full"
                                                                        >
                                                                            <div className="bg-blue-50 border border-blue-100 p-2 rounded text-xs break-all font-mono flex-grow text-left text-blue-700 font-bold">{transaction.stripePaymentIntentId}</div>
                                                                            <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                        </button>
                                                                    </div>
                                                                )}

                                                                {transaction.stripeChargeId && (
                                                                    <div>
                                                                        <p className="font-medium text-gray-700 mb-1">Charge ID</p>
                                                                        <button
                                                                            onClick={() => transactionService.openStripeCharge(transaction.stripeChargeId)}
                                                                            className="flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline w-full"
                                                                        >
                                                                            <div className="bg-blue-50 border border-blue-100 p-2 rounded text-xs break-all font-mono flex-grow text-left text-blue-700 font-bold">{transaction.stripeChargeId}</div>
                                                                            <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                        </button>
                                                                    </div>
                                                                )}

                                                                {transaction.stripeCustomerId && (
                                                                    <div>
                                                                        <p className="font-medium text-gray-700 mb-1">Customer ID</p>
                                                                        <button
                                                                            onClick={() => transactionService.openStripeCustomer(transaction.stripeCustomerId!)}
                                                                            className="flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:underline w-full"
                                                                        >
                                                                            <div className="bg-blue-50 border border-blue-100 p-2 rounded text-xs break-all font-mono flex-grow text-left text-blue-700 font-bold">{transaction.stripeCustomerId}</div>
                                                                            <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                        </button>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </React.Fragment>
                                ))}
                            </TableBody>
                        </Table>

                        {/* Pagination Controls */}
                        {transactions.length > pageSize && (
                            <div className="flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-100">
                                <div className="text-sm text-gray-600">
                                    Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, transactions.length)} of {transactions.length} transactions
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={goToPreviousPage}
                                        disabled={currentPage === 1}
                                        className="flex items-center gap-1"
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        <span>Previous</span>
                                    </Button>
                                    <div className="text-sm font-medium text-gray-700">
                                        Page {currentPage} of {totalPages}
                                    </div>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={goToNextPage}
                                        disabled={currentPage === totalPages}
                                        className="flex items-center gap-1"
                                    >
                                        <span>Next</span>
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default TransactionHistory; 