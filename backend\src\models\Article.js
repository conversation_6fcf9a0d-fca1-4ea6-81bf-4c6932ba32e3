const mongoose = require('mongoose');
const { slugify } = require('transliteration');

// Enhanced slugify function that handles international characters
function createSlug(text) {
    if (!text) return '';

    // First transliterate non-Latin characters to Latin equivalents
    let latinText = slugify(text);

    // Then apply standard slug rules
    return latinText
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '') // Remove non-word chars (keep spaces and dashes)
        .replace(/\s+/g, '-')     // Replace spaces with dashes
        .replace(/-+/g, '-')      // Replace multiple dashes with single dash
        .replace(/^-+|-+$/g, ''); // Trim dashes from start and end
}

const articleSchema = new mongoose.Schema({
    title: {
        type: String,
        required: [true, 'Title is required'],
        trim: true
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    description: {
        type: String,
        trim: true
    },
    content: {
        type: String,
        required: [true, 'Content is required']
    },
    tag: {
        type: String,
        default: 'General'
    },
    status: {
        type: String,
        enum: ['draft', 'published', 'scheduled', 'generated'],
        default: 'draft'
    },
    author: {
        type: String,
        default: 'AI Writer',
        trim: true
    },
    date: {
        type: Date,
        default: Date.now
    },
    urlSlug: {
        type: String,
        trim: true,
        unique: true,
        sparse: true
    },
    metaDescription: {
        type: String,
        trim: true
    },
    excerpt: {
        type: String,
        trim: true
    },
    keywords: [{
        type: String,
        trim: true
    }],
    sourceUrl: {
        type: String,
        trim: true
    },
    domainId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Domain',
        required: false // Make it optional for backward compatibility
    },
    image: {
        url: {
            type: String,
            default: '/lovable-uploads/default-article.png'
        },
        thumb: {
            type: String,
            default: '/lovable-uploads/default-article-thumb.png'
        },
        description: String,
        credit: {
            name: String,
            username: String,
            link: String
        }
    },
    video: {
        url: { type: String },
        width: { type: Number },
        height: { type: Number },
        duration: { type: Number },
        description: String,
        thumbnail: { type: String },
        credit: {
            name: String,
            url: String
        }
    },
    metadata: {
        originalTitle: String,
        originalDescription: String,
        generatedAt: {
            type: Date,
            default: Date.now
        }
    },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Create URL-friendly slug from title
articleSchema.pre('save', function (next) {
    if (this.title && (!this.urlSlug || this.isModified('title'))) {
        // Use the enhanced slugify function that handles international characters
        this.urlSlug = createSlug(this.title);

        // If slugification results in an empty string (e.g., all characters were removed),
        // generate a fallback slug with the current timestamp
        if (!this.urlSlug) {
            this.urlSlug = 'article-' + Date.now();
        }
    }

    // Set description from content if not provided
    if (!this.description && this.content) {
        this.description = this.content.substring(0, 200) + '...';
    }

    // Set excerpt from content if not provided
    if (!this.excerpt && this.content) {
        this.excerpt = this.content.substring(0, 150) + '...';
    }

    next();
});

const Article = mongoose.model('Article', articleSchema);

module.exports = Article; 