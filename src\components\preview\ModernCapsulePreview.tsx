import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { Article } from '../../types/Article';
import { useAuth } from '@/contexts/AuthContext';
import { articleService } from '@/services/articleService';

interface ModernCapsulePreviewProps {
    article: Article;
    fontFamily: string;
    fontColor: string;
    brandColor: string;
}

// Helper function to remove video elements from HTML content
const removeMediaContent = (html: string): string => {
    const div = document.createElement('div');
    div.innerHTML = html;

    // Remove video elements
    const videos = div.getElementsByTagName('video');
    while (videos.length > 0) {
        videos[0].parentNode?.removeChild(videos[0]);
    }

    // Remove iframe elements (embedded videos)
    const iframes = div.getElementsByTagName('iframe');
    while (iframes.length > 0) {
        iframes[0].parentNode?.removeChild(iframes[0]);
    }

    // Remove figure elements containing videos
    const figures = div.getElementsByTagName('figure');
    Array.from(figures).forEach(figure => {
        if (figure.querySelector('video, iframe')) {
            figure.parentNode?.removeChild(figure);
        }
    });

    // Remove video source elements
    const sources = div.getElementsByTagName('source');
    while (sources.length > 0) {
        sources[0].parentNode?.removeChild(sources[0]);
    }

    // Remove any elements with video-related classes
    const videoElements = div.querySelectorAll('.video-container, .video-wrapper, [class*="video"]');
    videoElements.forEach(el => el.parentNode?.removeChild(el));

    return div.innerHTML;
};

const ModernCapsulePreview: React.FC<ModernCapsulePreviewProps> = ({
    article,
    fontFamily,
    fontColor,
    brandColor
}) => {
    const { user } = useAuth();
    const authorData = user || article.userData;
    const [relatedArticles, setRelatedArticles] = useState<Article[]>([]);
    const [authorArticles, setAuthorArticles] = useState<Article[]>([]);

    useEffect(() => {
        const fetchArticles = async () => {
            try {
                // Get all articles from the domain
                const articles = await articleService.getArticles(article.domainId);

                // First, get articles by the same author
                const authorFiltered = articles
                    .filter(a => a.id !== article.id) // Exclude current article
                    .filter(a => {
                        // Match by userId if available
                        if (article.userId && a.userId) {
                            return a.userId === article.userId;
                        }
                        // Fallback to matching by author name if userId not available
                        return a.author === article.author;
                    })
                    .sort((a, b) => {
                        const dateA = new Date(a.createdAt || a.date).getTime();
                        const dateB = new Date(b.createdAt || b.date).getTime();
                        return dateB - dateA; // Sort by date descending
                    })
                    .slice(0, 3); // Get top 3 most recent articles
                setAuthorArticles(authorFiltered);

                // Then, get related articles excluding the author's articles
                const authorArticleIds = new Set(authorFiltered.map(a => a.id));
                const filtered = articles
                    .filter(a => a.id !== article.id) // Exclude current article
                    .filter(a => !authorArticleIds.has(a.id)) // Exclude author's articles
                    .filter(a => {
                        // Check for keyword overlap
                        const currentKeywords = article.keywords || [];
                        const articleKeywords = a.keywords || [];
                        return articleKeywords.some(keyword => currentKeywords.includes(keyword));
                    })
                    .sort((a, b) => {
                        // Prioritize articles with more matching keywords
                        const aKeywords = a.keywords || [];
                        const bKeywords = b.keywords || [];
                        const currentKeywords = article.keywords || [];
                        const aMatches = aKeywords.filter(k => currentKeywords.includes(k)).length;
                        const bMatches = bKeywords.filter(k => currentKeywords.includes(k)).length;
                        return bMatches - aMatches;
                    })
                    .slice(0, 3); // Get top 3 related articles
                setRelatedArticles(filtered);
            } catch (error) {
                console.error('Error fetching articles:', error);
            }
        };

        if (article.domainId) {
            fetchArticles();
        }
    }, [article.domainId, article.id, article.userId, article.author, article.keywords]);

    // Only show the Related Articles section if we have articles that aren't by the same author
    const showRelatedArticles = relatedArticles.length > 0 &&
        !relatedArticles.every(a => a.userId === article.userId || a.author === article.author);

    const renderArticleCard = (articleItem: Article) => (
        <a
            key={articleItem.id}
            href={`/articles/${articleItem.urlSlug}`}
            className="group block"
        >
            {articleItem.image?.url && (
                <div className="aspect-video w-full mb-3 overflow-hidden rounded-lg">
                    <img
                        src={articleItem.image.url}
                        alt={articleItem.title}
                        className="w-full h-full object-cover transition-transform group-hover:scale-105"
                    />
                </div>
            )}
            <h4
                className="font-medium line-clamp-2 group-hover:text-orange-500 transition-colors"
                style={{ color: '#1a1a1a' }}
            >
                {articleItem.title}
            </h4>
            <p className="text-sm mt-1 line-clamp-2" style={{ color: fontColor }}>
                {articleItem.description}
            </p>
            <div className="text-sm mt-2" style={{ color: fontColor }}>
                {format(new Date(articleItem.createdAt || articleItem.date), 'MMM d, yyyy')}
            </div>
        </a>
    );

    return (
        <div className="max-w-5xl mx-auto" style={{ fontFamily }}>
            {/* Tag */}
            {article.tag && (
                <div className="mb-6">
                    <span
                        className="px-3 py-1 rounded-full text-sm inline-block"
                        style={{
                            backgroundColor: `${brandColor}15`,
                            color: brandColor,
                            border: `1px solid ${brandColor}`
                        }}
                    >
                        {article.tag}
                    </span>
                </div>
            )}

            {/* Title */}
            <h1 className="text-4xl font-bold mb-6" style={{ color: '#1a1a1a' }}>
                {article.title}
            </h1>

            {/* Subtitle/Description */}
            <p className="text-xl mb-8" style={{ color: fontColor }}>
                {article.description}
            </p>

            {/* Meta Info */}
            <div className="flex items-center justify-between border-t border-b py-4 mb-8" style={{ borderColor: 'rgba(0,0,0,0.1)' }}>
                <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden">
                        {authorData?.avatar ? (
                            <img
                                src={authorData.avatar}
                                alt={`${authorData.firstName} ${authorData.lastName}`}
                                className="w-full h-full object-cover"
                            />
                        ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-500 font-medium">
                                {authorData ? authorData.firstName.charAt(0) : 'A'}
                            </div>
                        )}
                    </div>
                    <div>
                        <div className="font-medium" style={{ color: '#1a1a1a' }}>
                            {authorData ? `${authorData.firstName} ${authorData.lastName}` : 'Anonymous'}
                        </div>
                        <div className="text-sm" style={{ color: fontColor }}>
                            {authorData ? 'Author' : 'Technical Writer'}
                        </div>
                    </div>
                </div>
                <div className="flex items-center space-x-2" style={{ color: fontColor }}>
                    <span>{format(new Date(article.createdAt || article.date), 'MMM d, yyyy')}</span>
                </div>
            </div>

            {/* Article Content */}
            <div
                className="prose max-w-none mb-8"
                style={{ color: fontColor }}
                dangerouslySetInnerHTML={{ __html: removeMediaContent(article.content || '') }}
            />

            {/* Keywords Section */}
            {article.keywords && article.keywords.length > 0 && (
                <div className="border-t pt-6 mt-8">
                    <h3 className="text-lg font-semibold mb-4" style={{ color: '#1a1a1a' }}>Keywords</h3>
                    <div className="flex flex-wrap gap-2">
                        {article.keywords.map((keyword, index) => (
                            <span
                                key={index}
                                className="px-3 py-1 rounded-full text-sm"
                                style={{
                                    backgroundColor: `${brandColor}15`, // 15% opacity
                                    color: brandColor,
                                    border: `1px solid ${brandColor}`
                                }}
                            >
                                {keyword}
                            </span>
                        ))}
                    </div>
                </div>
            )}

            {/* Related Articles Section */}
            {showRelatedArticles && (
                <div className="border-t pt-6 mt-8">
                    <h3 className="text-lg font-semibold mb-4" style={{ color: '#1a1a1a' }}>Related Articles</h3>
                    <div className="grid gap-6 md:grid-cols-3">
                        {relatedArticles.map(renderArticleCard)}
                    </div>
                </div>
            )}

            {/* More from Author Section */}
            {authorArticles.length > 0 && (
                <div className="border-t pt-6 mt-8">
                    <h3 className="text-lg font-semibold mb-4" style={{ color: '#1a1a1a' }}>
                        More from {authorData ? `${authorData.firstName} ${authorData.lastName}` : (typeof article.author === 'string' ? article.author : 'this Author')}
                    </h3>
                    <div className="grid gap-6 md:grid-cols-3">
                        {authorArticles.map(renderArticleCard)}
                    </div>
                </div>
            )}
        </div>
    );
};

export default ModernCapsulePreview; 