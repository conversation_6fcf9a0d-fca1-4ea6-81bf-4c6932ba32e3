import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowRight, Globe } from 'lucide-react';
import { WebsiteInfo } from '@/lib/services/websiteService';
import { GeneratedArticle } from '@/lib/services/articleService';
import { toast } from '@/components/ui/use-toast';
import { useDomain } from '@/contexts/DomainContext';
import { useNavigate } from 'react-router-dom';
import apiClient from '@/lib/api-client';
import { Domain } from '@/services/domainService';

interface DomainEntryProps {
  onNext?: () => void;
  onData?: (data: { websiteInfo: WebsiteInfo; article: GeneratedArticle; domain: Domain }) => void;
  onSkip?: () => void;
}

export const DomainEntry = ({ onNext, onData, onSkip }: DomainEntryProps) => {
  const [domain, setDomain] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { addDomain, setCurrentDomain } = useDomain();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!domain.trim()) return;

    setIsLoading(true);
    try {
      // Ensure the URL has a protocol
      const url = domain.startsWith('http') ? domain : `https://${domain}`;

      // Call backend API to process domain and generate article
      console.log('Making request to process domain:', url);
      const response = await apiClient.post('/process-domain', {
        url
      }, {
        timeout: 120000 // 2 minute timeout
      });

      if (response.data.error) {
        throw new Error(response.data.error);
      }

      const { websiteInfo, domain: createdDomain, isExistingDomain, message } = response.data;

      // Validate response data
      if (!websiteInfo || !createdDomain) {
        throw new Error('Invalid response from server');
      }

      console.log('Website analysis complete:', websiteInfo.title);
      console.log(isExistingDomain ? 'Using existing domain:' : 'Created new domain:', createdDomain.name);

      // Set the domain in context
      setCurrentDomain(createdDomain);

      // Only add to domains list if it's a new domain
      if (!isExistingDomain) {
        addDomain(createdDomain);
        console.log('New domain added to context:', createdDomain.name);
      } else {
        console.log('Article added to existing domain:', createdDomain.name);
      }

      // Remove topics storage since we're not generating articles anymore
      localStorage.removeItem('generated_topics');

      // Show success message
      toast({
        title: isExistingDomain ? "Domain Found" : "Domain Created",
        description: message || (isExistingDomain
          ? `Found existing domain "${createdDomain.name}"`
          : `New domain "${createdDomain.name}" created successfully`),
        duration: 5000
      });

      // Call onData callback if provided
      if (onData) {
        // Create a placeholder article to satisfy the interface
        const placeholderArticle = {
          title: '',
          content: '',
          description: '',
          keywords: [],
          status: 'draft',
          urlSlug: '',
          author: '',
          date: new Date().toISOString()
        };
        onData({ websiteInfo, article: placeholderArticle, domain: createdDomain });
      }

      // Always navigate directly to settings page
      navigate('/dashboard/settings', {
        state: { fromDomainCreation: true, domainId: createdDomain._id }
      });
    } catch (error) {
      console.error('Error processing domain:', error);
      let errorMessage = 'Failed to process domain. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage = 'The request timed out. Please try again.';
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
        duration: 5000
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    } else {
      navigate('/dashboard/articles');
    }
  };

  return (
    <>
      <Button
        variant="outline"
        onClick={handleSkip}
        className="fixed top-4 right-4 text-orange-500 hover:text-orange-600 z-50"
      >
        Go to Dashboard →
      </Button>

      <div className="w-full max-w-2xl mx-auto p-8 text-center">
        <div className="mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">📝</span>
            </div>
            <span className="ml-3 text-2xl font-bold text-gray-800">BLOGBUSTER</span>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4 leading-tight">
            Enter your <span className="bg-orange-200 px-2 py-1 rounded">domain URL</span>,<br />
            and we do the rest
          </h1>
        </div>

        <form onSubmit={handleSubmit} className="mb-8">
          <div className="relative max-w-md mx-auto">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
              <Globe className="w-5 h-5 text-gray-400" />
            </div>
            <Input
              type="url"
              placeholder="https://yourdomain.com/"
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
              className="pl-12 pr-16 py-4 text-lg border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-0 bg-white"
              disabled={isLoading}
            />
            <Button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <ArrowRight className="w-5 h-5" />
              )}
            </Button>
          </div>
        </form>

        {isLoading && (
          <div className="text-gray-600">
            <p className="mb-2">🔍 Analyzing your website...</p>
            <p className="text-sm">This may take 1-2 minutes while we:</p>
            <ul className="text-sm mt-2 space-y-1">
              <li>• Fetch and analyze website content</li>
              <li>• Extract brand information</li>
              <li>• Set up your domain settings</li>
            </ul>
          </div>
        )}

        {!isLoading && (
          <div className="text-gray-600">
            <p className="mb-2">🚀 Transform your blog into a SEO traffic magnet, all on autopilot</p>
            <p className="text-sm">Automate, Publish, Grow – All in One</p>
          </div>
        )}
      </div>
    </>
  );
};
