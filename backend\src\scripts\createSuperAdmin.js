require('dotenv').config();
const mongoose = require('mongoose');
const Admin = require('../models/Admin');
const bcrypt = require('bcrypt');

const createSuperAdmin = async () => {
    try {
        await mongoose.connect("mongodb://127.0.0.1:27017/seo-autoblog");
        console.log('Connected to MongoDB');

        // Check if super admin already exists
        const existingAdmin = await Admin.findOne({ role: 'admin' });
        if (existingAdmin) {
            console.log('Super admin already exists');
            process.exit(0);
        }

        // Create super admin
        const superAdmin = new Admin({
            username: 'admin',
            email: '<EMAIL>',
            password: 'admin123!@#',  // This will be hashed by the pre-save middleware
            fullName: 'Admin',
            role: 'admin',
            permissions: [
                'manage_users',
                'manage_articles',
                'manage_domains',
                'manage_settings',
                'manage_admins'
            ],
            isActive: true
        });

        await superAdmin.save();
        console.log('Super admin created successfully');
        console.log('Email: <EMAIL>');
        console.log('Password: admin123!@#');
        console.log('Please change these credentials after first login');
    } catch (error) {
        console.error('Error creating super admin:', error);
    } finally {
        await mongoose.disconnect();
        process.exit(0);
    }
};

createSuperAdmin(); 