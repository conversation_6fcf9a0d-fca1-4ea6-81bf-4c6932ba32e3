import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Article } from '@/types/Article';
import { articleService } from '@/services/articleService';
import { useDomain } from '@/contexts/DomainContext';
import { userService, User } from '@/services/userService';
import DailyDelightPreview from '../preview/DailyDelightPreview';
import ModernCapsulePreview from '../preview/ModernCapsulePreview';
import { fontService } from '@/services/fontService';

export const ArticleSingleView = () => {
    const { slug } = useParams<{ slug: string }>();
    const [article, setArticle] = useState<Article | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const { currentDomain } = useDomain();
    const [userData, setUserData] = useState<User | null>(null);

    const brandColor = currentDomain?.designSettings?.colors?.brand || '#3c484c';
    const fontColor = currentDomain?.designSettings?.colors?.accent || '#cec4c0';
    const fontFamily = currentDomain?.designSettings?.font || 'Roboto';
    const theme = currentDomain?.designSettings?.articleTheme || 'Daily Delight';

    useEffect(() => {
        if (fontFamily) {
            fontService.loadFont(fontFamily).catch(error => {
                console.error('Error loading font:', error);
            });
        }
    }, [fontFamily]);

    useEffect(() => {
        const fetchArticle = async () => {
            if (!slug) {
                setError('No article slug provided');
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError(null);
                const fetchedArticle = await articleService.getArticleBySlug(slug);
                setArticle(fetchedArticle);
            } catch (err) {
                console.error('Error fetching article:', err);
                setError('Article not found or no longer available');
            } finally {
                setLoading(false);
            }
        };

        fetchArticle();
    }, [slug]);

    useEffect(() => {
        const fetchUserData = async () => {
            if (article?.userId) {
                try {
                    const data = await userService.getUserById(article.userId);
                    setUserData(data);
                } catch (error) {
                    console.error('Error fetching user data:', error);
                }
            }
        };

        fetchUserData();
    }, [article?.userId]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mb-4"></div>
                    <p className="text-gray-600">Loading article...</p>
                </div>
            </div>
        );
    }

    if (error || !article) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
                    <p className="text-gray-600">{error || 'Article not found'}</p>
                    <a href="/dashboard/articles" className="text-blue-500 hover:underline mt-4 inline-block">
                        Back to Articles
                    </a>
                </div>
            </div>
        );
    }

    const commonProps = {
        article,
        userData,
        fontFamily,
        fontColor,
        brandColor
    };

    return (
        <div className="min-h-screen flex flex-col">
            {/* Header */}
            <header className="py-4" style={{ backgroundColor: 'rgba(255, 255, 255, 0.25)' }}>
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between">
                        {/* Logo Section */}
                        <div className="h-16">
                            {currentDomain?.designSettings?.logo ? (
                                <img
                                    src={currentDomain.designSettings.logo}
                                    alt={`${currentDomain.name} logo`}
                                    className="h-full object-contain"
                                />
                            ) : (
                                <div className="h-full flex items-center">
                                    <span className="text-xl font-bold" style={{ fontFamily }}>
                                        {currentDomain?.name || 'Blog'}
                                    </span>
                                </div>
                            )}
                        </div>

                        {/* Navigation Links */}
                        <div className="flex items-center space-x-6">
                            {/* Show header links from navigation settings */}
                            {currentDomain?.navigationSettings?.headerLinks &&
                                currentDomain.navigationSettings.headerLinks.length > 0 ? (
                                currentDomain.navigationSettings.headerLinks
                                    .filter(link => link.text && link.url && link.text.trim() !== '' && link.url.trim() !== '')
                                    .map((link) => {
                                        // Validate URL format
                                        let url = link.url;
                                        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
                                            url = `https://${url}`;
                                        }

                                        return (
                                            <a
                                                key={link.id}
                                                href={url}
                                                className="text-gray-600 hover:text-gray-900 transition-colors"
                                                style={{ fontFamily }}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                {link.text}
                                            </a>
                                        );
                                    })
                            ) : (
                                // Fallback to default links if no custom links are set
                                <>
                                    <a
                                        href="#latest"
                                        className="text-gray-600 hover:text-gray-900 transition-colors"
                                        style={{ fontFamily }}
                                    >
                                        Latest
                                    </a>
                                    <a
                                        href="#categories"
                                        className="text-gray-600 hover:text-gray-900 transition-colors"
                                        style={{ fontFamily }}
                                    >
                                        Categories
                                    </a>
                                    <a
                                        href="#about"
                                        className="text-gray-600 hover:text-gray-900 transition-colors"
                                        style={{ fontFamily }}
                                    >
                                        About
                                    </a>
                                </>
                            )}
                        </div>

                        {/* CTA Button */}
                        {currentDomain?.navigationSettings?.ctaButtonDisabled === false &&
                            currentDomain.navigationSettings.ctaButtonText &&
                            currentDomain.navigationSettings.ctaButtonUrl && (
                                <a
                                    href={currentDomain.navigationSettings.ctaButtonUrl.startsWith('http')
                                        ? currentDomain.navigationSettings.ctaButtonUrl
                                        : `https://${currentDomain.navigationSettings.ctaButtonUrl}`}
                                    className="px-4 py-2 rounded-md text-white transition-colors"
                                    style={{
                                        backgroundColor: brandColor,
                                        fontFamily
                                    }}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    {currentDomain.navigationSettings.ctaButtonText}
                                </a>
                            )}
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="flex-grow bg-gray-50/30">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="bg-white rounded-lg shadow-sm p-8">
                        {theme === 'Modern Capsule' ? (
                            <ModernCapsulePreview {...commonProps} />
                        ) : (
                            <DailyDelightPreview {...commonProps} />
                        )}
                    </div>
                </div>
            </main>

            {/* Footer */}
            <footer className="bg-gray-900 py-4">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Footer Links */}
                    {currentDomain?.navigationSettings?.footerLinks &&
                        currentDomain.navigationSettings.footerLinks.length > 0 && (
                            <div className="flex justify-center space-x-8 mb-4">
                                {currentDomain.navigationSettings.footerLinks
                                    .filter(link => link.text && link.url && link.text.trim() !== '' && link.url.trim() !== '')
                                    .map((link) => {
                                        // Validate URL format
                                        let url = link.url;
                                        if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
                                            url = `https://${url}`;
                                        }

                                        return (
                                            <a
                                                key={link.id}
                                                href={url}
                                                className="text-gray-400 hover:text-white transition-colors"
                                                style={{ fontFamily }}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                {link.text}
                                            </a>
                                        );
                                    })
                                }
                            </div>
                        )}
                    <div className="text-center text-gray-400">
                        <p>© {new Date().getFullYear()} {currentDomain?.name || 'Blog'}. All rights reserved.</p>
                    </div>
                </div>
            </footer>
        </div>
    );
}; 