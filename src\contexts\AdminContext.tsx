import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate, useLocation } from 'react-router-dom';

interface Admin {
    _id: string;
    username: string;
    email: string;
    fullName: string;
    role: string;
    permissions: string[];
    avatar: string;
    isActive: boolean;
    lastLogin: Date;
}

interface AdminContextType {
    admin: Admin | null;
    token: string | null;
    loading: boolean;
    error: string | null;
    login: (email: string, password: string) => Promise<void>;
    logout: () => Promise<void>;
    clearError: () => void;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

export const AdminProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [admin, setAdmin] = useState<Admin | null>(null);
    const [token, setToken] = useState<string | null>(localStorage.getItem('adminToken'));
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const navigate = useNavigate();
    const location = useLocation();

    const api = axios.create({
        baseURL: 'http://localhost:5000/api/admin',
        headers: {
            'Content-Type': 'application/json'
        }
    });

    // Update axios headers when token changes
    useEffect(() => {
        if (token) {
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            localStorage.setItem('adminToken', token);
        } else {
            delete api.defaults.headers.common['Authorization'];
            localStorage.removeItem('adminToken');
        }
    }, [token]);

    // Verify token on mount and when location changes
    useEffect(() => {
        const verifyToken = async () => {
            const storedToken = localStorage.getItem('adminToken');

            if (!storedToken) {
                setLoading(false);
                if (location.pathname.startsWith('/admin') && location.pathname !== '/admin/login') {
                    navigate('/admin/login');
                }
                return;
            }

            try {
                setToken(storedToken);
                const response = await api.get('/verify-token', {
                    headers: { Authorization: `Bearer ${storedToken}` }
                });
                setAdmin(response.data);

                // If we're on the login page and have a valid token, redirect to dashboard
                if (location.pathname === '/admin/login') {
                    navigate('/admin/dashboard');
                }
            } catch (err) {
                console.error('Token verification failed:', err);
                setToken(null);
                setAdmin(null);
                localStorage.removeItem('adminToken');
                if (location.pathname.startsWith('/admin') && location.pathname !== '/admin/login') {
                    navigate('/admin/login');
                }
            } finally {
                setLoading(false);
            }
        };

        verifyToken();
    }, [location.pathname]);

    const login = async (email: string, password: string) => {
        try {
            setLoading(true);
            setError(null);

            // Use axios directly instead of the api instance to avoid sending the token
            const response = await axios.post('http://localhost:5000/api/admin/login',
                { email, password },
                { headers: { 'Content-Type': 'application/json' } }
            );

            const { admin, token } = response.data;
            setAdmin(admin);
            setToken(token);
            localStorage.setItem('adminToken', token);
            navigate('/admin/dashboard');
        } catch (err: any) {
            console.error('Login error:', err);
            setError(err.response?.data?.error || 'Login failed');
            throw err;
        } finally {
            setLoading(false);
        }
    };

    const logout = async () => {
        try {
            setLoading(true);
            if (token) {
                await api.post('/logout', null, {
                    headers: { Authorization: `Bearer ${token}` }
                });
            }
        } catch (err: any) {
            console.error('Logout error:', err);
        } finally {
            setToken(null);
            setAdmin(null);
            localStorage.removeItem('adminToken');
            setLoading(false);
            navigate('/admin/login');
        }
    };

    const clearError = () => setError(null);

    // Add axios interceptor to handle token expiration
    useEffect(() => {
        const interceptor = api.interceptors.response.use(
            (response) => response,
            async (error) => {
                if (error.response?.status === 401 && token) {
                    // Token has expired or is invalid
                    setToken(null);
                    setAdmin(null);
                    localStorage.removeItem('adminToken');
                    navigate('/admin/login');
                }
                return Promise.reject(error);
            }
        );

        return () => {
            api.interceptors.response.eject(interceptor);
        };
    }, [token, navigate]);

    return (
        <AdminContext.Provider
            value={{
                admin,
                token,
                loading,
                error,
                login,
                logout,
                clearError
            }}
        >
            {children}
        </AdminContext.Provider>
    );
};

export const useAdmin = () => {
    const context = useContext(AdminContext);
    if (context === undefined) {
        throw new Error('useAdmin must be used within an AdminProvider');
    }
    return context;
};

export default AdminContext; 