require('dotenv').config({ path: '../../.env' });
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Admin = require('../models/Admin');
const connectDB = require('../config/db');

// Admin email to reset password for
const adminEmail = '<EMAIL>'; // Change this to the email you're trying to login with
const newPassword = 'Admin@123'; // Change this to your desired new password

async function resetAdminPassword() {
    try {
        // Connect to the database
        await connectDB();
        console.log('Connected to MongoDB');

        // Find the admin by email
        const admin = await Admin.findOne({ email: adminEmail });
        if (!admin) {
            console.error(`Admin with email ${adminEmail} not found`);
            process.exit(1);
        }

        // Hash the new password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(newPassword, salt);

        // Update the admin's password and clear tokens
        admin.password = hashedPassword;
        admin.tokens = [];
        await admin.save();

        console.log(`Password for admin ${adminEmail} has been reset successfully`);
        console.log('All authentication tokens have been cleared');
        console.log(`You can now login with the new password: ${newPassword}`);

        process.exit(0);
    } catch (error) {
        console.error('Error resetting admin password:', error);
        process.exit(1);
    }
}

resetAdminPassword(); 