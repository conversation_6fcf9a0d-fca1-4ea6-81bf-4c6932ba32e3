const User = require('../models/User');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const Domain = require('../models/Domain');
const Article = require('../models/Article');
const nodemailer = require('nodemailer');

// Get user by ID (public data only)
const getUserById = async (req, res) => {
    try {
        const user = await User.findById(req.params.id).select('firstName lastName avatar isActive');

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Create combined name
        const fullName = user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;

        res.status(200).json({
            success: true,
            data: {
                id: user._id,
                name: fullName,
                firstName: user.firstName,
                lastName: user.lastName,
                avatar: user.avatar,
                isActive: user.isActive
            }
        });
    } catch (error) {
        console.error('Error fetching user:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching user data'
        });
    }
};

// Update user profile
const updateProfile = async (req, res) => {
    try {
        const { name } = req.body;

        // Validate input
        if (!name || !name.trim()) {
            return res.status(400).json({
                success: false,
                message: 'Name is required'
            });
        }

        // Split name into firstName and lastName
        let firstName = name.trim();
        let lastName = '';

        // If name contains a space, split it into firstName and lastName
        if (name.includes(' ')) {
            const nameParts = name.trim().split(' ');
            firstName = nameParts[0];
            lastName = nameParts.slice(1).join(' ');
        }

        // Get user from middleware
        const userId = req.user.id;

        // Update user
        const updatedUser = await User.findByIdAndUpdate(
            userId,
            {
                firstName,
                lastName
            },
            { new: true, select: '-password' }
        );

        if (!updatedUser) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Add combined name field for response
        const userObj = updatedUser.toObject();
        userObj.name = updatedUser.lastName ? `${updatedUser.firstName} ${updatedUser.lastName}` : updatedUser.firstName;

        res.status(200).json({
            success: true,
            data: userObj,
            message: 'Profile updated successfully'
        });

    } catch (error) {
        console.error('Error updating profile:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating profile'
        });
    }
};

// Change password
const changePassword = async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;

        // Validate input
        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                success: false,
                message: 'Current password and new password are required'
            });
        }

        if (newPassword.length < 8) {
            return res.status(400).json({
                success: false,
                message: 'New password must be at least 8 characters long'
            });
        }

        // Get user with password field
        const user = await User.findById(req.user.id).select('+password');
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if current password matches
        const isMatch = await user.comparePassword(currentPassword);
        if (!isMatch) {
            return res.status(401).json({
                success: false,
                message: 'Current password is incorrect'
            });
        }

        // Set new password (will be hashed by pre-save hook)
        user.password = newPassword;
        await user.save();

        // Log the success
        console.log('Password changed successfully for user:', user._id);

        res.status(200).json({
            success: true,
            message: 'Password changed successfully'
        });

    } catch (error) {
        console.error('Error changing password:', error);
        res.status(500).json({
            success: false,
            message: 'Error changing password'
        });
    }
};

// Delete account and all associated data
const deleteAccount = async (req, res) => {
    try {
        const userId = req.user.id;

        // Find all domains owned by the user
        const userDomains = await Domain.find({ userId: userId });
        const domainIds = userDomains.map(domain => domain._id);

        let deletedArticlesCount = 0;
        let deletedDomainsCount = 0;

        // First delete all articles by this user (both domain-specific and user-specific)
        const articlesResult = await Article.deleteMany({
            $or: [
                { userId: userId },
                { domainId: { $in: domainIds } }
            ]
        });
        deletedArticlesCount = articlesResult.deletedCount;
        console.log(`Deleted ${deletedArticlesCount} articles`);

        // Delete all domains owned by the user
        const domainsResult = await Domain.deleteMany({ userId: userId });
        deletedDomainsCount = domainsResult.deletedCount;
        console.log(`Deleted ${deletedDomainsCount} domains`);

        // Delete the user account
        const deletedUser = await User.findByIdAndDelete(userId);
        if (!deletedUser) {
            throw new Error('User not found');
        }

        res.status(200).json({
            success: true,
            message: 'Account and all associated data deleted successfully',
            details: {
                domainsDeleted: deletedDomainsCount,
                articlesDeleted: deletedArticlesCount
            }
        });

    } catch (error) {
        console.error('Error deleting account:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting account: ' + error.message
        });
    }
};

// Forgot Password
const forgotPassword = async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email is required'
            });
        }

        // Find user by email
        const user = await User.findOne({ email });
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'No user found with that email address'
            });
        }

        // Generate reset token with expiration (1 hour)
        const resetToken = jwt.sign(
            { id: user._id },
            process.env.JWT_SECRET,
            { expiresIn: '10m' }
        );

        // Create reset URL
        const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

        // Create email content
        const message = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Password Reset Request</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #fff7ed; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
">
  
  <!-- Email Container -->
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #fff7ed;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <!-- Main Content Card -->
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
              padding: 48px 40px 40px;
              text-align: center;
            ">
              
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Password Reset Request</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Secure your BlogBuster account</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hello!</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 32px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">You have requested to reset your password for your BlogBuster account. Click the button below to reset your password:</p>
              
              <!-- Reset Button -->
              <div style="text-align: center; margin: 40px 0;">
                <!--[if mso]>
                <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${resetUrl}" style="height:56px;v-text-anchor:middle;width:240px;" arcsize="21%" stroke="f" fillcolor="#ea580c">
                <w:anchorlock/>
                <center style="color:#ffffff;font-family:sans-serif;font-size:16px;font-weight:600;">Reset Password</center>
                </v:roundrect>
                <![endif]-->
                <!--[if !mso]><!-->
                <a href="${resetUrl}" style="
                  display: inline-block;
                  background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                  color: #ffffff;
                  text-decoration: none;
                  padding: 16px 40px;
                  border-radius: 12px;
                  font-weight: 600;
                  font-size: 16px;
                  letter-spacing: 0.025em;
                  box-shadow: 0 8px 24px rgba(234, 88, 12, 0.25);
                  transition: all 0.2s ease;
                  border: none;
                  min-width: 200px;
                " target="_blank">Reset Password</a>
                <!--<![endif]-->
              </div>
              
              <!-- Alternative Link -->
              <div style="
                background-color: #fff7ed; 
                border: 1px solid #fed7aa;
                border-radius: 12px; 
                padding: 24px; 
                margin: 32px 0;
              ">
                <p style="
                  color: #6b7280; 
                  font-size: 14px; 
                  margin: 0 0 12px 0; 
                  font-weight: 500;
                ">If the button doesn't work, copy and paste this link into your browser:</p>
                
                <p style="
                  color: #ea580c; 
                  font-size: 14px; 
                  word-break: break-all; 
                  margin: 0; 
                  font-family: 'SF Mono', Monaco, Consolas, monospace;
                  background-color: #ffffff;
                  padding: 12px;
                  border-radius: 6px;
                  border: 1px solid #fed7aa;
                "><a href="${resetUrl}" style="color: #ea580c; text-decoration: none;">${resetUrl}</a></p>
              </div>
              
              <!-- Security Notice -->
              <div style="
                border-left: 4px solid #ea580c; 
                background-color: #fff7ed; 
                padding: 20px; 
                border-radius: 0 8px 8px 0; 
                margin: 32px 0;
              ">
                <p style="
                  color: #9a3412; 
                  font-size: 14px; 
                  margin: 0 0 8px 0; 
                  font-weight: 600;
                ">⚠️ Important Security Notice</p>
                <p style="
                  color: #c2410c; 
                  font-size: 14px; 
                  margin: 0; 
                  line-height: 1.5;
                ">This link will expire in 10 minutes for security reasons. If you did not request this password reset, please ignore this email and your password will remain unchanged.</p>
              </div>
              
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="
              background-color: #fff7ed; 
              border-top: 1px solid #fed7aa; 
              padding: 32px 40px; 
              text-align: center;
            ">
              <p style="
                color: #64748b; 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Thanks,<br><strong style="color: #ea580c;">The BlogBuster Team</strong></p>
              
              <!-- Copyright -->
              <p style="
                color: #9ca3af; 
                font-size: 12px; 
                margin: 24px 0 0 0;
              ">© ${new Date().getFullYear()} BlogBuster. All rights reserved.</p>
            </td>
          </tr>
          
        </table>
        
      </td>
    </tr>
  </table>
  
  <!-- Mobile Styles -->
  <style>
    @media only screen and (max-width: 600px) {
      .email-container {
        padding: 20px 16px !important;
      }
      .content-padding {
        padding: 32px 24px !important;
      }
      .header-padding {
        padding: 32px 24px 24px !important;
      }
      .footer-padding {
        padding: 24px !important;
      }
      .button-container {
        margin: 32px 0 !important;
      }
      .reset-button {
        padding: 14px 32px !important;
        font-size: 15px !important;
        min-width: 180px !important;
      }
      .title {
        font-size: 28px !important;
      }
      .subtitle {
        font-size: 15px !important;
      }
    }
  </style>
  
</body>
</html>`;

        // Setup email transporter with Gmail SMTP
        const transporter = nodemailer.createTransport({
            service: 'gmail',
            auth: {
                user: process.env.EMAIL_USER || '<EMAIL>',
                pass: process.env.EMAIL_APP_PASSWORD || 'eewnuizuylfddyml'
            }
        });

        // Send email
        await transporter.sendMail({
            from: `"BlogBuster" <${process.env.EMAIL_USER || '<EMAIL>'}>`,
            to: user.email,
            subject: 'Password Reset Request',
            html: message
        });

        console.log(`Password reset email sent to: ${user.email}`);

        // For development/testing, also log the reset URL to console
        if (process.env.NODE_ENV === 'development') {
            console.log('Development mode - Password reset URL:', resetUrl);
        }

        res.status(200).json({
            success: true,
            message: 'Password reset link sent to your email'
        });

    } catch (error) {
        console.error('Forgot password error:', error);
        res.status(500).json({
            success: false,
            message: 'Error processing password reset request'
        });
    }
};

// Reset Password
const resetPassword = async (req, res) => {
    try {
        const { token, password } = req.body;

        if (!token || !password) {
            return res.status(400).json({
                success: false,
                message: 'Token and new password are required'
            });
        }

        if (password.length < 8) {
            return res.status(400).json({
                success: false,
                message: 'Password must be at least 8 characters long'
            });
        }

        // Verify token
        let decoded;
        try {
            decoded = jwt.verify(token, process.env.JWT_SECRET);
        } catch (error) {
            return res.status(401).json({
                success: false,
                message: 'Invalid or expired token'
            });
        }

        // Get user
        const user = await User.findById(decoded.id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Set new password (will be hashed by pre-save hook)
        user.password = password;
        await user.save();

        res.status(200).json({
            success: true,
            message: 'Password reset successful'
        });

    } catch (error) {
        console.error('Reset password error:', error);
        res.status(500).json({
            success: false,
            message: 'Error resetting password'
        });
    }
};

// Update profile picture
const updateProfilePicture = async (req, res) => {
    try {
        if (!req.body.avatar) {
            return res.status(400).json({
                success: false,
                message: 'Profile picture is required'
            });
        }

        // Get user from middleware
        const userId = req.user.id;

        // Update user's avatar
        const updatedUser = await User.findByIdAndUpdate(
            userId,
            { avatar: req.body.avatar },
            { new: true, select: '-password' }
        );

        if (!updatedUser) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.status(200).json({
            success: true,
            data: updatedUser,
            message: 'Profile picture updated successfully'
        });

    } catch (error) {
        console.error('Error updating profile picture:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating profile picture'
        });
    }
};

// Add this function to send welcome emails
const sendWelcomeEmail = async (user) => {
    try {
        // Get user's name
        const name = user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;

        // Create welcome email content
        const message = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to BlogBuster!</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #fff7ed; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
">
  
  <!-- Email Container -->
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #fff7ed;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <!-- Main Content Card -->
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
              padding: 48px 40px 40px;
              text-align: center;
            ">
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Welcome to BlogBuster!</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Your SEO Autoblog Solution</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hi ${name},</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Thank you for joining BlogBuster! We're excited to have you on board. With BlogBuster, you can create and manage SEO-optimized blogs automatically, saving you time and effort while growing your online presence.</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 32px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">Get started by setting up your first blog and exploring our powerful features:</p>
              
              <!-- Features List -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin-bottom: 32px;">
                <tr>
                  <td style="padding: 12px 16px; background-color: #fff7ed; border-radius: 8px; margin-bottom: 8px;">
                    <p style="margin: 0; color: #9a3412; font-weight: 500;">✨ Automated Content Generation</p>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 12px 16px; background-color: #fff7ed; border-radius: 8px; margin-bottom: 8px;">
                    <p style="margin: 0; color: #9a3412; font-weight: 500;">🔍 SEO Optimization Tools</p>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 12px 16px; background-color: #fff7ed; border-radius: 8px; margin-bottom: 8px;">
                    <p style="margin: 0; color: #9a3412; font-weight: 500;">📊 Analytics Dashboard</p>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 12px 16px; background-color: #fff7ed; border-radius: 8px;">
                    <p style="margin: 0; color: #9a3412; font-weight: 500;">🔄 Content Scheduling</p>
                  </td>
                </tr>
              </table>
              
              <!-- CTA Button -->
              <div style="text-align: center; margin: 40px 0;">
                <!--[if mso]>
                <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${process.env.FRONTEND_URL}/dashboard/articles" style="height:56px;v-text-anchor:middle;width:240px;" arcsize="21%" stroke="f" fillcolor="#ea580c">
                <w:anchorlock/>
                <center style="color:#ffffff;font-family:sans-serif;font-size:16px;font-weight:600;">Get Started Now</center>
                </v:roundrect>
                <![endif]-->
                <!--[if !mso]><!-->
                <a href="${process.env.FRONTEND_URL}/dashboard/articles" style="
                  display: inline-block;
                  background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                  color: #ffffff;
                  text-decoration: none;
                  padding: 16px 40px;
                  border-radius: 12px;
                  font-weight: 600;
                  font-size: 16px;
                  letter-spacing: 0.025em;
                  box-shadow: 0 8px 24px rgba(234, 88, 12, 0.25);
                  transition: all 0.2s ease;
                  border: none;
                  min-width: 200px;
                " target="_blank">Get Started Now</a>
                <!--<![endif]-->
              </div>
              
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="
              background-color: #fff7ed; 
              border-top: 1px solid #fed7aa; 
              padding: 32px 40px; 
              text-align: center;
            ">
              <p style="
                color: #64748b; 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">Thanks,<br><strong style="color: #ea580c;">The BlogBuster Team</strong></p>
              
              <!-- Copyright -->
              <p style="
                color: #9ca3af; 
                font-size: 12px; 
                margin: 24px 0 0 0;
              ">© ${new Date().getFullYear()} BlogBuster. All rights reserved.</p>
            </td>
          </tr>
          
        </table>
        
      </td>
    </tr>
  </table>
  
  <!-- Mobile Styles -->
  <style>
    @media only screen and (max-width: 600px) {
      .email-container {
        padding: 20px 16px !important;
      }
      .content-padding {
        padding: 32px 24px !important;
      }
      .header-padding {
        padding: 32px 24px 24px !important;
      }
      .footer-padding {
        padding: 24px !important;
      }
      .button-container {
        margin: 32px 0 !important;
      }
      .reset-button {
        padding: 14px 32px !important;
        font-size: 15px !important;
        min-width: 180px !important;
      }
      .title {
        font-size: 28px !important;
      }
      .subtitle {
        font-size: 15px !important;
      }
    }
  </style>
  
</body>
</html>`;

        // Setup email transporter with Gmail SMTP
        const transporter = nodemailer.createTransport({
            service: 'gmail',
            auth: {
                user: process.env.EMAIL_USER || '<EMAIL>',
                pass: process.env.EMAIL_APP_PASSWORD || 'eewnuizuylfddyml'
            }
        });

        // Send email
        await transporter.sendMail({
            from: `"BlogBuster" <${process.env.EMAIL_USER || '<EMAIL>'}>`,
            to: user.email,
            subject: 'Welcome to BlogBuster!',
            html: message
        });

        console.log(`Welcome email sent to: ${user.email}`);
        return true;
    } catch (error) {
        console.error('Error sending welcome email:', error);
        return false;
    }
};

module.exports = {
    updateProfile,
    changePassword,
    deleteAccount,
    getUserById,
    updateProfilePicture,
    forgotPassword,
    resetPassword,
    sendWelcomeEmail
}; 