import React, { useState, useEffect } from 'react';
import { useDomain } from '@/contexts/DomainContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Loader2, CheckCircle2, XCircle, FileText, RefreshCw, Pencil } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import apiClient from '@/lib/api-client';
import { Domain as BaseDomain, BrandInfo, ArticleSettings } from '@/services/domainService';

// Define domain subscription type
interface DomainSubscription {
    active?: boolean;
    planType?: string;
    credits?: number;
    expiresAt?: string;
}

// Extended Domain type with subscription
type Domain = BaseDomain & {
    subscription?: DomainSubscription;
};

// Interface for generated article
interface GeneratedArticle {
    id: string;
    title: string;
    status: string;
    date?: string;
    stage?: string;
    progress?: number;
    description?: string;
    tag?: string;
    urlSlug?: string;
    author?: string | { name: string };
}

// Generation stages
type GenerationStage = 'pending' | 'topic' | 'content' | 'metadata' | 'saving' | 'complete' | 'error';

export const BulkGenerateView = () => {
    const { currentDomain, refreshDomains } = useDomain();
    const [isGenerating, setIsGenerating] = useState(false);
    const [generationCount, setGenerationCount] = useState(5);
    const [customTopics, setCustomTopics] = useState('');
    const [useCustomTopics, setUseCustomTopics] = useState(false);
    const [autoPublish, setAutoPublish] = useState(false);
    const [language, setLanguage] = useState('English');

    // Progress tracking for bulk generation
    const [overallProgress, setOverallProgress] = useState(0);
    const [generatedArticles, setGeneratedArticles] = useState<GeneratedArticle[]>([]);
    const [currentArticleIndex, setCurrentArticleIndex] = useState(0);
    const [currentStage, setCurrentStage] = useState<GenerationStage>('pending');

    // Subscription check results
    const [subscriptionValid, setSubscriptionValid] = useState(true);
    const [subscriptionMessage, setSubscriptionMessage] = useState('');

    useEffect(() => {
        document.title = 'Bulk Generate | BlogBuster';

        // Set language from domain settings if available
        if (currentDomain && (currentDomain as Domain).articleSettings?.language) {
            setLanguage((currentDomain as Domain).articleSettings?.language || 'English');
        }
    }, [currentDomain]);

    // Check subscription validity
    useEffect(() => {
        if (!currentDomain) return;

        const domainWithSub = currentDomain as unknown as Domain;

        // Check if subscription is active
        if (!domainWithSub.subscription?.active) {
            setSubscriptionValid(false);
            setSubscriptionMessage('This domain does not have an active subscription.');
            return;
        }

        // Check if there are enough credits
        if ((domainWithSub.subscription?.credits || 0) < generationCount) {
            setSubscriptionValid(false);
            setSubscriptionMessage(`Insufficient credits. You need ${generationCount} credits but have only ${domainWithSub.subscription?.credits || 0}.`);
            return;
        }

        // Check if subscription is expired
        if (domainWithSub.subscription?.expiresAt) {
            const expiryDate = new Date(domainWithSub.subscription.expiresAt);
            if (expiryDate < new Date()) {
                setSubscriptionValid(false);
                setSubscriptionMessage(`Subscription expired on ${expiryDate.toLocaleDateString()}.`);
                return;
            }
        }

        setSubscriptionValid(true);
        setSubscriptionMessage('');
    }, [currentDomain, generationCount]);

    // Function to simulate the generation process with animated stages
    const simulateArticleGeneration = async (index: number, topic?: string) => {
        setCurrentArticleIndex(index);

        // Update the article at the current index with a pending status
        setGeneratedArticles(prev => {
            const updated = [...prev];
            updated[index] = {
                ...updated[index],
                stage: 'pending',
                progress: 0
            };
            return updated;
        });

        // Simulate topic generation (skip if topic is provided)
        if (!topic) {
            setCurrentStage('topic');
            await simulateStageProgress(index, 'topic', 20);
        }

        // Simulate content generation
        setCurrentStage('content');
        await simulateStageProgress(index, 'content', 40);

        // Simulate metadata generation
        setCurrentStage('metadata');
        await simulateStageProgress(index, 'metadata', 20);

        // Simulate saving
        setCurrentStage('saving');
        await simulateStageProgress(index, 'saving', 20);

        // Don't mark as complete yet - we'll only do that after the API call succeeds
        // This ensures we only show "Complete!" when the article is actually generated
        setGeneratedArticles(prev => {
            const updated = [...prev];
            updated[index] = {
                ...updated[index],
                stage: 'saving', // Keep at saving stage until API confirms completion
                progress: 99     // Set to 99% until confirmed by API
            };
            return updated;
        });
    };

    // Helper to simulate progress of each stage
    const simulateStageProgress = async (articleIndex: number, stage: string, durationPercent: number) => {
        const baseProgress = stage === 'topic' ? 0 :
            stage === 'content' ? 20 :
                stage === 'metadata' ? 60 :
                    stage === 'saving' ? 80 : 0;

        const stepsCount = 10;
        const stepSize = durationPercent / stepsCount;

        for (let step = 1; step <= stepsCount; step++) {
            await new Promise(resolve => setTimeout(resolve, 100));
            const stageProgress = baseProgress + (step * stepSize);

            setGeneratedArticles(prev => {
                const updated = [...prev];
                updated[articleIndex] = {
                    ...updated[articleIndex],
                    stage,
                    progress: stageProgress
                };
                return updated;
            });

            // Update overall progress
            const overall = ((articleIndex * 100) + stageProgress) / generationCount;
            setOverallProgress(Math.min(99, overall)); // Cap at 99% until fully complete
        }
    };

    const handleBulkGenerate = async () => {
        if (!currentDomain) {
            toast({
                title: 'No domain selected',
                description: 'Please select a domain to generate articles for.',
                variant: 'destructive',
            });
            return;
        }

        // Check domain subscription and credits
        const domainWithSub = currentDomain as unknown as Domain;
        if (!subscriptionValid) {
            toast({
                title: 'Subscription issue',
                description: subscriptionMessage,
                variant: 'destructive',
            });
            return;
        }

        setIsGenerating(true);
        setOverallProgress(0);
        setGeneratedArticles([]);
        setCurrentArticleIndex(0);
        setCurrentStage('pending');

        try {
            // Parse topics from the textarea
            let topics: string[] = [];
            if (useCustomTopics && customTopics.trim()) {
                topics = customTopics
                    .split('\n')
                    .map(topic => topic.trim())
                    .filter(topic => topic.length > 0);
            }

            // Initialize article placeholders for animation
            const initialArticles: GeneratedArticle[] = Array(generationCount).fill(null).map((_, i) => ({
                id: `temp-${i}`,
                title: topics[i] || `Generating article ${i + 1}...`,
                status: 'pending',
                stage: 'pending',
                progress: 0
            }));

            setGeneratedArticles(initialArticles);

            // Process each article with animation
            for (let i = 0; i < generationCount; i++) {
                setCurrentArticleIndex(i);
                // Simulate the generation process
                await simulateArticleGeneration(i, topics[i]);
            }

            // Make the actual API call to generate articles
            const response = await apiClient.post('/articles/bulk-generate', {
                domainId: currentDomain._id,
                count: generationCount,
                topics,
                language,
                autoPublish,
                useSettings: true // Flag to use domain settings
            });

            if (response.data.success) {
                // Set overall progress to 100% to indicate completion
                setOverallProgress(100);
                setCurrentStage('complete');

                // Process the API response to get detailed article information
                const completedArticles = response.data.articles.map((article: any, index: number) => {
                    // Extract article details from the API response
                    return {
                        id: article.id,
                        title: article.title || initialArticles[index].title,
                        status: article.status || 'generated',
                        date: article.date || new Date().toISOString(),
                        stage: 'complete',
                        progress: 100,
                        // Additional details if available
                        description: article.description || '',
                        author: article.author || '',
                        tag: article.tag || '',
                        urlSlug: article.urlSlug || ''
                    };
                });

                // Update the state with the completed articles
                setGeneratedArticles(completedArticles);

                // Show success message
                toast({
                    title: 'Bulk generation completed',
                    description: `Successfully generated ${response.data.generatedCount} articles for ${currentDomain.name}`,
                });

                // Refresh domain data to get updated credits
                await refreshDomains();
            } else {
                throw new Error(response.data.error || 'Failed to generate articles');
            }
        } catch (error) {
            console.error('Error in bulk generation:', error);
            toast({
                title: 'Generation failed',
                description: error instanceof Error ? error.message : 'An unknown error occurred',
                variant: 'destructive',
            });
            setCurrentStage('error');

            // Update article states to show error
            setGeneratedArticles(prev => {
                return prev.map(article => ({
                    ...article,
                    stage: 'error',
                    progress: 0
                }));
            });
        } finally {
            setIsGenerating(false);
        }
    };

    const formatCreditsInfo = () => {
        const domainWithSub = currentDomain as unknown as Domain;
        const credits = domainWithSub?.subscription?.credits || 0;
        return `Available credits: ${credits} | Will use: ${generationCount} | Remaining after: ${Math.max(0, credits - generationCount)}`;
    };

    // Get plan expiry date in readable format
    const getPlanExpiryInfo = () => {
        const domainWithSub = currentDomain as unknown as Domain;
        if (!domainWithSub?.subscription?.expiresAt) return 'No expiration date';

        const expiryDate = new Date(domainWithSub.subscription.expiresAt);
        const days = Math.max(0, Math.ceil((expiryDate.getTime() - new Date().getTime()) / (1000 * 3600 * 24)));

        return `${domainWithSub.subscription.planType || 'Unknown'} Plan - Expires on ${expiryDate.toLocaleDateString()} (${days} days left)`;
    };

    // Render the generation stage indicator
    const renderStageIndicator = (article: GeneratedArticle) => {
        if (!article.stage || article.stage === 'pending') {
            return <div className="flex items-center text-gray-500 text-xs"><RefreshCw className="mr-1 h-3 w-3" /> Waiting...</div>;
        }

        if (article.stage === 'topic') {
            return <div className="flex items-center text-[#EA580C] text-xs">
                <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                <span>Generating topic... ({Math.round(article.progress || 0)}%)</span>
            </div>;
        }

        if (article.stage === 'content') {
            return <div className="flex items-center text-[#EA580C] text-xs">
                <div className="relative">
                    <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                    <div className="absolute -top-1 -right-1 h-1.5 w-1.5 bg-green-500 rounded-full animate-ping" />
                </div>
                <span>Creating content... ({Math.round(article.progress || 0)}%)</span>
            </div>;
        }

        if (article.stage === 'metadata') {
            return <div className="flex items-center text-[#EA580C] text-xs">
                <div className="relative">
                    <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                    <div className="absolute -top-1 -right-1 h-1.5 w-1.5 bg-blue-500 rounded-full animate-ping" />
                </div>
                <span>Adding metadata... ({Math.round(article.progress || 0)}%)</span>
            </div>;
        }

        if (article.stage === 'saving') {
            return <div className="flex items-center text-[#EA580C] text-xs">
                <div className="relative">
                    <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                    <div className="absolute -top-1 -right-1 h-1.5 w-1.5 bg-purple-500 rounded-full animate-ping" />
                </div>
                <span>Saving article... ({Math.round(article.progress || 0)}%)</span>
            </div>;
        }

        if (article.stage === 'complete') {
            return <div className="flex items-center text-green-600 text-xs">
                <CheckCircle2 className="mr-1 h-3 w-3" />
                <span>Complete (100%)</span>
            </div>;
        }

        if (article.stage === 'error') {
            return <div className="flex items-center text-red-500 text-xs">
                <XCircle className="mr-1 h-3 w-3" />
                <span>Error occurred</span>
            </div>;
        }

        return null;
    };

    return (
        <div className="container mx-auto py-6 bg-[#FFF7ED]/30">
            <h1 className="text-2xl font-bold mb-6 text-gray-900">Bulk Article Generation</h1>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Settings Panel */}
                <div className="lg:col-span-1">
                    <Card className="p-4 border border-[#EA580C]/10 shadow-sm">
                        <h2 className="text-lg font-semibold mb-4 text-[#EA580C]">Generation Settings</h2>

                        {currentDomain ? (
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="domain-info" className="text-gray-700">Domain</Label>
                                    <div id="domain-info" className="text-sm mt-1 p-2 bg-[#FFF7ED] rounded border border-[#EA580C]/20">
                                        {currentDomain.name}
                                        <div className="text-xs text-gray-500 mt-1">{currentDomain.url}</div>
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="subscription-info" className="text-gray-700">Subscription</Label>
                                    <div id="subscription-info" className="text-sm mt-1 p-2 bg-[#FFF7ED] rounded border border-[#EA580C]/20">
                                        {getPlanExpiryInfo()}
                                        <div className={`text-xs mt-1 ${subscriptionValid ? 'text-green-600' : 'text-red-500'}`}>
                                            {subscriptionValid ? 'Active' : subscriptionMessage}
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="credits-info" className="text-gray-700">Credits Information</Label>
                                    <div id="credits-info" className="text-sm mt-1 p-2 bg-[#FFF7ED] rounded border border-[#EA580C]/20">
                                        {formatCreditsInfo()}
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="settings-info" className="text-gray-700">Article Settings</Label>
                                    <div id="settings-info" className="text-sm mt-1 p-2 bg-[#FFF7ED] rounded border border-[#EA580C]/20">
                                        <div>Using brand info: {(currentDomain as Domain).articleSettings?.useBrandInfo ? 'Yes' : 'No'}</div>
                                        <div>Length: {(currentDomain as Domain).articleSettings?.articleLength || 'Medium'}</div>
                                        <div>Default language: {(currentDomain as Domain).articleSettings?.language || 'English'}</div>
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="generation-count" className="text-gray-700">Number of Articles to Generate</Label>
                                    <div className="flex items-center gap-4">
                                        <Slider
                                            id="generation-count"
                                            value={[generationCount]}
                                            min={1}
                                            max={50}
                                            step={1}
                                            onValueChange={(values) => setGenerationCount(values[0])}
                                            disabled={isGenerating}
                                            className="flex-1"
                                        />
                                        <Input
                                            type="number"
                                            min={1}
                                            max={50}
                                            value={generationCount}
                                            onChange={(e) => setGenerationCount(Number(e.target.value))}
                                            disabled={isGenerating}
                                            className="w-20 border-[#EA580C]/20 focus-visible:ring-[#EA580C]"
                                        />
                                    </div>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="use-custom-topics"
                                        checked={useCustomTopics}
                                        onCheckedChange={setUseCustomTopics}
                                        disabled={isGenerating}
                                        className="data-[state=checked]:bg-[#EA580C]"
                                    />
                                    <Label htmlFor="use-custom-topics" className="text-gray-700">Use custom topics</Label>
                                </div>

                                {useCustomTopics && (
                                    <div>
                                        <Label htmlFor="custom-topics" className="text-gray-700">Custom Topics (one per line)</Label>
                                        <Textarea
                                            id="custom-topics"
                                            placeholder="Enter topics here, one per line..."
                                            value={customTopics}
                                            onChange={(e) => setCustomTopics(e.target.value)}
                                            disabled={isGenerating}
                                            className="min-h-32 border-[#EA580C]/20 focus-visible:ring-[#EA580C]"
                                        />
                                        <p className="text-xs text-gray-500 mt-1">
                                            {customTopics.split('\n').filter(line => line.trim().length > 0).length} topics entered
                                        </p>
                                    </div>
                                )}

                                <div>
                                    <Label htmlFor="language" className="text-gray-700">Language</Label>
                                    <Select
                                        value={language}
                                        onValueChange={setLanguage}
                                        disabled={isGenerating}
                                    >
                                        <SelectTrigger id="language" className="border-[#EA580C]/20 focus:ring-[#EA580C]">
                                            <SelectValue placeholder="Select language" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="English">English</SelectItem>
                                            <SelectItem value="Spanish">Spanish</SelectItem>
                                            <SelectItem value="French">French</SelectItem>
                                            <SelectItem value="German">German</SelectItem>
                                            <SelectItem value="Hindi">Hindi</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="auto-publish"
                                        checked={autoPublish}
                                        onCheckedChange={setAutoPublish}
                                        disabled={isGenerating}
                                        className="data-[state=checked]:bg-[#EA580C]"
                                    />
                                    <Label htmlFor="auto-publish" className="text-gray-700">Auto-publish generated articles</Label>
                                </div>

                                <Button
                                    onClick={handleBulkGenerate}
                                    className="w-full bg-[#EA580C] hover:bg-[#EA580C]/90 text-white"
                                    disabled={isGenerating || !currentDomain || !subscriptionValid}
                                >
                                    {isGenerating ? (
                                        <>
                                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            Generating ({Math.round(overallProgress)}%)
                                        </>
                                    ) : (
                                        `Generate ${generationCount} Articles`
                                    )}
                                </Button>
                            </div>
                        ) : (
                            <div className="text-center py-6">
                                <p className="text-gray-500">Please select a domain first</p>
                            </div>
                        )}
                    </Card>
                </div>

                {/* Results Panel */}
                <div className="lg:col-span-2">
                    <Card className="p-4 border border-[#EA580C]/10 shadow-sm">
                        <h2 className="text-lg font-semibold mb-4 text-[#EA580C]">Generation Results</h2>

                        {isGenerating && (
                            <div className="mb-4">
                                <div className="flex justify-between text-xs text-gray-500 mb-1">
                                    <span>Progress</span>
                                    <span className="font-medium">{Math.round(overallProgress)}%</span>
                                </div>
                                <div className="h-3 w-full bg-[#FFF7ED] rounded-full overflow-hidden shadow-inner">
                                    <div
                                        className="h-full bg-gradient-to-r from-[#EA580C]/80 to-[#EA580C] transition-all duration-500 ease-out animate-pulse"
                                        style={{ width: `${overallProgress}%` }}
                                    />
                                </div>
                                <p className="text-sm text-center mt-3 text-gray-700 font-medium">
                                    Generating {generationCount} articles...
                                    <span className="inline-block ml-2 px-2 py-0.5 bg-[#EA580C]/10 rounded-full text-[#EA580C] font-semibold animate-pulse">
                                        {currentArticleIndex + 1}/{generationCount}
                                    </span>
                                </p>
                                <div className="text-center text-sm text-[#EA580C] mt-2 font-medium transition-all duration-300 transform">
                                    {currentStage === 'pending' && (
                                        <div className="flex items-center justify-center space-x-2 animate-fadeIn">
                                            <Loader2 className="h-4 w-4 animate-spin" />
                                            <span>Preparing your content...</span>
                                        </div>
                                    )}
                                    {currentStage === 'topic' && (
                                        <div className="flex items-center justify-center space-x-2 animate-slideIn">
                                            <RefreshCw className="h-4 w-4 animate-spin" />
                                            <span>Finding the perfect topic for your audience...</span>
                                        </div>
                                    )}
                                    {currentStage === 'content' && (
                                        <div className="flex items-center justify-center space-x-2 animate-slideIn">
                                            <div className="relative">
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                                <div className="absolute -top-1 -right-1 h-2 w-2 bg-green-500 rounded-full animate-ping" />
                                            </div>
                                            <span>Creating high-quality content tailored to your brand...</span>
                                        </div>
                                    )}
                                    {currentStage === 'metadata' && (
                                        <div className="flex items-center justify-center space-x-2 animate-slideIn">
                                            <div className="relative">
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                                <div className="absolute -top-1 -right-1 h-2 w-2 bg-blue-500 rounded-full animate-ping" />
                                            </div>
                                            <span>Adding metadata and SEO optimization...</span>
                                        </div>
                                    )}
                                    {currentStage === 'saving' && (
                                        <div className="flex items-center justify-center space-x-2 animate-slideIn">
                                            <div className="relative">
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                                <div className="absolute -top-1 -right-1 h-2 w-2 bg-purple-500 rounded-full animate-ping" />
                                            </div>
                                            <span>Saving article to your library...</span>
                                        </div>
                                    )}
                                    {currentStage === 'complete' && (
                                        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md text-center">
                                            <div className="flex items-center justify-center space-x-2 text-green-600 animate-bounceIn">
                                                <CheckCircle2 className="h-5 w-5" />
                                                <span className="font-semibold">Complete!</span>
                                            </div>
                                            <p className="text-sm text-green-700 mt-1">
                                                Successfully generated {generationCount} articles for {currentDomain.name}
                                            </p>
                                        </div>
                                    )}
                                    {currentStage === 'error' && (
                                        <div className="flex items-center justify-center space-x-2 text-red-500 animate-shakeIn">
                                            <XCircle className="h-4 w-4" />
                                            <span>Error occurred during generation.</span>
                                        </div>
                                    )}
                                </div>

                                {/* Animated sliding text section */}
                                <div className="mt-6 mb-4">
                                    {/* Row 1 */}
                                    <div className="relative w-full overflow-hidden py-2" style={{
                                        maskImage: 'linear-gradient(to right, transparent, black 5%, black 95%, transparent)',
                                        WebkitMaskImage: 'linear-gradient(to right, transparent, black 5%, black 95%, transparent)'
                                    }}>
                                        <div className="inline-flex animate-slideInfinite whitespace-nowrap" style={{ animationDuration: '20s' }}>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Creating engaging content for your audience
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#ECFDF5] text-green-600 border border-green-200 font-medium">
                                                Optimizing SEO for better visibility
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#EFF6FF] text-blue-600 border border-blue-200 font-medium">
                                                Crafting compelling headlines
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#F5F3FF] text-purple-600 border border-purple-200 font-medium">
                                                Analyzing trending topics in your niche
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFFBEB] text-amber-600 border border-amber-200 font-medium">
                                                Generating reader-friendly formatting
                                            </div>

                                            {/* Duplicate for continuous scrolling */}
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Creating engaging content for your audience
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#ECFDF5] text-green-600 border border-green-200 font-medium">
                                                Optimizing SEO for better visibility
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#EFF6FF] text-blue-600 border border-blue-200 font-medium">
                                                Crafting compelling headlines
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#F5F3FF] text-purple-600 border border-purple-200 font-medium">
                                                Analyzing trending topics in your niche
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFFBEB] text-amber-600 border border-amber-200 font-medium">
                                                Generating reader-friendly formatting
                                            </div>
                                        </div>
                                    </div>

                                    {/* Row 2 */}
                                    <div className="relative w-full overflow-hidden py-2" style={{
                                        maskImage: 'linear-gradient(to right, transparent, black 5%, black 95%, transparent)',
                                        WebkitMaskImage: 'linear-gradient(to right, transparent, black 5%, black 95%, transparent)'
                                    }}>
                                        <div className="inline-flex animate-slideInfiniteReverse whitespace-nowrap" style={{ animationDuration: '25s' }}>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#F0FDFA] text-teal-600 border border-teal-200 font-medium">
                                                Researching keywords for maximum impact
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Enhancing your brand's online presence
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF1F2] text-rose-600 border border-rose-200 font-medium">
                                                Structuring content for readability
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#EEF2FF] text-indigo-600 border border-indigo-200 font-medium">
                                                Incorporating industry best practices
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Boosting your content marketing strategy
                                            </div>

                                            {/* Duplicate for continuous scrolling */}
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#F0FDFA] text-teal-600 border border-teal-200 font-medium">
                                                Researching keywords for maximum impact
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Enhancing your brand's online presence
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF1F2] text-rose-600 border border-rose-200 font-medium">
                                                Structuring content for readability
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#EEF2FF] text-indigo-600 border border-indigo-200 font-medium">
                                                Incorporating industry best practices
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Boosting your content marketing strategy
                                            </div>
                                        </div>
                                    </div>

                                    {/* Row 3 */}
                                    <div className="relative w-full overflow-hidden py-2" style={{
                                        maskImage: 'linear-gradient(to right, transparent, black 5%, black 95%, transparent)',
                                        WebkitMaskImage: 'linear-gradient(to right, transparent, black 5%, black 95%, transparent)'
                                    }}>
                                        <div className="inline-flex animate-slideInfinite whitespace-nowrap" style={{ animationDuration: '22s' }}>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#ECFEFF] text-cyan-600 border border-cyan-200 font-medium">
                                                Tailoring content to your target audience
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Boosting your content marketing strategy
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#F7FEE7] text-lime-600 border border-lime-200 font-medium">
                                                Ensuring factual accuracy and quality
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FAE8FF] text-fuchsia-600 border border-fuchsia-200 font-medium">
                                                Applying persuasive writing techniques
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Generating engaging visual content
                                            </div>

                                            {/* Duplicate for continuous scrolling */}
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#ECFEFF] text-cyan-600 border border-cyan-200 font-medium">
                                                Tailoring content to your target audience
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Boosting your content marketing strategy
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#F7FEE7] text-lime-600 border border-lime-200 font-medium">
                                                Ensuring factual accuracy and quality
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FAE8FF] text-fuchsia-600 border border-fuchsia-200 font-medium">
                                                Applying persuasive writing techniques
                                            </div>
                                            <div className="mx-2 px-5 py-2.5 rounded-md bg-[#FFF7ED] text-[#EA580C] border border-[#EA580C]/20 font-medium">
                                                Generating engaging visual content
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {generatedArticles.length > 0 ? (
                            <div className="space-y-4">
                                {currentStage === 'complete' && !isGenerating && (
                                    <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                                        <div className="flex items-center justify-center">
                                            <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                                            <span className="font-medium text-green-700">Generation Complete!</span>
                                        </div>
                                        <p className="text-sm text-green-700 text-center mt-1">
                                            Successfully generated {generatedArticles.length} articles for {currentDomain.name}
                                        </p>
                                    </div>
                                )}

                                <div className="flex justify-end items-center mb-2">
                                    <p className="text-sm text-gray-500">{generatedArticles.length} articles</p>
                                </div>

                                {generatedArticles.map((article, index) => (
                                    <div
                                        key={article.id}
                                        className="border border-gray-200 rounded-lg p-3 flex justify-between items-center bg-white hover:bg-gray-50 transition-all duration-300"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center">
                                                <FileText className="h-4 w-4 text-gray-500 mr-2 flex-shrink-0" />
                                                <p className="font-medium text-gray-800">{article.title}</p>
                                            </div>

                                            {article.description && (
                                                <p className="text-gray-600 text-sm mt-1 line-clamp-2 ml-6">
                                                    {article.description}
                                                </p>
                                            )}
                                        </div>
                                        <div className="ml-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => article.id && !article.id.startsWith('temp-') ? window.location.href = `/dashboard/articles/edit/${article.id}` : null}
                                                className="border-gray-200 text-gray-700 hover:bg-gray-50"
                                                disabled={!article.id || article.id.startsWith('temp-')}
                                            >
                                                <Pencil className="h-4 w-4 mr-1" />
                                                Edit
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12 text-gray-500 bg-[#FFF7ED]/40 rounded-lg border border-[#EA580C]/10">
                                {isGenerating ?
                                    <div className="animate-pulse">Generating articles...</div> :
                                    'Generated articles will appear here'
                                }
                            </div>
                        )}
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default BulkGenerateView; 