const axios = require('axios');

class UnsplashService {
    constructor() {
        this.accessKey = process.env.UNSPLASH_ACCESS_KEY;
        this.baseUrl = 'https://api.unsplash.com';
        this.usedImageIds = new Set(); // Keep track of used image IDs
    }

    async searchImage(query) {
        try {
            if (!this.accessKey) {
                throw new Error('Unsplash API key not configured');
            }

            // Fetch multiple images
            const response = await axios.get(`${this.baseUrl}/search/photos`, {
                params: {
                    query,
                    per_page: 10, // Fetch 10 images to have a good selection
                    orientation: 'landscape'
                },
                headers: {
                    Authorization: `Client-ID ${this.accessKey}`
                }
            });

            if (response.data.results && response.data.results.length > 0) {
                // Filter out previously used images
                const availableImages = response.data.results.filter(img => !this.usedImageIds.has(img.id));

                // If all images have been used, clear the history and use all images
                if (availableImages.length === 0) {
                    this.usedImageIds.clear();
                    availableImages.push(...response.data.results);
                }

                // Randomly select an image from available ones
                const randomIndex = Math.floor(Math.random() * availableImages.length);
                const image = availableImages[randomIndex];

                // Mark this image as used
                this.usedImageIds.add(image.id);

                return {
                    url: image.urls.regular,
                    thumb: image.urls.thumb,
                    description: image.description || image.alt_description,
                    credit: {
                        name: image.user.name,
                        username: image.user.username,
                        link: image.user.links.html
                    }
                };
            }

            return null;
        } catch (error) {
            console.error('Error fetching image from Unsplash:', error);
            return null;
        }
    }

    // Method to clear used images history if needed
    clearUsedImagesHistory() {
        this.usedImageIds.clear();
    }
}

module.exports = new UnsplashService(); 