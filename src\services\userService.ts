import apiClient from '../lib/api-client';

export interface UpdateProfileData {
    name?: string;
    firstName?: string;
    lastName?: string;
}

export interface ChangePasswordData {
    currentPassword: string;
    newPassword: string;
}

export interface ForgotPasswordData {
    email: string;
}

export interface ResetPasswordData {
    token: string;
    password: string;
}

export interface UserResponse {
    success: boolean;
    data?: any;
    message?: string;
}

export interface User {
    id: string;
    name: string;
    firstName: string;
    lastName: string;
    avatar: string;
}

export const userService = {
    getUserById: async (userId: string): Promise<User> => {
        try {
            const response = await apiClient.get(`/auth/user/${userId}`);
            if (!response.data.success) {
                throw new Error(response.data.message || 'Failed to fetch user');
            }
            return response.data.data;
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Error fetching user';
            console.error('Error fetching user:', error);
            throw new Error(errorMessage);
        }
    },

    updateProfile: async (data: UpdateProfileData): Promise<UserResponse> => {
        try {
            // If we have name but not firstName/lastName, split it
            if (data.name && (!data.firstName || !data.lastName)) {
                const nameParts = data.name.trim().split(' ');
                data.firstName = nameParts[0];
                data.lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
                delete data.name; // Remove name from the payload
            }

            const response = await apiClient.put('/auth/profile', data);
            return response.data;
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Error updating profile';
            console.error('Profile update error:', error);
            throw new Error(errorMessage);
        }
    },

    updateProfilePicture: async (avatar: string): Promise<UserResponse> => {
        try {
            const response = await apiClient.put('/auth/profile/picture', { avatar });
            return response.data;
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Error updating profile picture';
            console.error('Profile picture update error:', error);
            throw new Error(errorMessage);
        }
    },

    changePassword: async (data: ChangePasswordData): Promise<UserResponse> => {
        try {
            const response = await apiClient.put('/auth/change-password', data);
            if (!response.data.success) {
                throw new Error(response.data.message || 'Failed to change password');
            }
            return response.data;
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Error changing password';
            console.error('Password change error:', error);
            throw new Error(errorMessage);
        }
    },

    forgotPassword: async (data: ForgotPasswordData): Promise<UserResponse> => {
        try {
            const response = await apiClient.post('/auth/forgot-password', data);
            return response.data;
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Error processing forgot password request';
            console.error('Forgot password error:', error);
            throw new Error(errorMessage);
        }
    },

    resetPassword: async (data: ResetPasswordData): Promise<UserResponse> => {
        try {
            const response = await apiClient.post('/auth/reset-password', data);
            return response.data;
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Error resetting password';
            console.error('Reset password error:', error);
            throw new Error(errorMessage);
        }
    },

    deleteAccount: async (): Promise<void> => {
        try {
            await apiClient.delete('/auth/delete-account');
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'Error deleting account';
            console.error('Account deletion error:', error);
            throw new Error(errorMessage);
        }
    }
}; 