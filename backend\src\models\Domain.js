const mongoose = require('mongoose');

const brandInfoSchema = new mongoose.Schema({
    name: {
        type: String,
        required: false
    },
    description: {
        type: String,
        required: false
    },
    targetAudience: {
        type: String,
        required: false
    },
    logo: {
        type: String,
        required: false
    },
    audienceLocation: {
        type: String,
        required: false
    },
    benefits: [{
        type: String,
        trim: true
    }],
    toneOfVoice: {
        type: String,
        required: false
    },
    industry: {
        type: String,
        required: false
    },
    tags: [{
        type: String,
        trim: true
    }]
}, { _id: false });

const articleSettingsSchema = new mongoose.Schema({
    useBrandInfo: {
        type: Boolean,
        default: true
    },
    articleLength: {
        type: String,
        enum: ['short', 'medium', 'long'],
        default: 'short'
    },
    wordCountRanges: {
        type: Map,
        of: {
            min: Number,
            max: Number
        },
        default: {
            short: { min: 50, max: 100 },
            medium: { min: 100, max: 200 },
            long: { min: 200, max: 300 }
        }
    },
    language: {
        type: String,
        default: 'English',
        required: true
    },
    specificInstructions: {
        type: String,
        default: ''
    },
    exclusions: {
        type: String,
        default: ''
    }
}, { _id: false });

// Add subscription schema
const subscriptionSchema = new mongoose.Schema({
    active: {
        type: Boolean,
        default: false
    },
    planType: {
        type: String,
        enum: ['Free', 'Monthly', 'Yearly', 'Daily'],
        default: 'Free'
    },
    credits: {
        type: Number,
        default: 0
    },
    stripeCustomerId: {
        type: String
    },
    stripeSubscriptionId: {
        type: String
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    canceledAt: {
        type: Date
    },
    expiresAt: {
        type: Date
    }
}, { _id: false });

// Add navigationSettings schema
const linkSchema = new mongoose.Schema({
    id: String,
    text: String,
    url: String
}, { _id: false });

const navigationSettingsSchema = new mongoose.Schema({
    homeButtonEnabled: {
        type: Boolean,
        default: false
    },
    ctaButtonDisabled: {
        type: Boolean,
        default: true
    },
    ctaButtonText: {
        type: String,
        default: ''
    },
    ctaButtonUrl: {
        type: String,
        default: ''
    },
    headerLinks: [linkSchema],
    footerLinks: [linkSchema]
}, { _id: false });

const domainSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Domain name is required'],
        trim: true
    },
    url: {
        type: String,
        required: [true, 'Domain URL is required'],
        trim: true,
        index: false // Explicitly disable single-field index
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    title: {
        type: String,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    brandInfo: {
        type: brandInfoSchema,
        required: true,
        default: function () {
            return {
                name: this.title || this.name || '',
                description: this.description || '',
                targetAudience: 'General audience',
                audienceLocation: 'Global',
                benefits: [],
                toneOfVoice: 'Professional',
                industry: 'General',
                tags: []
            };
        }
    },
    articleSettings: {
        type: articleSettingsSchema,
        required: true,
        default: () => ({
            useBrandInfo: true,
            articleLength: 'short',
            wordCountRanges: {
                short: { min: 50, max: 100 },
                medium: { min: 100, max: 200 },
                long: { min: 200, max: 300 }
            },
            language: 'English',
            specificInstructions: '',
            exclusions: ''
        })
    },
    isDefault: {
        type: Boolean,
        default: false
    },
    metadata: {
        scrapedAt: { type: Date },
        confidence: { type: Number, min: 0, max: 100 },
        dataPoints: { type: Number, min: 0 },
        fallback: { type: Boolean, default: false },
        error: { type: String }
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    hostingSettings: {
        domain: String,
        subdomain: String,
        isVerified: {
            type: Boolean,
            default: false
        },
        cnameRecords: {
            one: {
                host: String,
                value: String
            },
            two: {
                host: String,
                value: String
            }
        }
    },
    designSettings: {
        logo: String,
        articleTheme: {
            type: String,
            default: 'Daily Delight'
        },
        layout: {
            grid: {
                type: String,
                default: 'grid-3'
            },
            listEnabled: {
                type: Boolean,
                default: false
            }
        },
        colors: {
            brand: {
                type: String,
                default: '#3c484c'
            },
            accent: {
                type: String,
                default: '#cec4c0'
            }
        },
        font: {
            type: String,
            default: 'Arial'
        }
    },
    navigationSettings: {
        type: navigationSettingsSchema,
        default: () => ({
            homeButtonEnabled: false,
            ctaButtonDisabled: true,
            headerLinks: [],
            footerLinks: []
        })
    },
    // Add subscription field
    subscription: {
        type: subscriptionSchema,
        default: () => ({})
    }
}, {
    timestamps: true
});

// Create a compound index for URL and userId to ensure uniqueness per user
domainSchema.index({ url: 1, userId: 1 }, { unique: true, name: 'url_userId_unique' });

// Ensure only one default domain exists per user
domainSchema.pre('save', async function (next) {
    if (this.isDefault) {
        await this.constructor.updateMany(
            { userId: this.userId, _id: { $ne: this._id } },
            { isDefault: false }
        );
    }
    next();
});

// Pre-save middleware to ensure brandInfo and articleSettings are properly initialized
domainSchema.pre('save', function (next) {
    // Initialize brandInfo if not set at all
    if (!this.brandInfo) {
        this.brandInfo = {};
    }

    // Only set name if it's completely missing
    if (!this.brandInfo.name) {
        this.brandInfo.name = this.title || this.name || '';
    }

    // Initialize empty arrays if they don't exist
    if (!this.brandInfo.benefits) {
        this.brandInfo.benefits = [];
    }
    if (!this.brandInfo.tags) {
        this.brandInfo.tags = [];
    }

    // Initialize articleSettings if not set
    if (!this.articleSettings) {
        this.articleSettings = {
            useBrandInfo: true,
            articleLength: 'short',
            wordCountRanges: {
                short: { min: 50, max: 100 },
                medium: { min: 100, max: 200 },
                long: { min: 200, max: 300 }
            },
            language: 'English',
            specificInstructions: '',
            exclusions: ''
        };
    }

    next();
});

module.exports = mongoose.model('Domain', domainSchema);
