require('dotenv').config();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Configuration - You can modify these values
const DAILY_PLAN_PRICE_ID = process.env.PRICE_ID_DAILY; // Will be read from environment
const DRY_RUN = false; // Set to true to see what would be canceled without actually canceling
const BATCH_SIZE = 100; // Process subscriptions in batches to avoid rate limits

// Logging function
function log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] ${message}`);
}

// Function to get all subscriptions for a specific price ID
async function getAllSubscriptionsForPrice(priceId) {
    log(`Fetching all subscriptions for price ID: ${priceId}`);

    const subscriptions = [];
    let hasMore = true;
    let startingAfter = null;

    while (hasMore) {
        try {
            const params = {
                limit: BATCH_SIZE,
                status: 'all', // Include all statuses
                price: priceId,
                expand: ['data.customer']
            };

            if (startingAfter) {
                params.starting_after = startingAfter;
            }

            const response = await stripe.subscriptions.list(params);

            subscriptions.push(...response.data);
            hasMore = response.has_more;

            if (hasMore && response.data.length > 0) {
                startingAfter = response.data[response.data.length - 1].id;
            }

            log(`Fetched ${response.data.length} subscriptions. Total so far: ${subscriptions.length}`);

            // Add a small delay to respect rate limits
            await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
            log(`Error fetching subscriptions: ${error.message}`, 'error');
            throw error;
        }
    }

    return subscriptions;
}

// Function to cancel a single subscription
async function cancelSubscription(subscription) {
    try {
        if (DRY_RUN) {
            log(`[DRY RUN] Would cancel subscription: ${subscription.id} (Customer: ${subscription.customer?.email || subscription.customer?.id || 'Unknown'})`);
            return { success: true, dryRun: true };
        }

        const canceledSubscription = await stripe.subscriptions.cancel(subscription.id);

        log(`Successfully canceled subscription: ${subscription.id} (Customer: ${subscription.customer?.email || subscription.customer?.id || 'Unknown'})`, 'success');

        return { success: true, canceledSubscription };

    } catch (error) {
        log(`Failed to cancel subscription ${subscription.id}: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

// Function to cancel all daily plan subscriptions
async function cancelAllDailySubscriptions() {
    try {
        log('Starting daily plan subscription cancellation process...');

        // Validate environment variables
        if (!process.env.STRIPE_SECRET_KEY) {
            throw new Error('STRIPE_SECRET_KEY environment variable is not set');
        }

        if (!DAILY_PLAN_PRICE_ID) {
            throw new Error('PRICE_ID_DAILY environment variable is not set');
        }

        log(`Using Stripe Secret Key: ${process.env.STRIPE_SECRET_KEY.substring(0, 12)}...`);
        log(`Daily Plan Price ID: ${DAILY_PLAN_PRICE_ID}`);
        log(`Dry Run Mode: ${DRY_RUN ? 'ON' : 'OFF'}`);

        // Get all subscriptions for the daily plan
        const subscriptions = await getAllSubscriptionsForPrice(DAILY_PLAN_PRICE_ID);

        log(`Found ${subscriptions.length} total subscriptions for daily plan`);

        // Filter active subscriptions
        const activeSubscriptions = subscriptions.filter(sub =>
            sub.status === 'active' || sub.status === 'trialing'
        );

        log(`Found ${activeSubscriptions.length} active subscriptions to cancel`);

        if (activeSubscriptions.length === 0) {
            log('No active daily plan subscriptions found to cancel.', 'success');
            return;
        }

        // Show summary before proceeding
        log('\n=== SUBSCRIPTION SUMMARY ===');
        activeSubscriptions.forEach((sub, index) => {
            log(`${index + 1}. ID: ${sub.id} | Status: ${sub.status} | Customer: ${sub.customer?.email || sub.customer?.id || 'Unknown'} | Created: ${new Date(sub.created * 1000).toISOString()}`);
        });
        log('============================\n');

        if (!DRY_RUN) {
            // Ask for confirmation in production
            const readline = require('readline');
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });

            const answer = await new Promise(resolve => {
                rl.question(`Are you sure you want to cancel ${activeSubscriptions.length} daily plan subscriptions? (yes/no): `, resolve);
            });

            rl.close();

            if (answer.toLowerCase() !== 'yes') {
                log('Cancellation aborted by user.');
                return;
            }
        }

        // Cancel subscriptions in batches
        const results = {
            successful: 0,
            failed: 0,
            errors: []
        };

        log(`Starting cancellation of ${activeSubscriptions.length} subscriptions...`);

        for (let i = 0; i < activeSubscriptions.length; i += BATCH_SIZE) {
            const batch = activeSubscriptions.slice(i, i + BATCH_SIZE);
            log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1} (${batch.length} subscriptions)...`);

            const batchPromises = batch.map(subscription => cancelSubscription(subscription));
            const batchResults = await Promise.all(batchPromises);

            batchResults.forEach((result, index) => {
                if (result.success) {
                    results.successful++;
                } else {
                    results.failed++;
                    results.errors.push({
                        subscriptionId: batch[index].id,
                        error: result.error
                    });
                }
            });

            // Add delay between batches to respect rate limits
            if (i + BATCH_SIZE < activeSubscriptions.length) {
                log('Waiting 1 second before next batch...');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // Final summary
        log('\n=== CANCELLATION RESULTS ===');
        log(`Total processed: ${activeSubscriptions.length}`);
        log(`Successful: ${results.successful}`, 'success');
        log(`Failed: ${results.failed}`, results.failed > 0 ? 'error' : 'info');

        if (results.errors.length > 0) {
            log('\n=== ERRORS ===');
            results.errors.forEach(error => {
                log(`Subscription ${error.subscriptionId}: ${error.error}`, 'error');
            });
        }

        log('============================\n');

        if (DRY_RUN) {
            log('This was a dry run. No actual cancellations were performed.', 'success');
            log('Set DRY_RUN = false in the script to perform actual cancellations.');
        } else {
            log('Daily plan subscription cancellation process completed.', 'success');
        }

    } catch (error) {
        log(`Fatal error in cancellation process: ${error.message}`, 'error');
        process.exit(1);
    }
}

// Alternative function to cancel by product ID instead of price ID
async function cancelAllDailySubscriptionsByProduct(productId) {
    try {
        log(`Fetching all subscriptions for product ID: ${productId}`);

        // First, get all prices for the product
        const prices = await stripe.prices.list({
            product: productId,
            limit: 100
        });

        log(`Found ${prices.data.length} prices for product ${productId}`);

        const allSubscriptions = [];

        // Get subscriptions for each price
        for (const price of prices.data) {
            log(`Fetching subscriptions for price: ${price.id}`);
            const subscriptions = await getAllSubscriptionsForPrice(price.id);
            allSubscriptions.push(...subscriptions);
        }

        log(`Found ${allSubscriptions.length} total subscriptions for product`);

        // Filter active subscriptions
        const activeSubscriptions = allSubscriptions.filter(sub =>
            sub.status === 'active' || sub.status === 'trialing'
        );

        log(`Found ${activeSubscriptions.length} active subscriptions to cancel`);

        // Use the same cancellation logic as above
        // ... (implement similar cancellation logic)

    } catch (error) {
        log(`Error canceling subscriptions by product: ${error.message}`, 'error');
        throw error;
    }
}

// Main execution
if (require.main === module) {
    // Check command line arguments
    const args = process.argv.slice(2);

    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
Usage: node cancelDailySubscriptions.js [options]

Options:
  --help, -h          Show this help message
  --dry-run          Perform a dry run (see what would be canceled without actually canceling)
  --product-id <id>  Cancel by product ID instead of price ID
  
Environment Variables Required:
  STRIPE_SECRET_KEY   Your Stripe secret key
  PRICE_ID_DAILY     The price ID for your daily plan
  
Examples:
  node cancelDailySubscriptions.js --dry-run
  node cancelDailySubscriptions.js --product-id prod_abc123
        `);
        process.exit(0);
    }

    if (args.includes('--dry-run')) {
        // Override DRY_RUN setting
        Object.defineProperty(global, 'DRY_RUN', { value: true, writable: false });
    }

    const productIdIndex = args.indexOf('--product-id');
    if (productIdIndex !== -1 && args[productIdIndex + 1]) {
        const productId = args[productIdIndex + 1];
        log(`Using product ID mode: ${productId}`);
        cancelAllDailySubscriptionsByProduct(productId);
    } else {
        // Default: cancel by price ID
        cancelAllDailySubscriptions();
    }
}

module.exports = {
    cancelAllDailySubscriptions,
    cancelAllDailySubscriptionsByProduct,
    getAllSubscriptionsForPrice
}; 