import React, { useEffect, useState } from 'react';
import { Card } from "../../components/ui/card";
import { Users, Globe, FileText, CreditCard, DollarSign, CalendarCheck, CalendarRange } from 'lucide-react';
import axios from 'axios';
import { useAdmin } from '../../contexts/AdminContext';
import { transactionService } from '@/services/transactionService';

interface DashboardStats {
    totalUsers: number;
    totalDomains: number;
    totalArticles: number;
    activeAdmins: number;
}

interface TransactionStats {
    totalTransactions: number;
    monthlyCount: number;
    monthlyAmount: number;
    yearlyCount: number;
    yearlyAmount: number;
    totalAmount: number;
}

const Dashboard = () => {
    const { token } = useAdmin();
    const [stats, setStats] = useState<DashboardStats>({
        totalUsers: 0,
        totalDomains: 0,
        totalArticles: 0,
        activeAdmins: 0
    });
    const [transactionStats, setTransactionStats] = useState<TransactionStats>({
        totalTransactions: 0,
        monthlyCount: 0,
        monthlyAmount: 0,
        yearlyCount: 0,
        yearlyAmount: 0,
        totalAmount: 0
    });
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchDashboardData = async () => {
            try {
                const headers = { Authorization: `Bearer ${token}` };

                // Fetch general stats
                const statsRes = await axios.get('http://localhost:5000/api/admin/stats', { headers });
                setStats(statsRes.data);

                // Fetch transaction data using transactionService
                const transactions = await transactionService.getAllTransactionsAdmin();

                // Calculate transaction stats
                const txStats = {
                    totalTransactions: transactions.length,
                    monthlyCount: 0,
                    monthlyAmount: 0,
                    yearlyCount: 0,
                    yearlyAmount: 0,
                    totalAmount: 0
                };

                transactions.forEach(transaction => {
                    if (transaction.status === 'completed') {
                        // Add to total amount
                        txStats.totalAmount += transaction.amount;

                        // Count by plan type
                        if (transaction.planType === 'Monthly') {
                            txStats.monthlyCount++;
                            txStats.monthlyAmount += transaction.amount;
                        } else if (transaction.planType === 'Yearly') {
                            txStats.yearlyCount++;
                            txStats.yearlyAmount += transaction.amount;
                        }
                    }
                });

                setTransactionStats(txStats);
            } catch (error) {
                console.error('Error fetching dashboard data:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchDashboardData();
    }, [token]);

    if (loading) {
        return <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>;
    }

    return (
        <div className="space-y-6">
            <h1 className="text-2xl font-semibold">Dashboard Overview</h1>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <Users className="h-8 w-8 text-gray-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Users</p>
                            <p className="text-2xl font-semibold">{stats.totalUsers}</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <Globe className="h-8 w-8 text-gray-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Domains</p>
                            <p className="text-2xl font-semibold">{stats.totalDomains}</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <FileText className="h-8 w-8 text-gray-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Articles</p>
                            <p className="text-2xl font-semibold">{stats.totalArticles}</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Transaction Stats Grid */}
            <h2 className="text-xl font-semibold mt-8">Transaction Statistics</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Total Subscriptions Card */}
                <Card className="border-0 shadow-md">
                    <div className="p-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-gray-500">Total Subscriptions</p>
                                <h3 className="text-2xl font-bold text-gray-800 mt-1">{transactionStats.totalTransactions}</h3>
                            </div>
                            <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                                <CreditCard className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                        <p className="text-sm text-green-600 font-medium mt-2">
                            {transactionService.formatCurrency(transactionStats.totalAmount)}
                        </p>
                    </div>
                </Card>

                {/* Monthly Subscriptions Card */}
                <Card className="border-0 shadow-md">
                    <div className="p-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-gray-500">Monthly Subscriptions</p>
                                <h3 className="text-2xl font-bold text-gray-800 mt-1">{transactionStats.monthlyCount}</h3>
                            </div>
                            <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                                <CalendarCheck className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                        <p className="text-sm text-green-600 font-medium mt-2">
                            {transactionService.formatCurrency(transactionStats.monthlyAmount)}
                        </p>
                    </div>
                </Card>

                {/* Yearly Subscriptions Card */}
                <Card className="border-0 shadow-md">
                    <div className="p-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-gray-500">Yearly Subscriptions</p>
                                <h3 className="text-2xl font-bold text-gray-800 mt-1">{transactionStats.yearlyCount}</h3>
                            </div>
                            <div className="h-12 w-12 bg-violet-100 rounded-full flex items-center justify-center">
                                <CalendarRange className="h-6 w-6 text-violet-600" />
                            </div>
                        </div>
                        <p className="text-sm text-green-600 font-medium mt-2">
                            {transactionService.formatCurrency(transactionStats.yearlyAmount)}
                        </p>
                    </div>
                </Card>

                {/* Total Revenue Card */}
                <Card className="border-0 shadow-md">
                    <div className="p-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                                <h3 className="text-2xl font-bold text-gray-800 mt-1">
                                    {transactionService.formatCurrency(transactionStats.totalAmount)}
                                </h3>
                            </div>
                            <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                                <DollarSign className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default Dashboard; 