const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    domainId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Domain',
        required: true
    },
    stripeSessionId: {
        type: String,
        required: true,
        index: true // Add index for better performance
    },
    stripeCustomerId: {
        type: String
    },
    stripeSubscriptionId: {
        type: String
    },
    stripePaymentIntentId: {
        type: String
    },
    stripeChargeId: {
        type: String
    },
    invoiceUrl: {
        type: String
    },
    transactionType: {
        type: String,
        enum: ['New Subscription', 'Recurring Subscription', 'Subscription Cancellation', 'Plan Change'],
        default: 'New Subscription'
    },
    amount: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        default: 'usd'
    },
    planType: {
        type: String,
        enum: ['Monthly', 'Yearly', 'Daily', 'Free'],
        required: true
    },
    status: {
        type: String,
        enum: ['pending', 'completed', 'failed', 'refunded'],
        // default: 'pending'
    },
    paymentMethod: {
        type: String,
        default: 'card'
    },
    // New subscription period fields
    subscriptionPeriod: {
        startDate: {
            type: Date
        },
        endDate: {
            type: Date
        },
        nextPaymentDate: {
            type: Date
        }
    },
    billingDetails: {
        name: String,
        email: String,
        address: {
            line1: String,
            line2: String,
            city: String,
            state: String,
            postal_code: String,
            country: String
        }
    },
    metadata: {
        type: Object,
        default: {}
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Update the updatedAt field on save
transactionSchema.pre('save', function (next) {
    this.updatedAt = Date.now();
    next();
});

// Create compound index for faster lookups
transactionSchema.index({ userId: 1, status: 1 });
transactionSchema.index({ stripeSubscriptionId: 1 });

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction; 