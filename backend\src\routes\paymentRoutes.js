const express = require('express');
const router = express.Router();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY || 'sk_test_dummy');
const Domain = require('../models/Domain');
const Plan = require('../models/Plan');
const Transaction = require('../models/Transaction');
const mongoose = require('mongoose');
const { protect } = require('../middleware/auth');
const fs = require('fs');
const path = require('path');
const emailService = require('../utils/emailService');
const User = require('../models/User');
const nodemailer = require('nodemailer');

// Stripe price IDs for subscription plans
const PRICE_ID_MONTHLY = process.env.PRICE_ID_MONTHLY
const PRICE_ID_YEARLY = process.env.PRICE_ID_YEARLY
const PRICE_ID_DAILY = process.env.PRICE_ID_DAILY

// Webhook logging function
function logWebhookEvent(eventType, data, isError = false) {
    try {
        const timestamp = new Date().toISOString();
        const logDir = path.join(__dirname, '..', '..', 'logs');

        // Create logs directory if it doesn't exist
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }

        // Format for console log
        const logPrefix = isError ? '❌ ERROR' : '✅ EVENT';
        console.log(`${logPrefix} [${timestamp}] ${eventType}: ${JSON.stringify(data, null, 2)}`);

        // Write to log file
        const logFile = path.join(logDir, `webhook-${new Date().toISOString().split('T')[0]}.log`);
        const logEntry = `[${timestamp}] ${isError ? 'ERROR' : 'EVENT'} ${eventType}: ${JSON.stringify(data)}\n`;

        fs.appendFileSync(logFile, logEntry);
    } catch (err) {
        console.error('❌ Error writing to webhook log:', err);
    }
}

// Helper function to update domain subscription and plan when a transaction is completed
async function updateSubscriptionAndPlan(transaction) {
    if (!transaction || transaction.status !== 'completed') {
        return false;
    }

    try {
        // Update domain subscription
        const domain = await Domain.findById(transaction.domainId);
        if (!domain) {
            console.log(`Domain not found for transaction ${transaction._id}`);
            return false;
        }

        await Domain.findByIdAndUpdate(transaction.domainId, {
            subscription: {
                active: true,
                planType: transaction.planType,
                stripeCustomerId: transaction.stripeCustomerId || domain.subscription?.stripeCustomerId,
                stripeSubscriptionId: transaction.stripeSubscriptionId || domain.subscription?.stripeSubscriptionId,
                updatedAt: new Date(),
                credits: transaction.planType === 'Daily' ? 1 :
                    transaction.planType === 'Monthly' ? 30 :
                        transaction.planType === 'Yearly' ? 365 : 0,
                canceledAt: domain.subscription?.canceledAt,
                expiresAt: domain.subscription?.expiresAt
            }
        });
        console.log(`Domain ${transaction.domainId} subscription updated for transaction ${transaction._id}`);

        // Update user plan
        let planFeatures;
        let planType;
        let startDate = new Date();
        let endDate;
        let nextPaymentDate;

        // Use subscription period dates from transaction if available
        if (transaction.subscriptionPeriod && transaction.subscriptionPeriod.startDate) {
            startDate = transaction.subscriptionPeriod.startDate;
        }

        if (transaction.subscriptionPeriod && transaction.subscriptionPeriod.endDate) {
            endDate = transaction.subscriptionPeriod.endDate;
            nextPaymentDate = transaction.subscriptionPeriod.nextPaymentDate || endDate;
        } else {
            // Fallback to calculated dates if subscription period is not available
            if (transaction.planType === 'Monthly') {
                endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
            } else if (transaction.planType === 'Yearly') {
                endDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 365 days
            } else if (transaction.planType === 'Daily') {
                endDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day
            } else {
                endDate = null;
            }
            nextPaymentDate = endDate;
        }

        if (transaction.planType === 'Monthly') {
            planFeatures = { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true };
            planType = 'Pro';
        } else if (transaction.planType === 'Yearly') {
            planFeatures = { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true };
            planType = 'Enterprise';
        } else if (transaction.planType === 'Daily') {
            planFeatures = { maxDomains: 2, maxArticlesPerMonth: 5, customBranding: true };
            planType = 'Basic';
        } else {
            planFeatures = { maxDomains: 1, maxArticlesPerMonth: 10, customBranding: false };
            planType = 'Free';
        }

        await Plan.findOneAndUpdate(
            { userId: transaction.userId },
            {
                type: planType,
                displayType: transaction.planType,
                features: planFeatures,
                startDate: startDate,
                endDate: endDate,
                nextPaymentDate: nextPaymentDate,
                status: 'active'
            },
            { upsert: true }
        );
        console.log(`Plan updated for user ${transaction.userId} for transaction ${transaction._id}`);

        return true;
    } catch (error) {
        console.error(`Error updating subscription and plan for transaction ${transaction._id}:`, error);
        return false;
    }
}

// Create a checkout session for subscription
router.post('/create-checkout-session', protect, async (req, res) => {
    try {
        const { planType, domainId } = req.body;

        if (!domainId) {
            return res.status(400).json({ error: 'Domain ID is required' });
        }

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Set price based on plan type
        let priceId;
        if (planType === 'Monthly') {
            priceId = PRICE_ID_MONTHLY;
        } else if (planType === 'Yearly') {
            priceId = PRICE_ID_YEARLY;
        } else if (planType === 'Daily') {
            priceId = PRICE_ID_DAILY;
        } else {
            return res.status(400).json({ error: 'Invalid plan type' });
        }

        // Create a checkout session
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [
                {
                    price: priceId,
                    quantity: 1,
                },
            ],
            mode: 'subscription',
            success_url: `${process.env.FRONTEND_URL}/dashboard/domain/${domainId}?payment_success=true&plan_type=${planType}`,
            cancel_url: `${process.env.FRONTEND_URL}/dashboard/plans`,
            client_reference_id: domainId,
            customer_email: req.user.email, // Pre-fill the user's email in the checkout form
            metadata: {
                userId: req.user.id,
                domainId: domainId,
                planType: planType
            },
            expand: ['subscription', 'subscription.latest_invoice'] // Fix: Only expand subscription and latest_invoice
        });

        // Log subscription information for debugging
        console.log(`Checkout session created: ${session.id}`);
        if (session.subscription) {
            console.log(`Subscription info in session: ${typeof session.subscription === 'object' ?
                JSON.stringify({ id: session.subscription.id, status: session.subscription.status }) :
                session.subscription}`);
        } else {
            console.log('No subscription info in session');
        }

        // Try to get invoice URL directly from the expanded session data
        let invoiceUrl = null;
        if (session.subscription &&
            session.subscription.latest_invoice &&
            typeof session.subscription.latest_invoice === 'object' &&
            session.subscription.latest_invoice.hosted_invoice_url) {
            invoiceUrl = session.subscription.latest_invoice.hosted_invoice_url;
            console.log('Invoice URL found directly in session:', invoiceUrl);
        }

        // Determine amount based on plan type
        let amount;
        if (planType === 'Monthly') {
            amount = 59.99;
        } else if (planType === 'Yearly') {
            amount = 159.99;
        } else if (planType === 'Daily') {
            amount = 9.99;
        } else {
            amount = 0; // Free plan
        }

        // Create a pending transaction record
        const transaction = new Transaction({
            userId: req.user.id,
            domainId: domainId,
            stripeSessionId: session.id,
            stripeSubscriptionId: session.subscription ? (typeof session.subscription === 'object' ? session.subscription.id : session.subscription) : null,
            planType: planType,
            status: 'pending',
            amount: amount,
            currency: 'usd',
            transactionType: 'New Subscription',
            invoiceUrl: invoiceUrl, // Set invoice URL if found
            metadata: {
                sessionUrl: session.url,
                createdAt: new Date(),
                invoiceFound: !!invoiceUrl,
                hasSubscriptionId: !!session.subscription
            }
        });

        await transaction.save();
        console.log('Pending transaction created:', transaction._id);

        // If invoice URL wasn't found in the expanded session, try other methods
        if (!invoiceUrl && session.subscription) {
            try {
                console.log('Subscription created, attempting to fetch invoice URL');

                // Method 1: Get via subscription with expanded latest_invoice
                const subscription = await stripe.subscriptions.retrieve(session.subscription, {
                    expand: ['latest_invoice']
                });

                if (subscription.latest_invoice) {
                    const invoice = typeof subscription.latest_invoice === 'string'
                        ? await stripe.invoices.retrieve(subscription.latest_invoice)
                        : subscription.latest_invoice;

                    if (invoice.hosted_invoice_url) {
                        invoiceUrl = invoice.hosted_invoice_url;
                        console.log('Invoice URL found via subscription:', invoiceUrl);

                        // Update the transaction with the invoice URL
                        await Transaction.findByIdAndUpdate(transaction._id, {
                            invoiceUrl: invoiceUrl,
                            metadata: {
                                ...transaction.metadata,
                                invoiceFound: true,
                                invoiceUpdatedAt: new Date()
                            }
                        });
                        console.log('Transaction updated with invoice URL');
                    }
                }
            } catch (error) {
                console.error('Error fetching invoice URL:', error);
                // Continue even if invoice URL fetch fails
            }
        }

        res.json({ sessionId: session.id, url: session.url });
    } catch (error) {
        console.error('Error creating checkout session:', error);

        // Log specific details for expansion errors
        if (error.type === 'StripeInvalidRequestError' &&
            error.message &&
            error.message.includes('cannot be expanded')) {
            console.error('Stripe expansion error detected. Please check the expand parameters.');
        }

        res.status(500).json({ error: error.message });
    }
});

// Create or update a plan for a user
router.post('/create-plan', protect, async (req, res) => {
    try {
        const { planType, domainId } = req.body;

        if (!planType || !domainId) {
            return res.status(400).json({ error: 'Plan type and domain ID are required' });
        }

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Set plan features based on plan type
        const planFeatures = planType === 'Monthly'
            ? { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true }
            : planType === 'Yearly'
                ? { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true }
                : planType === 'Daily'
                    ? { maxDomains: 2, maxArticlesPerMonth: 5, customBranding: true }
                    : { maxDomains: 1, maxArticlesPerMonth: 10, customBranding: false };

        // Set plan type for database
        const dbPlanType = planType === 'Monthly'
            ? 'Pro'
            : planType === 'Yearly'
                ? 'Enterprise'
                : planType === 'Daily'
                    ? 'Basic'
                    : 'Free';

        // Calculate end date based on plan type
        const endDate = planType === 'Monthly'
            ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days
            : planType === 'Yearly'
                ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)  // 365 days
                : planType === 'Daily'
                    ? new Date(Date.now() + 24 * 60 * 60 * 1000)  // 1 day
                    : null;  // No end date for free plan

        // Create or update the plan
        const plan = await Plan.findOneAndUpdate(
            { userId: req.user.id },
            {
                userId: req.user.id,
                type: dbPlanType,
                displayType: planType, // Store the user-friendly plan type
                features: planFeatures,
                startDate: new Date(), // Always set a valid start date
                endDate: endDate,
                status: 'active'
            },
            { upsert: true, new: true }
        );

        // For free plan, reset subscription to inactive
        if (planType === 'Free') {
            await Domain.findByIdAndUpdate(domainId, {
                subscription: {
                    active: false,
                    planType: 'Free',
                    credits: 0,
                    updatedAt: new Date(), // Ensure we have a valid date
                    stripeCustomerId: domain.subscription?.stripeCustomerId, // Preserve customer ID
                    stripeSubscriptionId: domain.subscription?.stripeSubscriptionId // Preserve subscription ID
                }
            });
        } else {
            // For paid plans, update the credits based on plan type
            const credits = planType === 'Daily' ? 1 :
                planType === 'Monthly' ? 30 :
                    planType === 'Yearly' ? 365 : 0;

            // Use $set to update specific fields while preserving other subscription data
            await Domain.findByIdAndUpdate(domainId, {
                $set: {
                    'subscription.credits': credits,
                    'subscription.planType': planType,
                    'subscription.updatedAt': new Date()
                }
            });
        }
        // For paid plans, only update subscription if payment is completed
        // This endpoint should not update subscription for paid plans
        // That should happen in the payment webhook after payment is confirmed

        res.json({ success: true, plan });
    } catch (error) {
        console.error('Error creating/updating plan:', error);
        res.status(500).json({ error: error.message });
    }
});

// Verify payment status directly from Stripe
router.get('/verify-payment/:sessionId', protect, async (req, res) => {
    try {
        const { sessionId } = req.params;

        // Get the session from Stripe
        const session = await stripe.checkout.sessions.retrieve(sessionId, {
            expand: ['payment_intent']
        });

        // Determine status based on Stripe session
        let status = 'pending';

        if (session.payment_status === 'paid') {
            status = 'completed';
        } else if (session.payment_status === 'unpaid') {
            status = 'failed';
        } else if (session.status === 'expired') {
            status = 'failed';
        }

        // For prorated payments with mode=payment, we need to check the payment status directly
        // as these are one-time payments, not subscriptions
        if (session.mode === 'payment' && session.metadata?.isProrated === 'true' && session.status === 'complete') {
            status = 'completed';
        }

        // Get payment details if available
        let paymentIntentId = session.payment_intent;
        let chargeId = null;

        if (paymentIntentId) {
            try {
                // If payment_intent is a string (ID), retrieve it
                let paymentIntent;
                if (typeof paymentIntentId === 'string') {
                    paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId, {
                        expand: ['charges']
                    });
                } else {
                    // If payment_intent is already expanded, use it directly
                    paymentIntent = session.payment_intent;
                    // Ensure paymentIntentId is stored as a string (ID)
                    paymentIntentId = paymentIntent.id;
                }

                // Get the latest charge ID if available
                if (paymentIntent.latest_charge) {
                    chargeId = paymentIntent.latest_charge;
                } else if (paymentIntent.charges && paymentIntent.charges.data && paymentIntent.charges.data.length > 0) {
                    chargeId = paymentIntent.charges.data[0].id;
                }

                console.log(`Retrieved payment details - PaymentIntent: ${paymentIntentId}, Charge: ${chargeId}`);

                // Ensure paymentIntentId is a string (ID)
                if (typeof paymentIntentId !== 'string') {
                    paymentIntentId = paymentIntent.id;
                }
            } catch (error) {
                console.error('Error retrieving payment intent:', error);
            }
        }

        // Find the transaction
        const transaction = await Transaction.findOne({ stripeSessionId: sessionId });

        // Update transaction in our database
        if (transaction && (status !== 'pending' || paymentIntentId || chargeId)) {
            // Check if this is a prorated payment
            const isProrated = transaction.metadata?.isProrated === true ||
                transaction.metadata?.isProrated === 'true' ||
                session.metadata?.isProrated === 'true';

            // For new subscriptions, make sure we try to get payment intent and charge ID
            if (!isProrated && !transaction.stripePaymentIntentId && session.mode === 'subscription' && paymentIntentId) {
                console.log(`Updating new subscription transaction with payment details - PaymentIntent: ${paymentIntentId}, Charge: ${chargeId}`);
            }

            // Update transaction with payment details
            await Transaction.findOneAndUpdate(
                { stripeSessionId: sessionId },
                {
                    status,
                    stripeCustomerId: session.customer || transaction.stripeCustomerId,
                    stripeSubscriptionId: session.subscription || transaction.stripeSubscriptionId,
                    stripePaymentIntentId: paymentIntentId || transaction.stripePaymentIntentId,
                    stripeChargeId: chargeId || transaction.stripeChargeId,
                    metadata: {
                        ...transaction.metadata,
                        verifiedAt: new Date(),
                        stripeStatus: session.status,
                        stripePaymentStatus: session.payment_status,
                        paymentIntent: paymentIntentId ? (typeof paymentIntentId === 'object' ? paymentIntentId.id : paymentIntentId) : null,
                        chargeId: chargeId
                    }
                }
            );

            // If status is completed, update the plan and domain subscription
            if (status === 'completed' && transaction.domainId) {
                try {
                    // Check if this is a prorated payment
                    if (isProrated) {
                        console.log(`Processing completed prorated payment for transaction ${transaction._id}`);

                        // Get the domain
                        const domain = await Domain.findById(transaction.domainId);
                        if (!domain) {
                            console.log(`Domain not found for transaction ${transaction._id}`);
                            return res.json({
                                status,
                                stripeStatus: session.status,
                                stripePaymentStatus: session.payment_status,
                                paymentIntentId,
                                chargeId
                            });
                        }

                        // Get the current subscription from Stripe
                        const currentSubscriptionId = domain.subscription?.stripeSubscriptionId;
                        console.log("currentSubscriptionId", currentSubscriptionId);
                        if (!currentSubscriptionId) {
                            console.log(`No subscription ID found for domain ${transaction.domainId}`);
                            return res.json({
                                status,
                                stripeStatus: session.status,
                                stripePaymentStatus: session.payment_status,
                                paymentIntentId,
                                chargeId
                            });
                        }

                        try {
                            // Update the existing subscription instead of canceling and creating new one
                            const newPlanType = transaction.planType;
                            let priceId;
                            if (newPlanType === 'Monthly') {
                                priceId = PRICE_ID_MONTHLY;
                            } else if (newPlanType === 'Yearly') {
                                priceId = PRICE_ID_YEARLY;
                            } else if (newPlanType === 'Daily') {
                                priceId = PRICE_ID_DAILY;
                            } else {
                                console.error(`Invalid plan type for subscription update: ${newPlanType}`);
                                throw new Error(`Invalid plan type: ${newPlanType}`);
                            }

                            // Get current subscription details for debugging
                            const currentSubscription = await stripe.subscriptions.retrieve(currentSubscriptionId);
                            console.log(`[VERIFY] Current subscription before update: Plan=${currentSubscription.items.data[0].price.id}, Status=${currentSubscription.status}`);

                            // Update the existing subscription with the new plan
                            const updatedSubscription = await stripe.subscriptions.update(currentSubscriptionId, {
                                items: [
                                    {
                                        id: currentSubscription.items.data[0].id,
                                        price: priceId,
                                    },
                                ],
                                metadata: {
                                    userId: transaction.userId,
                                    domainId: transaction.domainId,
                                    planType: newPlanType,
                                    plan: newPlanType, // Add this for Stripe dashboard display
                                    isProrated: 'true',
                                    previousPlanType: domain.subscription.planType,
                                    updatedAt: new Date().toISOString()
                                },
                                proration_behavior: 'create_prorations'
                            });

                            console.log(`[VERIFY] Updated subscription: Plan=${updatedSubscription.items.data[0].price.id}, Status=${updatedSubscription.status}`);
                            console.log(`Updated subscription ${currentSubscriptionId} with new plan ${newPlanType} for domain ${transaction.domainId}`);

                            // Calculate credits based on the new plan type
                            const newPlanCredits = newPlanType === 'Daily' ? 1 :
                                newPlanType === 'Monthly' ? 30 :
                                    newPlanType === 'Yearly' ? 365 : 0;

                            // Update domain subscription (keeping the same subscription ID)
                            await Domain.findByIdAndUpdate(transaction.domainId, {
                                subscription: {
                                    active: true,
                                    planType: newPlanType,
                                    credits: newPlanCredits,
                                    stripeCustomerId: domain.subscription?.stripeCustomerId || session.customer,
                                    stripeSubscriptionId: currentSubscriptionId, // Keep the same subscription ID
                                    updatedAt: new Date()
                                }
                            });
                            console.log(`Domain ${transaction.domainId} subscription updated after prorated payment verification`);
                            console.log(`Credits updated from previous plan to ${newPlanCredits} credits for ${newPlanType} plan`);

                            // Update user plan
                            const planFeatures = newPlanType === 'Monthly'
                                ? { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true }
                                : { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true };

                            await Plan.findOneAndUpdate(
                                { userId: transaction.userId },
                                {
                                    type: newPlanType === 'Monthly' ? 'Pro' : 'Enterprise',
                                    displayType: newPlanType,
                                    features: planFeatures,
                                    startDate: new Date(),
                                    endDate: newPlanType === 'Monthly'
                                        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days
                                        : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),  // 365 days
                                    status: 'active'
                                },
                                { upsert: true }
                            );

                            console.log(`Plan updated for user ${transaction.userId} after prorated payment verification`);

                            // Update the transaction to reflect that subscription ID remains same
                            await Transaction.findOneAndUpdate(
                                { stripeSessionId: sessionId },
                                {
                                    metadata: {
                                        ...transaction.metadata,
                                        subscriptionUpdated: true,
                                        subscriptionUpdatedAt: new Date(),
                                        maintainedSameSubscriptionId: true
                                    }
                                }
                            );
                            console.log(`Transaction ${transaction._id} updated - subscription ID maintained: ${currentSubscriptionId}`);
                        } catch (error) {
                            console.error(`Error updating subscription during verification for transaction ${transaction._id}:`, error);

                            // Fallback: try the old cancel+create method
                            try {
                                console.log('Falling back to cancel+create method for verification');
                                await stripe.subscriptions.cancel(currentSubscriptionId);
                                console.log(`Cancelled old subscription ${currentSubscriptionId} as fallback`);

                                const newSubscription = await stripe.subscriptions.create({
                                    customer: domain.subscription?.stripeCustomerId || session.customer,
                                    items: [{ price: newPlanType === 'Monthly' ? PRICE_ID_MONTHLY : newPlanType === 'Yearly' ? PRICE_ID_YEARLY : PRICE_ID_DAILY }],
                                    metadata: {
                                        userId: transaction.userId,
                                        domainId: transaction.domainId,
                                        planType: newPlanType
                                    }
                                });

                                // Update domain with new subscription ID
                                await Domain.findByIdAndUpdate(transaction.domainId, {
                                    subscription: {
                                        active: true,
                                        planType: newPlanType,
                                        stripeCustomerId: domain.subscription?.stripeCustomerId || session.customer,
                                        stripeSubscriptionId: newSubscription.id,
                                        updatedAt: new Date()
                                    }
                                });

                                // Update transaction with new subscription ID
                                await Transaction.findOneAndUpdate(
                                    { stripeSessionId: sessionId },
                                    {
                                        stripeSubscriptionId: newSubscription.id,
                                        metadata: {
                                            ...transaction.metadata,
                                            newSubscriptionId: newSubscription.id,
                                            subscriptionUpdatedAt: new Date(),
                                            fallbackUsed: true
                                        }
                                    }
                                );
                                console.log(`Fallback completed - new subscription ID: ${newSubscription.id}`);
                            } catch (fallbackError) {
                                console.error(`Fallback also failed for verification transaction ${transaction._id}:`, fallbackError);
                            }
                        }
                    } else {
                        // For regular (non-prorated) payments, update as normal
                        const domain = await Domain.findById(transaction.domainId);
                        if (domain) {
                            // Update domain subscription
                            await Domain.findByIdAndUpdate(transaction.domainId, {
                                subscription: {
                                    active: true,
                                    planType: transaction.planType,
                                    stripeCustomerId: domain.subscription?.stripeCustomerId || session.customer,
                                    stripeSubscriptionId: domain.subscription?.stripeSubscriptionId || session.subscription,
                                    updatedAt: new Date()
                                }
                            });
                            console.log(`Domain ${transaction.domainId} subscription updated after payment verification`);

                            // Update user plan
                            const planFeatures = transaction.planType === 'Monthly'
                                ? { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true }
                                : { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true };

                            await Plan.findOneAndUpdate(
                                { userId: transaction.userId },
                                {
                                    type: transaction.planType === 'Monthly' ? 'Pro' : 'Enterprise',
                                    displayType: transaction.planType,
                                    features: planFeatures,
                                    startDate: new Date(),
                                    endDate: transaction.planType === 'Monthly'
                                        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days
                                        : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),  // 365 days
                                    status: 'active'
                                },
                                { upsert: true }
                            );

                            console.log(`Plan updated automatically after verifying payment for domain ${transaction.domainId}`);
                        }
                    }
                } catch (error) {
                    console.error('Error updating plan after verifying payment:', error);
                }
            }
        }

        res.json({
            status,
            stripeStatus: session.status,
            stripePaymentStatus: session.payment_status,
            paymentIntentId,
            chargeId
        });
    } catch (error) {
        console.error('Error verifying payment status:', error);
        res.status(500).json({ error: error.message });
    }
});

// Sync all transaction statuses with Stripe
router.post('/sync-transactions', protect, async (req, res) => {
    try {
        // Get all pending transactions for the user
        const pendingTransactions = await Transaction.find({
            userId: req.user.id,
            status: { $in: ['pending'] }
        });

        if (pendingTransactions.length === 0) {
            // If no pending transactions, just return all transactions
            const allTransactions = await Transaction.find({ userId: req.user.id })
                .populate('domainId')
                .sort({ createdAt: -1 });

            return res.json(allTransactions);
        }

        console.log(`Found ${pendingTransactions.length} pending transactions to sync`);

        // Update each pending transaction
        const updatedTransactions = await Promise.all(
            pendingTransactions.map(async (transaction) => {
                try {
                    // Get the session from Stripe
                    const session = await stripe.checkout.sessions.retrieve(transaction.stripeSessionId, {
                        expand: ['payment_intent']
                    });

                    // Determine status based on Stripe session
                    let status = 'pending';

                    if (session.payment_status === 'paid') {
                        status = 'completed';
                    } else if (session.payment_status === 'unpaid') {
                        status = 'failed';
                    } else if (session.status === 'expired') {
                        status = 'failed';
                    }

                    // For prorated payments with mode=payment, we need to check the payment status directly
                    // as these are one-time payments, not subscriptions
                    if (session.mode === 'payment' && session.metadata?.isProrated === 'true' && session.status === 'complete') {
                        status = 'completed';
                    }

                    // Get payment details if available
                    let paymentIntentId = session.payment_intent;
                    let chargeId = null;
                    let invoiceUrl = null;

                    if (paymentIntentId) {
                        try {
                            // If payment_intent is a string (ID), retrieve it
                            let paymentIntent;
                            if (typeof paymentIntentId === 'string') {
                                paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId, {
                                    expand: ['charges']
                                });
                            } else {
                                // If payment_intent is already expanded, use it directly
                                paymentIntent = session.payment_intent;
                                // Ensure paymentIntentId is stored as a string (ID)
                                paymentIntentId = paymentIntent.id;
                            }

                            // Get the latest charge ID if available
                            if (paymentIntent.latest_charge) {
                                chargeId = paymentIntent.latest_charge;
                            } else if (paymentIntent.charges && paymentIntent.charges.data && paymentIntent.charges.data.length > 0) {
                                chargeId = paymentIntent.charges.data[0].id;
                            }

                            // Check if this payment intent is associated with an invoice
                            if (paymentIntent.invoice) {
                                const invoice = await stripe.invoices.retrieve(paymentIntent.invoice);
                                if (invoice.hosted_invoice_url) {
                                    invoiceUrl = invoice.hosted_invoice_url;
                                }
                            }
                        } catch (error) {
                            console.error('Error retrieving payment intent:', error);
                        }
                    }

                    // If there's a subscription, try to get the latest invoice
                    if (!invoiceUrl && session.subscription) {
                        try {
                            const subscription = await stripe.subscriptions.retrieve(session.subscription);
                            if (subscription.latest_invoice) {
                                const invoice = await stripe.invoices.retrieve(subscription.latest_invoice);
                                if (invoice.hosted_invoice_url) {
                                    invoiceUrl = invoice.hosted_invoice_url;
                                }
                            }
                        } catch (error) {
                            console.error('Error retrieving invoice:', error);
                        }
                    }

                    // Only update if status changed or payment details retrieved
                    if (status !== transaction.status || paymentIntentId || chargeId || invoiceUrl) {
                        const previousStatus = transaction.status;
                        transaction.status = status;
                        transaction.stripeCustomerId = session.customer || transaction.stripeCustomerId;
                        transaction.stripeSubscriptionId = session.subscription || transaction.stripeSubscriptionId;

                        // For new subscriptions, make sure we try to get payment intent and charge ID
                        if (!transaction.metadata?.isProrated && !transaction.stripePaymentIntentId && session.mode === 'subscription' && paymentIntentId) {
                            console.log(`Updating new subscription transaction with payment details - PaymentIntent: ${paymentIntentId}, Charge: ${chargeId}`);
                        }

                        transaction.stripePaymentIntentId = paymentIntentId || transaction.stripePaymentIntentId;
                        transaction.stripeChargeId = chargeId || transaction.stripeChargeId;
                        if (invoiceUrl) {
                            transaction.invoiceUrl = invoiceUrl;
                        }
                        transaction.metadata = {
                            ...transaction.metadata,
                            verifiedAt: new Date(),
                            stripeStatus: session.status,
                            stripePaymentStatus: session.payment_status,
                            paymentIntent: paymentIntentId ? (typeof paymentIntentId === 'object' ? paymentIntentId.id : paymentIntentId) : null,
                            chargeId: chargeId
                        };

                        await transaction.save();

                        // If transaction status changed from pending to completed, update domain subscription and plan
                        if (previousStatus === 'pending' && status === 'completed') {
                            try {
                                // Check if this is a prorated payment
                                const isProrated = transaction.metadata?.isProrated === true ||
                                    transaction.metadata?.isProrated === 'true' ||
                                    session.metadata?.isProrated === 'true';

                                if (isProrated) {
                                    console.log(`Processing completed prorated payment for transaction ${transaction._id} during sync`);

                                    // Get the domain
                                    const domain = await Domain.findById(transaction.domainId);
                                    if (!domain) {
                                        console.log(`Domain not found for transaction ${transaction._id}`);
                                        return transaction; // Skip further processing and return the transaction
                                    }

                                    // Get the current subscription from Stripe
                                    const currentSubscriptionId = domain.subscription?.stripeSubscriptionId;
                                    if (!currentSubscriptionId) {
                                        console.log(`No subscription ID found for domain ${transaction.domainId}`);
                                        return transaction; // Skip further processing and return the transaction
                                    }

                                    try {
                                        // Update the existing subscription instead of canceling and creating new one
                                        const newPlanType = transaction.planType;
                                        let priceId;
                                        if (newPlanType === 'Monthly') {
                                            priceId = PRICE_ID_MONTHLY;
                                        } else if (newPlanType === 'Yearly') {
                                            priceId = PRICE_ID_YEARLY;
                                        } else if (newPlanType === 'Daily') {
                                            priceId = PRICE_ID_DAILY;
                                        } else {
                                            console.error(`Invalid plan type for sync subscription update: ${newPlanType}`);
                                            throw new Error(`Invalid plan type: ${newPlanType}`);
                                        }

                                        // Update the existing subscription with the new plan
                                        const updatedSubscription = await stripe.subscriptions.update(currentSubscriptionId, {
                                            items: [
                                                {
                                                    id: (await stripe.subscriptions.retrieve(currentSubscriptionId)).items.data[0].id,
                                                    price: priceId,
                                                },
                                            ],
                                            metadata: {
                                                userId: transaction.userId,
                                                domainId: transaction.domainId,
                                                planType: newPlanType,
                                                plan: newPlanType, // Add this for Stripe dashboard display
                                                isProrated: 'true',
                                                previousPlanType: domain.subscription.planType,
                                                updatedAt: new Date().toISOString()
                                            },
                                            proration_behavior: 'create_prorations'
                                        });

                                        console.log(`Updated subscription ${currentSubscriptionId} with new plan ${newPlanType} for domain ${transaction.domainId} during sync`);

                                        // Calculate credits based on the new plan type
                                        const newPlanCredits = newPlanType === 'Daily' ? 1 :
                                            newPlanType === 'Monthly' ? 30 :
                                                newPlanType === 'Yearly' ? 365 : 0;

                                        // Update domain subscription (keeping the same subscription ID)
                                        await Domain.findByIdAndUpdate(transaction.domainId, {
                                            subscription: {
                                                active: true,
                                                planType: newPlanType,
                                                credits: newPlanCredits,
                                                stripeCustomerId: domain.subscription?.stripeCustomerId || transaction.stripeCustomerId,
                                                stripeSubscriptionId: currentSubscriptionId, // Keep the same subscription ID
                                                updatedAt: new Date()
                                            }
                                        });
                                        console.log(`Domain ${transaction.domainId} subscription updated after prorated payment sync`);
                                        console.log(`Credits updated from previous plan to ${newPlanCredits} credits for ${newPlanType} plan`);

                                        // Update user plan
                                        const planFeatures = newPlanType === 'Monthly'
                                            ? { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true }
                                            : { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true };

                                        await Plan.findOneAndUpdate(
                                            { userId: transaction.userId },
                                            {
                                                type: newPlanType === 'Monthly' ? 'Pro' : 'Enterprise',
                                                displayType: newPlanType,
                                                features: planFeatures,
                                                startDate: new Date(),
                                                endDate: newPlanType === 'Monthly'
                                                    ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days
                                                    : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),  // 365 days
                                                status: 'active'
                                            },
                                            { upsert: true }
                                        );

                                        console.log(`Plan updated for user ${transaction.userId} after prorated payment sync`);

                                        // Update the transaction to reflect that subscription ID remains same
                                        transaction.metadata = {
                                            ...transaction.metadata,
                                            subscriptionUpdated: true,
                                            subscriptionUpdatedAt: new Date(),
                                            maintainedSameSubscriptionId: true
                                        };
                                        await transaction.save();
                                        console.log(`Transaction ${transaction._id} updated - subscription ID maintained during sync: ${currentSubscriptionId}`);
                                    } catch (error) {
                                        console.error(`Error updating subscription during sync for transaction ${transaction._id}:`, error);

                                        // Fallback: try the old cancel+create method
                                        try {
                                            console.log('Falling back to cancel+create method for sync');
                                            await stripe.subscriptions.cancel(currentSubscriptionId);
                                            console.log(`Cancelled old subscription ${currentSubscriptionId} as fallback during sync`);

                                            const newSubscription = await stripe.subscriptions.create({
                                                customer: domain.subscription?.stripeCustomerId || transaction.stripeCustomerId,
                                                items: [{ price: newPlanType === 'Monthly' ? PRICE_ID_MONTHLY : newPlanType === 'Yearly' ? PRICE_ID_YEARLY : PRICE_ID_DAILY }],
                                                metadata: {
                                                    userId: transaction.userId,
                                                    domainId: transaction.domainId,
                                                    planType: newPlanType
                                                }
                                            });

                                            // Update domain with new subscription ID
                                            await Domain.findByIdAndUpdate(transaction.domainId, {
                                                subscription: {
                                                    active: true,
                                                    planType: newPlanType,
                                                    stripeCustomerId: domain.subscription?.stripeCustomerId || transaction.stripeCustomerId,
                                                    stripeSubscriptionId: newSubscription.id,
                                                    updatedAt: new Date()
                                                }
                                            });

                                            // Update transaction with new subscription ID
                                            transaction.stripeSubscriptionId = newSubscription.id;
                                            transaction.metadata = {
                                                ...transaction.metadata,
                                                newSubscriptionId: newSubscription.id,
                                                subscriptionUpdatedAt: new Date(),
                                                fallbackUsed: true
                                            };
                                            await transaction.save();
                                            console.log(`Fallback completed during sync - new subscription ID: ${newSubscription.id}`);
                                        } catch (fallbackError) {
                                            console.error(`Fallback also failed for sync transaction ${transaction._id}:`, fallbackError);
                                        }
                                    }
                                } else {
                                    // For regular (non-prorated) payments, update as normal
                                    // Update domain subscription
                                    const domain = await Domain.findById(transaction.domainId);
                                    if (domain) {
                                        await Domain.findByIdAndUpdate(transaction.domainId, {
                                            subscription: {
                                                active: true,
                                                planType: transaction.planType,
                                                stripeCustomerId: transaction.stripeCustomerId || domain.subscription?.stripeCustomerId,
                                                stripeSubscriptionId: transaction.stripeSubscriptionId || domain.subscription?.stripeSubscriptionId,
                                                updatedAt: new Date()
                                            }
                                        });
                                        console.log(`Domain ${transaction.domainId} subscription updated after sync`);

                                        // Update user plan
                                        const planFeatures = transaction.planType === 'Monthly'
                                            ? { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true }
                                            : { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true };

                                        await Plan.findOneAndUpdate(
                                            { userId: transaction.userId },
                                            {
                                                type: transaction.planType === 'Monthly' ? 'Pro' : 'Enterprise',
                                                displayType: transaction.planType,
                                                features: planFeatures,
                                                startDate: new Date(),
                                                endDate: transaction.planType === 'Monthly'
                                                    ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days
                                                    : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),  // 365 days
                                                status: 'active'
                                            },
                                            { upsert: true }
                                        );
                                        console.log(`Plan updated for user ${transaction.userId} after sync`);
                                    }
                                }
                            } catch (error) {
                                console.error(`Error updating subscription and plan after sync for transaction ${transaction._id}:`, error);
                            }
                        }
                    }

                    return transaction;
                } catch (error) {
                    console.error(`Error syncing transaction ${transaction._id}:`, error);
                    return transaction;
                }
            })
        );

        // Get all transactions for the user (including newly updated ones)
        const allTransactions = await Transaction.find({ userId: req.user.id })
            .populate('domainId')
            .sort({ createdAt: -1 });

        // For completed transactions without invoice URLs, try to fetch them
        const transactionsWithoutInvoice = allTransactions.filter(
            t => t.status === 'completed' && !t.invoiceUrl && (t.stripeSubscriptionId || t.stripePaymentIntentId)
        );

        if (transactionsWithoutInvoice.length > 0) {
            console.log(`Found ${transactionsWithoutInvoice.length} completed transactions without invoice URLs`);

            await Promise.all(
                transactionsWithoutInvoice.map(async (transaction) => {
                    let invoiceUrl = null;

                    // Try to get invoice URL from subscription
                    if (transaction.stripeSubscriptionId) {
                        try {
                            const subscription = await stripe.subscriptions.retrieve(transaction.stripeSubscriptionId, {
                                expand: ['latest_invoice']
                            });

                            if (subscription.latest_invoice) {
                                const invoice = typeof subscription.latest_invoice === 'string'
                                    ? await stripe.invoices.retrieve(subscription.latest_invoice)
                                    : subscription.latest_invoice;

                                if (invoice.hosted_invoice_url) {
                                    invoiceUrl = invoice.hosted_invoice_url;
                                }
                            }
                        } catch (error) {
                            console.error(`Error retrieving invoice for subscription ${transaction.stripeSubscriptionId}:`, error);
                        }
                    }

                    // Try to get invoice URL from payment intent
                    if (!invoiceUrl && transaction.stripePaymentIntentId) {
                        try {
                            const paymentIntent = await stripe.paymentIntents.retrieve(transaction.stripePaymentIntentId, {
                                expand: ['invoice']
                            });

                            if (paymentIntent.invoice) {
                                const invoice = typeof paymentIntent.invoice === 'string'
                                    ? await stripe.invoices.retrieve(paymentIntent.invoice)
                                    : paymentIntent.invoice;

                                if (invoice.hosted_invoice_url) {
                                    invoiceUrl = invoice.hosted_invoice_url;
                                }
                            }
                        } catch (error) {
                            console.error(`Error retrieving invoice for payment intent ${transaction.stripePaymentIntentId}:`, error);
                        }
                    }

                    // Try to get invoice URL from charge if not found yet
                    if (!invoiceUrl && transaction.stripeChargeId) {
                        try {
                            const charge = await stripe.charges.retrieve(transaction.stripeChargeId, {
                                expand: ['invoice']
                            });

                            if (charge.invoice) {
                                const invoice = typeof charge.invoice === 'string'
                                    ? await stripe.invoices.retrieve(charge.invoice)
                                    : charge.invoice;

                                if (invoice.hosted_invoice_url) {
                                    invoiceUrl = invoice.hosted_invoice_url;
                                }
                            }
                        } catch (error) {
                            console.error(`Error retrieving invoice for charge ${transaction.stripeChargeId}:`, error);
                        }
                    }

                    // Update transaction if invoice URL found
                    if (invoiceUrl) {
                        transaction.invoiceUrl = invoiceUrl;
                        await transaction.save();
                        console.log(`Added invoice URL to transaction ${transaction._id}`);
                    }
                })
            );

            // Refresh transactions after updating invoice URLs
            const refreshedTransactions = await Transaction.find({ userId: req.user.id })
                .populate('domainId')
                .sort({ createdAt: -1 });

            return res.json(refreshedTransactions);
        }

        res.json(allTransactions);
    } catch (error) {
        console.error('Error syncing transactions:', error);
        res.status(500).json({ error: error.message });
    }
});

// Cancel subscription
router.post('/cancel-subscription', protect, async (req, res) => {
    try {
        const { domainId, cancelImmediately = false } = req.body;

        if (!domainId) {
            return res.status(400).json({ error: 'Domain ID is required' });
        }

        console.log(`Processing cancel subscription request for domain ${domainId}, immediate: ${cancelImmediately}`);

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Check if domain has an active subscription
        console.log(`Checking subscription status for domain ${domainId}:`, {
            hasSubscription: !!domain.subscription,
            active: domain.subscription?.active,
            hasStripeId: !!domain.subscription?.stripeSubscriptionId,
            planType: domain.subscription?.planType
        });

        if (!domain.subscription || !domain.subscription.active || !domain.subscription.stripeSubscriptionId) {
            console.log(`No active subscription found for domain ${domainId}`);

            // Special case: If this was a Daily plan that might have expired naturally,
            // handle it as a successful cancellation instead of an error
            if (domain.subscription?.planType === 'Daily') {
                console.log('Daily plan detected but not active - treating as expired');

                // Update domain subscription data to ensure it's properly marked as Free
                await Domain.findByIdAndUpdate(domainId, {
                    'subscription.active': false,
                    'subscription.planType': 'Free',
                    'subscription.credits': 0,
                    'subscription.canceledAt': new Date(),
                    'subscription.expiresAt': new Date(),
                    'subscription.updatedAt': new Date()
                });

                // Create a record of the cancellation due to expiration
                const transaction = new Transaction({
                    userId: req.user.id,
                    domainId: domainId,
                    stripeSessionId: `expire_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                    stripeSubscriptionId: domain.subscription?.stripeSubscriptionId || null,
                    stripeCustomerId: domain.subscription?.stripeCustomerId || null,
                    transactionType: 'Subscription Cancellation',
                    planType: 'Daily',
                    status: 'completed',
                    amount: 0,
                    currency: 'usd',
                    metadata: {
                        canceledAt: new Date(),
                        expiresAt: new Date(),
                        previousPlan: 'Daily',
                        expiredNaturally: true
                    }
                });

                await transaction.save();
                console.log('Expiration transaction recorded:', transaction._id);

                return res.json({
                    success: true,
                    message: 'Daily plan has expired and been updated to Free.',
                    expiresAt: new Date()
                });
            }

            // For non-Daily plans, return the error response as before
            return res.status(400).json({
                success: false,
                error: 'No active subscription found for this domain',
                message: 'This domain does not have an active subscription to cancel.'
            });
        }

        const subscriptionId = domain.subscription.stripeSubscriptionId;
        console.log(`Cancelling subscription ${subscriptionId} for domain ${domainId}, immediate: ${cancelImmediately}`);

        // Special handling for Daily plans - check if it has expired naturally
        if (domain.subscription.planType === 'Daily') {
            console.log('Daily plan detected, checking if it has expired naturally');

            // Calculate if 24 hours have passed since subscription was created/updated
            const subscriptionUpdated = domain.subscription.updatedAt || new Date();
            const currentTime = new Date();
            const hoursDifference = (currentTime - subscriptionUpdated) / (1000 * 60 * 60);

            if (hoursDifference >= 24) {
                console.log(`Daily plan has likely expired (${hoursDifference.toFixed(2)} hours since update)`);

                // Update domain to Free plan since Daily plan has expired
                await Domain.findByIdAndUpdate(domainId, {
                    'subscription.active': false,
                    'subscription.planType': 'Free',
                    'subscription.credits': 0,
                    'subscription.canceledAt': new Date(),
                    'subscription.expiresAt': new Date(),
                    'subscription.updatedAt': new Date()
                });

                // Create a record of the cancellation due to expiration
                const transaction = new Transaction({
                    userId: req.user.id,
                    domainId: domainId,
                    stripeSessionId: `expire_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                    stripeSubscriptionId: subscriptionId,
                    stripeCustomerId: domain.subscription.stripeCustomerId,
                    transactionType: 'Subscription Cancellation',
                    planType: 'Daily',
                    status: 'completed',
                    amount: 0,
                    currency: 'usd',
                    metadata: {
                        canceledAt: new Date(),
                        expiresAt: new Date(),
                        previousPlan: 'Daily',
                        expiredNaturally: true
                    }
                });

                await transaction.save();
                console.log('Expiration transaction recorded:', transaction._id);

                return res.json({
                    success: true,
                    message: 'Daily plan has expired. Your plan has been updated to Free.',
                    expiresAt: new Date()
                });
            }
        }

        // Retrieve the subscription from Stripe
        let subscription;
        try {
            subscription = await stripe.subscriptions.retrieve(subscriptionId);
            console.log('Retrieved subscription from Stripe:', subscription.id);

            // Additional check: verify the subscription is actually active in Stripe
            if (subscription.status !== 'active' && subscription.status !== 'trialing') {
                console.log(`Subscription ${subscriptionId} is not active in Stripe (status: ${subscription.status})`);

                // Update our database to match Stripe's status
                await Domain.findByIdAndUpdate(domainId, {
                    'subscription.active': false,
                    'subscription.updatedAt': new Date()
                });

                return res.status(400).json({
                    success: false,
                    error: 'Subscription is not active in Stripe',
                    message: `This subscription cannot be canceled because it is ${subscription.status} in Stripe.`
                });
            }
        } catch (stripeError) {
            console.error('Error retrieving subscription from Stripe:', stripeError);

            // If the subscription is not found in Stripe, update our database and handle gracefully
            if (stripeError.code === 'resource_missing') {
                console.log('Subscription not found in Stripe, updating domain subscription status');

                // Update domain with Free plan since the subscription doesn't exist in Stripe
                await Domain.findByIdAndUpdate(domainId, {
                    'subscription.active': false,
                    'subscription.planType': 'Free',
                    'subscription.credits': 0,
                    'subscription.canceledAt': new Date(),
                    'subscription.expiresAt': new Date(),
                    'subscription.updatedAt': new Date()
                });

                // Return a success response to the client
                return res.json({
                    success: true,
                    message: 'Subscription was already canceled in Stripe. Your plan has been updated to Free.',
                    expiresAt: new Date()
                });
            }

            return res.status(500).json({
                error: 'Failed to retrieve subscription from Stripe',
                message: stripeError.message
            });
        }

        // Cancel the subscription in Stripe
        let result;
        let expiresAt;
        try {
            if (cancelImmediately) {
                // Cancel immediately
                result = await stripe.subscriptions.cancel(subscriptionId);
                console.log('Subscription cancelled immediately:', result.id);

                // Update domain subscription with cancellation info
                await Domain.findByIdAndUpdate(domainId, {
                    'subscription.active': false,
                    'subscription.planType': 'Free',
                    'subscription.credits': 0,
                    'subscription.canceledAt': new Date(),
                    'subscription.expiresAt': new Date(),
                    'subscription.updatedAt': new Date()
                });

                expiresAt = new Date();
            } else {
                // Cancel at period end
                result = await stripe.subscriptions.update(subscriptionId, {
                    cancel_at_period_end: true
                });
                console.log('Subscription set to cancel at period end:', result.id);

                // Calculate expiration date from current period end
                expiresAt = new Date(result.current_period_end * 1000);

                // Update domain subscription with cancellation info
                await Domain.findByIdAndUpdate(domainId, {
                    'subscription.canceledAt': new Date(),
                    'subscription.expiresAt': expiresAt,
                    'subscription.updatedAt': new Date()
                });
            }
        } catch (stripeError) {
            console.error('Error cancelling subscription in Stripe:', stripeError);

            // If the subscription is not found, handle it gracefully
            if (stripeError.code === 'resource_missing') {
                console.log('Subscription not found in Stripe during cancellation, updating domain subscription status');

                // Update domain with Free plan
                await Domain.findByIdAndUpdate(domainId, {
                    'subscription.active': false,
                    'subscription.planType': 'Free',
                    'subscription.canceledAt': new Date(),
                    'subscription.expiresAt': new Date(),
                    'subscription.updatedAt': new Date()
                });

                // Return a success response
                return res.json({
                    success: true,
                    message: 'Subscription was not found in Stripe. Your plan has been updated to Free.',
                    expiresAt: new Date()
                });
            }

            return res.status(500).json({
                error: 'Failed to cancel subscription in Stripe',
                message: stripeError.message
            });
        }

        // Create a record of the cancellation
        let transaction;
        try {
            transaction = new Transaction({
                userId: req.user.id,
                domainId: domainId,
                stripeSessionId: `cancel_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`, // Generate a unique dummy session ID for cancellations
                stripeSubscriptionId: subscriptionId,
                stripeCustomerId: domain.subscription.stripeCustomerId,
                transactionType: 'Subscription Cancellation',
                planType: domain.subscription.planType,
                status: 'completed',
                amount: 0,
                currency: 'usd',
                metadata: {
                    canceledAt: new Date(),
                    cancelImmediately,
                    expiresAt: expiresAt,
                    previousPlan: domain.subscription.planType
                }
            });

            await transaction.save();
            console.log('Cancellation transaction recorded:', transaction._id);

            // Send cancellation email
            try {
                const user = await User.findById(req.user.id);
                if (user) {
                    console.log(`Found user for cancellation email: ${user.email}`);
                    const cancellationDetails = {
                        planType: domain.subscription.planType,
                        cancelImmediately,
                        expiresAt: expiresAt
                    };

                    // Try sending email directly with nodemailer first
                    try {
                        console.log('Attempting to send cancellation email directly with nodemailer');
                        const transporter = nodemailer.createTransport({
                            service: 'gmail',
                            auth: {
                                user: process.env.EMAIL_USER || '<EMAIL>',
                                pass: process.env.EMAIL_APP_PASSWORD || 'eewnuizuylfddyml'
                            }
                        });

                        const name = user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;
                        const planType = domain.subscription.planType || 'Premium';
                        const date = new Date().toLocaleDateString();
                        const expiryDateStr = expiresAt ? new Date(expiresAt).toLocaleDateString() : 'Immediately';

                        // Professional email template matching other templates
                        const emailHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Subscription Cancellation</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #fff7ed; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
">
  
  <!-- Email Container -->
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #fff7ed;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <!-- Main Content Card -->
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
              padding: 40px 40px 32px;
              text-align: center;
            ">
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Subscription Cancelled</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">We're sorry to see you go</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hi ${name},</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">We've received your request to cancel your <strong style="color: #ea580c;">${planType} Plan</strong> subscription. ${cancelImmediately
                                ? 'Your subscription has been cancelled immediately and your plan has been downgraded to Free.'
                                : `Your subscription will remain active until <strong>${expiryDateStr}</strong>, after which it will be downgraded to Free.`}</p>
              
              <!-- Cancellation Details -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="
                margin-bottom: 32px;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                overflow: hidden;
              ">
                <tr style="background-color: #f9fafb;">
                  <td style="padding: 16px; border-bottom: 1px solid #e5e7eb;">
                    <p style="margin: 0; font-weight: 600; color: #111827;">Cancellation Details</p>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 16px;">
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                      <tr>
                        <td style="padding-bottom: 8px;"><p style="margin: 0; color: #6b7280;">Cancelled Plan:</p></td>
                        <td style="padding-bottom: 8px; text-align: right;"><p style="margin: 0; font-weight: 500; color: #111827;">${planType}</p></td>
                      </tr>
                      <tr>
                        <td style="padding-bottom: 8px;"><p style="margin: 0; color: #6b7280;">Cancellation Date:</p></td>
                        <td style="padding-bottom: 8px; text-align: right;"><p style="margin: 0; font-weight: 500; color: #111827;">${date}</p></td>
                      </tr>
                      <tr>
                        <td style="padding-bottom: 8px;"><p style="margin: 0; color: #6b7280;">Access Until:</p></td>
                        <td style="padding-bottom: 8px; text-align: right;"><p style="margin: 0; font-weight: 500; color: #111827;">${expiryDateStr}</p></td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">We're sorry to see you go. If you change your mind, you can resubscribe at any time to regain access to all premium features. We'd love to have you back!</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">If you have any feedback on how we could improve our service, please reply to this email. We value your input.</p>
              
              <!-- Footer -->
              <tr>
                <td style="
                  background-color: #fff7ed; 
                  border-top: 1px solid #fed7aa; 
                  padding: 32px 40px; 
                  text-align: center;
                ">
                  <p style="
                    color: #64748b; 
                    font-size: 16px; 
                    margin: 0; 
                    font-weight: 400;
                  ">Thanks,<br><strong style="color: #ea580c;">The BlogBuster Team</strong></p>
                  
                  <!-- Copyright -->
                  <p style="
                    color: #9ca3af; 
                    font-size: 12px; 
                    margin: 24px 0 0 0;
                  ">© ${new Date().getFullYear()} BlogBuster. All rights reserved.</p>
                </td>
              </tr>
              
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>`;

                        await transporter.sendMail({
                            from: `"BlogBuster" <${process.env.EMAIL_USER || '<EMAIL>'}>`,
                            to: user.email,
                            subject: 'Your BlogBuster Subscription Cancellation',
                            html: emailHtml
                        });

                        console.log(`Direct cancellation email sent to ${user.email}`);
                    } catch (directEmailError) {
                        console.error('Error sending direct cancellation email:', directEmailError);

                        // Fall back to the email service
                        const emailSent = await emailService.sendPlanCancellationEmail(user, cancellationDetails);
                        console.log(`Plan cancellation email sent via service to ${user.email}, result: ${emailSent}`);

                        if (!emailSent) {
                            console.log('Email service reported failure, will retry once');
                            // Retry once with a delay
                            setTimeout(async () => {
                                try {
                                    const retryResult = await emailService.sendPlanCancellationEmail(user, cancellationDetails);
                                    console.log(`Retry sending cancellation email to ${user.email}, result: ${retryResult}`);
                                } catch (retryError) {
                                    console.error('Error in retry sending cancellation email:', retryError);
                                }
                            }, 2000);
                        }
                    }
                } else {
                    console.error(`User not found for cancellation email, ID: ${req.user.id}`);
                }
            } catch (emailError) {
                console.error('Error sending cancellation email:', emailError);
            }
        } catch (dbError) {
            console.error('Error recording cancellation transaction:', dbError);
            // Continue even if transaction recording fails
        }

        res.json({
            success: true,
            message: cancelImmediately
                ? 'Subscription cancelled successfully'
                : 'Subscription will be cancelled at the end of the billing period',
            expiresAt: expiresAt
        });
    } catch (error) {
        console.error('Error cancelling subscription:', error);
        res.status(500).json({
            error: 'Failed to cancel subscription',
            message: error.message
        });
    }
});

// Get subscription for a domain
router.get('/subscription/:domainId', protect, async (req, res) => {
    try {
        const { domainId } = req.params;

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Return the subscription data
        res.json({
            subscription: domain.subscription || {
                active: false,
                planType: 'Free',
                updatedAt: new Date()
            }
        });
    } catch (error) {
        console.error('Error fetching subscription:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get plan for a domain
router.get('/plan', protect, async (req, res) => {
    try {
        const { domainId } = req.query;

        if (!domainId) {
            return res.status(400).json({ error: 'Domain ID is required' });
        }

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Get the user's plan
        const plan = await Plan.findOne({ userId: req.user.id });

        // If no plan exists, create a free plan
        if (!plan) {
            const freePlan = new Plan({
                userId: req.user.id,
                type: 'Free',
                displayType: 'Free',
                features: {
                    maxDomains: 1,
                    maxArticlesPerMonth: 10,
                    customBranding: false
                },
                startDate: new Date(),
                endDate: null,
                status: 'active'
            });
            await freePlan.save();
            return res.json({ plan: freePlan });
        }

        res.json({ plan });
    } catch (error) {
        console.error('Error fetching plan:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get all transactions for the current user
router.get('/transactions', protect, async (req, res) => {
    try {
        // Find all transactions for the user
        const transactions = await Transaction.find({ userId: req.user.id })
            .populate('domainId')
            .sort({ createdAt: -1 });

        // For completed transactions without invoice URLs, try to fetch them in the background
        const transactionsWithoutInvoice = transactions.filter(
            t => t.status === 'completed' && !t.invoiceUrl && (t.stripeSubscriptionId || t.stripePaymentIntentId || t.stripeChargeId)
        );

        if (transactionsWithoutInvoice.length > 0) {
            console.log(`Found ${transactionsWithoutInvoice.length} completed transactions without invoice URLs. Will fetch in background.`);

            // Process in background (don't await)
            Promise.all(
                transactionsWithoutInvoice.map(async (transaction) => {
                    let invoiceUrl = null;

                    // Try to get invoice URL from subscription
                    if (transaction.stripeSubscriptionId) {
                        try {
                            const subscription = await stripe.subscriptions.retrieve(transaction.stripeSubscriptionId, {
                                expand: ['latest_invoice']
                            });

                            if (subscription.latest_invoice) {
                                const invoice = typeof subscription.latest_invoice === 'string'
                                    ? await stripe.invoices.retrieve(subscription.latest_invoice)
                                    : subscription.latest_invoice;

                                if (invoice.hosted_invoice_url) {
                                    invoiceUrl = invoice.hosted_invoice_url;
                                }
                            }
                        } catch (error) {
                            console.error(`Error retrieving invoice for subscription ${transaction.stripeSubscriptionId}:`, error);
                        }
                    }

                    // Try to get invoice URL from payment intent if not found from subscription
                    if (!invoiceUrl && transaction.stripePaymentIntentId) {
                        try {
                            const paymentIntent = await stripe.paymentIntents.retrieve(transaction.stripePaymentIntentId, {
                                expand: ['invoice']
                            });

                            if (paymentIntent.invoice) {
                                const invoice = typeof paymentIntent.invoice === 'string'
                                    ? await stripe.invoices.retrieve(paymentIntent.invoice)
                                    : paymentIntent.invoice;

                                if (invoice.hosted_invoice_url) {
                                    invoiceUrl = invoice.hosted_invoice_url;
                                }
                            }
                        } catch (error) {
                            console.error(`Error retrieving invoice for payment intent ${transaction.stripePaymentIntentId}:`, error);
                        }
                    }

                    // Try to get invoice URL from charge if not found yet
                    if (!invoiceUrl && transaction.stripeChargeId) {
                        try {
                            const charge = await stripe.charges.retrieve(transaction.stripeChargeId, {
                                expand: ['invoice']
                            });

                            if (charge.invoice) {
                                const invoice = typeof charge.invoice === 'string'
                                    ? await stripe.invoices.retrieve(charge.invoice)
                                    : charge.invoice;

                                if (invoice.hosted_invoice_url) {
                                    invoiceUrl = invoice.hosted_invoice_url;
                                }
                            }
                        } catch (error) {
                            console.error(`Error retrieving invoice for charge ${transaction.stripeChargeId}:`, error);
                        }
                    }

                    // Update transaction if invoice URL found
                    if (invoiceUrl) {
                        transaction.invoiceUrl = invoiceUrl;
                        transaction.metadata = {
                            ...transaction.metadata,
                            invoiceFound: true,
                            invoiceUpdatedAt: new Date()
                        };
                        await transaction.save();
                        console.log(`Added invoice URL to transaction ${transaction._id}`);
                    }
                })
            ).catch(err => {
                console.error('Error in background invoice URL fetching:', err);
            });
        }

        res.json(transactions);
    } catch (error) {
        console.error('Error fetching transactions:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get transaction invoice URL
router.get('/transactions/:transactionId/invoice', protect, async (req, res) => {
    try {
        const { transactionId } = req.params;

        // Find the transaction
        const transaction = await Transaction.findOne({
            _id: transactionId,
            userId: req.user.id
        });

        if (!transaction) {
            return res.status(404).json({ error: 'Transaction not found' });
        }

        // If we already have an invoice URL, return it
        if (transaction.invoiceUrl) {
            return res.json({ invoiceUrl: transaction.invoiceUrl });
        }

        // Try to get the invoice URL from Stripe
        let invoiceUrl = null;

        // Method 1: Try to get invoice from subscription
        if (transaction.stripeSubscriptionId) {
            try {
                console.log(`Retrieving invoice URL from subscription: ${transaction.stripeSubscriptionId}`);
                const subscription = await stripe.subscriptions.retrieve(transaction.stripeSubscriptionId, {
                    expand: ['latest_invoice']
                });

                if (subscription.latest_invoice) {
                    const invoice = typeof subscription.latest_invoice === 'string'
                        ? await stripe.invoices.retrieve(subscription.latest_invoice)
                        : subscription.latest_invoice;

                    if (invoice.hosted_invoice_url) {
                        invoiceUrl = invoice.hosted_invoice_url;
                        console.log(`Found invoice URL from subscription: ${invoiceUrl}`);
                    }
                }
            } catch (error) {
                console.error(`Error retrieving invoice from subscription: ${error.message}`);
            }
        }

        // Method 2: Try to get invoice from payment intent
        if (!invoiceUrl && transaction.stripePaymentIntentId) {
            try {
                console.log(`Retrieving invoice URL from payment intent: ${transaction.stripePaymentIntentId}`);
                const paymentIntent = await stripe.paymentIntents.retrieve(transaction.stripePaymentIntentId, {
                    expand: ['invoice']
                });

                if (paymentIntent.invoice) {
                    const invoice = typeof paymentIntent.invoice === 'string'
                        ? await stripe.invoices.retrieve(paymentIntent.invoice)
                        : paymentIntent.invoice;

                    if (invoice.hosted_invoice_url) {
                        invoiceUrl = invoice.hosted_invoice_url;
                        console.log(`Found invoice URL from payment intent: ${invoiceUrl}`);
                    }
                }
            } catch (error) {
                console.error(`Error retrieving invoice from payment intent: ${error.message}`);
            }
        }

        // Method 3: Try to get invoice URL directly from session
        if (!invoiceUrl && transaction.stripeSessionId) {
            try {
                console.log(`Retrieving invoice URL from session: ${transaction.stripeSessionId}`);
                const session = await stripe.checkout.sessions.retrieve(transaction.stripeSessionId, {
                    expand: ['invoice']
                });

                if (session.invoice) {
                    const invoice = typeof session.invoice === 'string'
                        ? await stripe.invoices.retrieve(session.invoice)
                        : session.invoice;

                    if (invoice.hosted_invoice_url) {
                        invoiceUrl = invoice.hosted_invoice_url;
                        console.log(`Found invoice URL from session: ${invoiceUrl}`);
                    }
                }
            } catch (error) {
                console.error(`Error retrieving invoice from session: ${error.message}`);
            }
        }

        // Method 4: Try to get invoice from charge
        if (!invoiceUrl && transaction.stripeChargeId) {
            try {
                console.log(`Retrieving invoice URL from charge: ${transaction.stripeChargeId}`);
                const charge = await stripe.charges.retrieve(transaction.stripeChargeId, {
                    expand: ['invoice']
                });

                if (charge.invoice) {
                    const invoice = typeof charge.invoice === 'string'
                        ? await stripe.invoices.retrieve(charge.invoice)
                        : charge.invoice;

                    if (invoice.hosted_invoice_url) {
                        invoiceUrl = invoice.hosted_invoice_url;
                        console.log(`Found invoice URL from charge: ${invoiceUrl}`);
                    }
                }
            } catch (error) {
                console.error(`Error retrieving invoice from charge: ${error.message}`);
            }
        }

        // If we found an invoice URL, update the transaction
        if (invoiceUrl) {
            transaction.invoiceUrl = invoiceUrl;
            transaction.metadata = {
                ...transaction.metadata,
                invoiceFound: true,
                invoiceUpdatedAt: new Date()
            };
            await transaction.save();
            console.log(`Updated transaction ${transactionId} with invoice URL: ${invoiceUrl}`);
            return res.json({ invoiceUrl });
        }

        // If we still don't have an invoice URL, return null
        return res.json({ invoiceUrl: null });
    } catch (error) {
        console.error(`Error retrieving invoice URL: ${error.message}`);
        res.status(500).json({ error: 'Failed to retrieve invoice URL' });
    }
});

// Refresh subscription data
router.get('/refresh-subscription/:domainId', protect, async (req, res) => {
    try {
        const { domainId } = req.params;

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        console.log(`Refreshing subscription data for domain ${domainId}`);

        // If no subscription, return default data
        if (!domain.subscription || !domain.subscription.stripeSubscriptionId) {
            return res.json({
                subscription: {
                    active: false,
                    planType: 'Free',
                    updatedAt: new Date()
                }
            });
        }

        const subscriptionId = domain.subscription.stripeSubscriptionId;

        // Try to retrieve the subscription from Stripe
        try {
            const subscription = await stripe.subscriptions.retrieve(subscriptionId);
            console.log(`Retrieved subscription from Stripe: ${subscription.id}, status: ${subscription.status}`);

            // Update our database to match Stripe's status
            const isActive = subscription.status === 'active' || subscription.status === 'trialing';

            // Get plan type from metadata or default to current
            let planType = domain.subscription.planType;
            if (subscription.metadata && subscription.metadata.planType) {
                planType = subscription.metadata.planType;
            }

            // Update domain subscription
            const updatedDomain = await Domain.findByIdAndUpdate(
                domainId,
                {
                    'subscription.active': isActive,
                    'subscription.planType': isActive ? planType : 'Free',
                    'subscription.updatedAt': new Date(),
                    'subscription.canceledAt': subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : domain.subscription.canceledAt,
                    'subscription.expiresAt': subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : domain.subscription.expiresAt
                },
                { new: true }
            );

            return res.json({
                subscription: updatedDomain.subscription
            });
        } catch (stripeError) {
            console.error('Error retrieving subscription from Stripe:', stripeError);

            // If subscription doesn't exist in Stripe, update our records
            if (stripeError.code === 'resource_missing') {
                console.log('Subscription not found in Stripe, updating local records');

                // Update domain subscription
                const updatedDomain = await Domain.findByIdAndUpdate(
                    domainId,
                    {
                        'subscription.active': false,
                        'subscription.planType': 'Free',
                        'subscription.stripeSubscriptionId': null,
                        'subscription.canceledAt': new Date(),
                        'subscription.updatedAt': new Date()
                    },
                    { new: true }
                );

                return res.json({
                    subscription: updatedDomain.subscription
                });
            }

            // For other errors, return the current subscription data
            return res.json({
                subscription: domain.subscription
            });
        }
    } catch (error) {
        console.error('Error refreshing subscription:', error);
        res.status(500).json({ error: error.message });
    }
});

// Webhook endpoint to handle Stripe events
router.post('/webhook', (req, res) => {
    handleWebhook(req, res);
});

// Function to handle checkout.session.completed event
async function handleCheckoutSessionCompleted(event) {
    const session = event.data.object;
    console.log(`Processing checkout.session.completed: ${session.id}`);

    // Find the transaction by session ID
    const transaction = await Transaction.findOne({ stripeSessionId: session.id });

    if (!transaction) {
        console.log(`No transaction found for session ID: ${session.id}`);
        return;
    }

    // Update transaction status
    transaction.status = 'completed';

    // Update payment details
    if (session.customer) {
        transaction.stripeCustomerId = session.customer;
    }

    if (session.subscription) {
        transaction.stripeSubscriptionId = session.subscription;

        // Fetch subscription details to get period dates
        try {
            const subscription = await stripe.subscriptions.retrieve(session.subscription);

            // Store subscription period dates
            if (subscription) {
                // Convert Unix timestamps to JavaScript Date objects
                const startDate = subscription.current_period_start ? new Date(subscription.current_period_start * 1000) : null;
                const endDate = subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null;

                // Next payment date is the same as current period end for active subscriptions
                const nextPaymentDate = subscription.status === 'active' ?
                    (subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null) : null;

                transaction.subscriptionPeriod = {
                    startDate,
                    endDate,
                    nextPaymentDate
                };

                console.log(`Subscription period dates stored: Start: ${startDate}, End: ${endDate}, Next Payment: ${nextPaymentDate}`);
            }
        } catch (error) {
            console.error(`Error retrieving subscription details: ${error.message}`);
        }
    }

    if (session.payment_intent) {
        transaction.stripePaymentIntentId = session.payment_intent;
    }

    // Get the plan type from the transaction or session metadata
    const planType = transaction.planType || session.metadata?.planType;
    console.log(`Plan type for transaction ${transaction._id}: ${planType}`);

    // Try to get invoice URL
    let invoiceUrl = await getInvoiceUrlFromStripe(session, transaction);

    // Update transaction with invoice URL if found
    if (invoiceUrl) {
        transaction.invoiceUrl = invoiceUrl;
        transaction.metadata = {
            ...transaction.metadata,
            invoiceFound: true,
            invoiceUpdatedAt: new Date()
        };
    }

    // Ensure planType is set correctly
    if (planType && !transaction.planType) {
        transaction.planType = planType;
    }

    // Save the updated transaction
    await transaction.save();
    console.log(`Transaction ${transaction._id} updated with status: ${transaction.status}, planType: ${transaction.planType}, subscription ID: ${transaction.stripeSubscriptionId || 'not found'}`);

    // Check if this is a prorated payment
    if (session.metadata?.isProrated === 'true' || transaction.metadata?.isProrated === true || transaction.transactionType === 'Plan Change') {
        console.log(`Processing prorated payment for transaction ${transaction._id}, type: ${transaction.transactionType}`);
        console.log(`Session metadata isProrated: ${session.metadata?.isProrated}`);
        console.log(`Transaction metadata isProrated: ${transaction.metadata?.isProrated}`);
        await processCompletedProratedPayment(transaction);

        // Send proration email
        try {
            const user = await User.findById(transaction.userId);
            if (user) {
                // Prepare proration details
                const proratedDetails = {
                    currentPlanType: transaction.metadata?.previousPlanType || 'previous plan',
                    newPlanType: transaction.planType,
                    originalAmount: transaction.metadata?.originalAmount || transaction.amount,
                    unusedCredit: transaction.metadata?.unusedCredit || 0,
                    finalAmount: transaction.amount,
                    invoiceUrl: transaction.invoiceUrl
                };

                // Send email
                await emailService.sendPlanProrationEmail(user, proratedDetails);
                console.log(`Plan proration email sent to ${user.email}`);
            }
        } catch (error) {
            console.error('Error sending plan proration email:', error);
        }
    } else {
        // For regular subscriptions, update as normal
        await updateSubscriptionAndPlan(transaction);

        // Send purchase confirmation email
        try {
            const user = await User.findById(transaction.userId);
            if (user) {
                // Prepare plan details with correct plan type
                const planDetails = {
                    planType: transaction.planType, // Use the plan type from the transaction
                    amount: transaction.amount,
                    invoiceUrl: transaction.invoiceUrl
                };

                console.log(`Sending purchase email for ${planDetails.planType} plan to ${user.email}`);

                // Send email
                await emailService.sendPlanPurchaseEmail(user, planDetails);
                console.log(`Plan purchase email sent to ${user.email}`);
            }
        } catch (error) {
            console.error('Error sending plan purchase email:', error);
        }
    }
}

/**
 * Helper function to get a valid invoice URL from Stripe
 * @param {Object} session - Stripe checkout session
 * @param {Object} transaction - Transaction record
 * @returns {Promise<string|null>} - Invoice URL or null
 */
async function getInvoiceUrlFromStripe(session, transaction) {
    let invoiceUrl = null;

    // Method 1: Try to get invoice URL from subscription
    if (session.subscription) {
        try {
            console.log(`Retrieving invoice URL from subscription: ${session.subscription}`);
            const subscription = await stripe.subscriptions.retrieve(session.subscription, {
                expand: ['latest_invoice']
            });

            if (subscription.latest_invoice) {
                const invoice = typeof subscription.latest_invoice === 'string'
                    ? await stripe.invoices.retrieve(subscription.latest_invoice)
                    : subscription.latest_invoice;

                if (invoice.hosted_invoice_url) {
                    invoiceUrl = invoice.hosted_invoice_url;
                    console.log(`Found invoice URL from subscription: ${invoiceUrl}`);

                    // Store the original Stripe invoice URL in metadata for reference
                    if (transaction) {
                        transaction.metadata = {
                            ...transaction.metadata,
                            stripeInvoiceUrl: invoice.hosted_invoice_url,
                            invoiceId: invoice.id
                        };
                    }

                    return invoice.hosted_invoice_url;
                }
            }
        } catch (error) {
            console.error(`Error retrieving invoice from subscription: ${error.message}`);
        }
    }

    // Method 2: Try to get invoice URL from payment intent
    if (!invoiceUrl && session.payment_intent) {
        try {
            console.log(`Retrieving invoice URL from payment intent: ${session.payment_intent}`);
            const paymentIntent = await stripe.paymentIntents.retrieve(session.payment_intent, {
                expand: ['invoice']
            });

            if (paymentIntent.invoice) {
                const invoice = typeof paymentIntent.invoice === 'string'
                    ? await stripe.invoices.retrieve(paymentIntent.invoice)
                    : paymentIntent.invoice;

                if (invoice.hosted_invoice_url) {
                    invoiceUrl = invoice.hosted_invoice_url;
                    console.log(`Found invoice URL from payment intent: ${invoiceUrl}`);

                    // Store the original Stripe invoice URL in metadata for reference
                    if (transaction) {
                        transaction.metadata = {
                            ...transaction.metadata,
                            stripeInvoiceUrl: invoice.hosted_invoice_url,
                            invoiceId: invoice.id
                        };
                    }

                    return invoice.hosted_invoice_url;
                }
            }
        } catch (error) {
            console.error(`Error retrieving invoice from payment intent: ${error.message}`);
        }
    }

    // Method 3: Try to get invoice URL directly from session
    if (!invoiceUrl && session.invoice) {
        try {
            console.log(`Retrieving invoice URL from session invoice: ${session.invoice}`);
            const invoice = await stripe.invoices.retrieve(session.invoice);

            if (invoice.hosted_invoice_url) {
                invoiceUrl = invoice.hosted_invoice_url;
                console.log(`Found invoice URL from session invoice: ${invoiceUrl}`);

                // Store the original Stripe invoice URL in metadata for reference
                if (transaction) {
                    transaction.metadata = {
                        ...transaction.metadata,
                        stripeInvoiceUrl: invoice.hosted_invoice_url,
                        invoiceId: invoice.id
                    };
                }

                return invoice.hosted_invoice_url;
            }
        } catch (error) {
            console.error(`Error retrieving invoice from session: ${error.message}`);
        }
    }

    // Method 4: Try to get invoice URL from Stripe customer portal
    if (!invoiceUrl && session.customer) {
        try {
            console.log(`Creating customer portal session for customer: ${session.customer}`);
            const portalSession = await stripe.billingPortal.sessions.create({
                customer: session.customer,
                return_url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard/transactions`
            });

            if (portalSession.url) {
                invoiceUrl = portalSession.url;
                console.log(`Created customer portal URL: ${invoiceUrl}`);

                // Store the portal URL in metadata for reference
                if (transaction) {
                    transaction.metadata = {
                        ...transaction.metadata,
                        stripePortalUrl: portalSession.url,
                        portalCreatedAt: new Date()
                    };
                }

                return portalSession.url;
            }
        } catch (error) {
            console.error(`Error creating customer portal session: ${error.message}`);
        }
    }

    // Method 5: Use Stripe dashboard as fallback instead of localhost
    invoiceUrl = 'https://dashboard.stripe.com';
    console.log(`Using Stripe dashboard as fallback URL: ${invoiceUrl}`);

    return invoiceUrl;
}

// Function to handle invoice.payment_succeeded event
async function handleInvoicePaymentSucceeded(event) {
    const invoice = event.data.object;
    console.log(`Processing invoice.payment_succeeded: ${invoice.id}`);

    // Check if this is a subscription invoice
    if (!invoice.subscription) {
        console.log('Not a subscription invoice, skipping');
        return;
    }

    // Find the transaction by subscription ID
    const transaction = await Transaction.findOne({ stripeSubscriptionId: invoice.subscription });

    if (!transaction) {
        console.log(`No transaction found for subscription ID: ${invoice.subscription}`);
        return;
    }

    // Update transaction with invoice URL
    if (invoice.hosted_invoice_url && !transaction.invoiceUrl) {
        transaction.invoiceUrl = invoice.hosted_invoice_url;
        transaction.metadata = {
            ...transaction.metadata,
            invoiceFound: true,
            invoiceUpdatedAt: new Date()
        };

        await transaction.save();
        console.log(`Transaction ${transaction._id} updated with invoice URL: ${invoice.hosted_invoice_url}`);
    }

    // Handle recurring payments - refresh domain credits
    try {
        // Find domain with this subscription
        const domain = await Domain.findOne({ 'subscription.stripeSubscriptionId': invoice.subscription });

        if (!domain) {
            console.log(`No domain found for subscription ID: ${invoice.subscription}`);

            // MODIFIED: Create an orphaned transaction record even when domain is not found
            try {
                // Get subscription details from Stripe
                const subscription = await stripe.subscriptions.retrieve(invoice.subscription);

                if (subscription && subscription.metadata) {
                    const { userId, domainId, planType } = subscription.metadata;

                    console.log(`Creating orphaned transaction record for subscription ${invoice.subscription}`);

                    // Determine plan type and credits from Stripe subscription metadata
                    const detectedPlanType = planType || 'Unknown';
                    const credits = detectedPlanType === 'Daily' ? 1 :
                        detectedPlanType === 'Monthly' ? 30 :
                            detectedPlanType === 'Yearly' ? 365 : 0;

                    // Create orphaned transaction record
                    const orphanedTransaction = new Transaction({
                        userId: userId || null,
                        domainId: domainId || null,
                        stripeSessionId: `orphaned_recurring_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                        stripeSubscriptionId: invoice.subscription,
                        stripeCustomerId: subscription.customer,
                        transactionType: 'Recurring Subscription',
                        planType: detectedPlanType,
                        status: 'completed',
                        amount: invoice.amount_paid / 100, // Convert from cents
                        currency: invoice.currency,
                        invoiceUrl: invoice.hosted_invoice_url,
                        metadata: {
                            invoiceId: invoice.id,
                            orphaned: true,
                            reason: 'Domain subscription data not found',
                            originalSubscriptionId: invoice.subscription,
                            detectedFromStripe: true,
                            creditsCalculated: credits,
                            refreshedAt: new Date()
                        }
                    });

                    // Add subscription period details
                    const startDate = subscription.current_period_start ? new Date(subscription.current_period_start * 1000) : null;
                    const endDate = subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null;
                    const nextPaymentDate = subscription.status === 'active' ?
                        (subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null) : null;

                    orphanedTransaction.subscriptionPeriod = {
                        startDate,
                        endDate,
                        nextPaymentDate
                    };

                    await orphanedTransaction.save();
                    console.log(`Orphaned recurring transaction created: ${orphanedTransaction._id}`);

                    // Optional: Try to find domain by ID from metadata and update it
                    if (domainId) {
                        const foundDomain = await Domain.findById(domainId);
                        if (foundDomain) {
                            console.log(`Found domain ${domainId} by metadata, but no subscription data exists`);
                            // You could optionally restore subscription data here if needed
                        }
                    }
                } else {
                    console.log(`No subscription metadata found for ${invoice.subscription}, creating minimal transaction record`);

                    // Create minimal transaction record without user/domain info
                    const minimalTransaction = new Transaction({
                        userId: null,
                        domainId: null,
                        stripeSessionId: `orphaned_minimal_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                        stripeSubscriptionId: invoice.subscription,
                        stripeCustomerId: invoice.customer,
                        transactionType: 'Recurring Subscription',
                        planType: 'Unknown',
                        status: 'completed',
                        amount: invoice.amount_paid / 100,
                        currency: invoice.currency,
                        invoiceUrl: invoice.hosted_invoice_url,
                        metadata: {
                            invoiceId: invoice.id,
                            orphaned: true,
                            reason: 'No domain or subscription metadata found',
                            originalSubscriptionId: invoice.subscription,
                            refreshedAt: new Date()
                        }
                    });

                    await minimalTransaction.save();
                    console.log(`Minimal orphaned transaction created: ${minimalTransaction._id}`);
                }
            } catch (orphanedError) {
                console.error('Error creating orphaned transaction record:', orphanedError);
            }

            return; // Still return early, but after creating the orphaned record
        }

        // EXISTING LOGIC: Domain found, process normally
        // Check if subscription data is complete
        if (!domain.subscription || !domain.subscription.planType || !domain.subscription.stripeCustomerId) {
            console.log(`Domain found but subscription data incomplete for domain ${domain._id}`);

            // Create transaction record with partial data and try to get missing info from Stripe
            try {
                const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
                const detectedPlanType = subscription.metadata?.planType || domain.subscription?.planType || 'Unknown';
                const stripeCustomerId = subscription.customer || domain.subscription?.stripeCustomerId;

                console.log(`Creating transaction for domain with incomplete subscription data`);

                const partialTransaction = new Transaction({
                    userId: domain.userId,
                    domainId: domain._id,
                    stripeSessionId: `partial_recurring_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                    stripeSubscriptionId: invoice.subscription,
                    stripeCustomerId: stripeCustomerId,
                    transactionType: 'Recurring Subscription',
                    planType: detectedPlanType,
                    status: 'completed',
                    amount: invoice.amount_paid / 100,
                    currency: invoice.currency,
                    invoiceUrl: invoice.hosted_invoice_url,
                    metadata: {
                        invoiceId: invoice.id,
                        partialSubscriptionData: true,
                        reason: 'Domain found but subscription data incomplete',
                        originalPlanType: domain.subscription?.planType,
                        detectedFromStripe: true,
                        refreshedAt: new Date()
                    }
                });

                // Add subscription period details
                const startDate = subscription.current_period_start ? new Date(subscription.current_period_start * 1000) : null;
                const endDate = subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null;
                const nextPaymentDate = subscription.status === 'active' ?
                    (subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null) : null;

                partialTransaction.subscriptionPeriod = {
                    startDate,
                    endDate,
                    nextPaymentDate
                };

                await partialTransaction.save();
                console.log(`Partial subscription transaction created: ${partialTransaction._id}`);

                // Optionally restore subscription data to domain
                const credits = detectedPlanType === 'Daily' ? 1 :
                    detectedPlanType === 'Monthly' ? 30 :
                        detectedPlanType === 'Yearly' ? 365 : 0;

                console.log(`Attempting to restore subscription data for domain ${domain._id}`);

            } catch (partialError) {
                console.error('Error creating partial transaction record:', partialError);
            }
            return;
        }

        // Set credits based on plan type
        let credits = 0;
        if (domain.subscription.planType === 'Daily') {
            credits = 1;
        } else if (domain.subscription.planType === 'Monthly') {
            credits = 30;
        } else if (domain.subscription.planType === 'Yearly') {
            credits = 365;
        }

        // Update domain credits
        await Domain.findByIdAndUpdate(domain._id, {
            'subscription.credits': credits,
            'subscription.updatedAt': new Date()
        });

        console.log(`Credits refreshed for domain ${domain._id} (${domain.name}): ${credits} credits`);

        // Create a recurring transaction record
        const recurringTransaction = new Transaction({
            userId: domain.userId,
            domainId: domain._id,
            stripeSessionId: `recurring_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
            stripeSubscriptionId: invoice.subscription,
            stripeCustomerId: domain.subscription.stripeCustomerId,
            transactionType: 'Recurring Subscription',
            planType: domain.subscription.planType,
            status: 'completed',
            amount: invoice.amount_paid / 100, // Convert from cents
            currency: invoice.currency,
            invoiceUrl: invoice.hosted_invoice_url,
            metadata: {
                invoiceId: invoice.id,
                creditsRefreshed: credits,
                refreshedAt: new Date()
            }
        });

        // Try to get subscription period details
        try {
            if (invoice.subscription) {
                const subscription = await stripe.subscriptions.retrieve(invoice.subscription);

                if (subscription) {
                    // Convert Unix timestamps to JavaScript Date objects
                    const startDate = subscription.current_period_start ? new Date(subscription.current_period_start * 1000) : null;
                    const endDate = subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null;

                    // Next payment date is the same as current period end for active subscriptions
                    const nextPaymentDate = subscription.status === 'active' ?
                        (subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null) : null;

                    recurringTransaction.subscriptionPeriod = {
                        startDate,
                        endDate,
                        nextPaymentDate
                    };

                    console.log(`Recurring subscription period dates stored: Start: ${startDate}, End: ${endDate}, Next Payment: ${nextPaymentDate}`);
                }
            }
        } catch (error) {
            console.error(`Error retrieving subscription details for recurring payment: ${error.message}`);
        }

        await recurringTransaction.save();
        console.log(`Recurring transaction created: ${recurringTransaction._id}`);
    } catch (error) {
        console.error('Error processing recurring payment:', error);
    }
}

// Function to handle payment_intent.succeeded event
async function handlePaymentIntentSucceeded(event) {
    const paymentIntent = event.data.object;
    console.log(`Processing payment_intent.succeeded: ${paymentIntent.id}`);

    // Find the transaction by payment intent ID
    const transaction = await Transaction.findOne({ stripePaymentIntentId: paymentIntent.id });

    if (!transaction) {
        console.log(`No transaction found for payment intent ID: ${paymentIntent.id}`);

        // Try to find transaction by session ID if available in metadata
        if (paymentIntent.metadata?.checkout_session_id) {
            const sessionTransaction = await Transaction.findOne({
                stripeSessionId: paymentIntent.metadata.checkout_session_id
            });

            if (sessionTransaction) {
                console.log(`Found transaction by checkout session ID: ${sessionTransaction._id}`);
                // Update the transaction with the payment intent ID
                sessionTransaction.stripePaymentIntentId = paymentIntent.id;
                sessionTransaction.status = 'completed';

                if (paymentIntent.latest_charge) {
                    sessionTransaction.stripeChargeId = paymentIntent.latest_charge;
                }

                // Try to get subscription ID from the payment intent
                if (paymentIntent.invoice) {
                    try {
                        const invoice = await stripe.invoices.retrieve(paymentIntent.invoice);
                        if (invoice.subscription) {
                            sessionTransaction.stripeSubscriptionId = invoice.subscription;
                            console.log(`Retrieved subscription ID from invoice: ${invoice.subscription}`);
                        }
                    } catch (error) {
                        console.error(`Error retrieving invoice for subscription ID: ${error.message}`);
                    }
                }

                await sessionTransaction.save();
                console.log(`Updated transaction ${sessionTransaction._id} with payment intent ID and status`);

                // Check if this is a prorated payment
                if (sessionTransaction.metadata?.isProrated === true || sessionTransaction.transactionType === 'Plan Change') {
                    await processCompletedProratedPayment(sessionTransaction);
                }

                return;
            }
        }

        return;
    }

    // Update transaction status
    transaction.status = 'completed';

    // Try to get invoice URL and subscription ID
    if (paymentIntent.invoice) {
        try {
            console.log(`Retrieving invoice from payment intent: ${paymentIntent.invoice}`);
            const invoice = await stripe.invoices.retrieve(paymentIntent.invoice);

            if (invoice.hosted_invoice_url) {
                transaction.invoiceUrl = invoice.hosted_invoice_url;
                console.log(`Found invoice URL from payment intent invoice: ${invoice.hosted_invoice_url}`);
            }

            // Get subscription ID from invoice
            if (invoice.subscription) {
                transaction.stripeSubscriptionId = invoice.subscription;
                console.log(`Found subscription ID from invoice: ${invoice.subscription}`);
            }

            transaction.metadata = {
                ...transaction.metadata,
                invoiceFound: true,
                invoiceUpdatedAt: new Date()
            };
        } catch (error) {
            console.error(`Error retrieving invoice from payment intent: ${error.message}`);
        }
    }

    // Update charge ID if available
    if (paymentIntent.latest_charge && !transaction.stripeChargeId) {
        transaction.stripeChargeId = paymentIntent.latest_charge;
    }

    // If we still don't have a subscription ID, try to get it from related objects
    if (!transaction.stripeSubscriptionId) {
        // Try to get from domain if this is a subscription transaction
        if (transaction.transactionType === 'New Subscription') {
            try {
                const domain = await Domain.findById(transaction.domainId);
                if (domain && domain.subscription?.stripeSubscriptionId) {
                    transaction.stripeSubscriptionId = domain.subscription.stripeSubscriptionId;
                    console.log(`Retrieved subscription ID from domain: ${transaction.stripeSubscriptionId}`);
                }
            } catch (error) {
                console.error(`Error retrieving domain for subscription ID: ${error.message}`);
            }
        }
    }

    // Save the updated transaction
    await transaction.save();
    console.log(`Transaction ${transaction._id} updated with status: ${transaction.status}, subscription ID: ${transaction.stripeSubscriptionId || 'not found'}`);

    // Check if this is a prorated payment
    if (transaction.metadata?.isProrated === true || transaction.transactionType === 'Plan Change') {
        await processCompletedProratedPayment(transaction);
    }
}

// Helper function to process completed prorated payments
async function processCompletedProratedPayment(transaction) {
    console.log(`Processing completed prorated payment for transaction ${transaction._id}`);

    try {
        // Get the domain
        const domain = await Domain.findById(transaction.domainId);
        if (!domain) {
            console.log(`Domain not found for transaction ${transaction._id}`);
            return;
        }

        // Get the current subscription from Stripe
        const currentSubscriptionId = domain.subscription?.stripeSubscriptionId;
        if (!currentSubscriptionId) {
            console.log(`No subscription ID found for domain ${transaction.domainId}`);
            return;
        }

        // Update the existing subscription instead of canceling and creating new one
        const newPlanType = transaction.planType;
        let priceId;
        if (newPlanType === 'Monthly') {
            priceId = PRICE_ID_MONTHLY;
        } else if (newPlanType === 'Yearly') {
            priceId = PRICE_ID_YEARLY;
        } else if (newPlanType === 'Daily') {
            priceId = PRICE_ID_DAILY;
        } else {
            console.error(`Invalid plan type for prorated payment: ${newPlanType}`);
            throw new Error(`Invalid plan type: ${newPlanType}`);
        }

        try {
            // Get current subscription details for debugging
            const currentSubscription = await stripe.subscriptions.retrieve(currentSubscriptionId);
            console.log(`Current subscription before update: Plan=${currentSubscription.items.data[0].price.id}, Status=${currentSubscription.status}`);

            // Update the existing subscription with the new plan
            const updatedSubscription = await stripe.subscriptions.update(currentSubscriptionId, {
                items: [
                    {
                        id: currentSubscription.items.data[0].id,
                        price: priceId,
                    },
                ],
                metadata: {
                    userId: transaction.userId,
                    domainId: transaction.domainId,
                    planType: newPlanType,
                    plan: newPlanType, // Add this for Stripe dashboard display
                    isProrated: 'true',
                    previousPlanType: domain.subscription.planType,
                    updatedAt: new Date().toISOString()
                },
                proration_behavior: 'create_prorations'
            });

            console.log(`Updated subscription: Plan=${updatedSubscription.items.data[0].price.id}, Status=${updatedSubscription.status}`);

            console.log(`Updated subscription ${currentSubscriptionId} with new plan ${newPlanType} for domain ${transaction.domainId}`);

            // Log the full subscription details to debug what Stripe sees
            console.log('Full updated subscription details:', JSON.stringify({
                id: updatedSubscription.id,
                status: updatedSubscription.status,
                metadata: updatedSubscription.metadata,
                items: updatedSubscription.items.data.map(item => ({
                    id: item.id,
                    priceId: item.price.id,
                    priceName: item.price.nickname,
                    productId: item.price.product
                }))
            }, null, 2));

            // Calculate credits based on the new plan type
            const credits = newPlanType === 'Daily' ? 1 :
                newPlanType === 'Monthly' ? 30 :
                    newPlanType === 'Yearly' ? 365 : 0;

            // Update domain subscription (keeping the same subscription ID)
            await Domain.findByIdAndUpdate(transaction.domainId, {
                subscription: {
                    active: true,
                    planType: newPlanType,
                    credits: credits,
                    stripeCustomerId: domain.subscription.stripeCustomerId,
                    stripeSubscriptionId: currentSubscriptionId, // Keep the same subscription ID
                    updatedAt: new Date(),
                    canceledAt: domain.subscription?.canceledAt,
                    expiresAt: domain.subscription?.expiresAt
                }
            });
            console.log(`Domain ${transaction.domainId} subscription updated after prorated payment with same subscription ID`);

            // Update user plan
            const planFeatures = newPlanType === 'Monthly'
                ? { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true }
                : { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true };

            await Plan.findOneAndUpdate(
                { userId: transaction.userId },
                {
                    type: newPlanType === 'Monthly' ? 'Pro' : 'Enterprise',
                    displayType: newPlanType,
                    features: planFeatures,
                    startDate: new Date(),
                    endDate: newPlanType === 'Monthly'
                        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days
                        : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),  // 365 days
                    status: 'active'
                },
                { upsert: true }
            );

            console.log(`Plan updated for user ${transaction.userId} after prorated payment`);

            // Update the transaction to reflect that subscription ID remains same
            transaction.metadata = {
                ...transaction.metadata,
                subscriptionUpdated: true,
                subscriptionUpdatedAt: new Date(),
                planChangeCompleted: true,
                maintainedSameSubscriptionId: true
            };
            await transaction.save();
            console.log(`Transaction ${transaction._id} updated - subscription ID maintained: ${currentSubscriptionId}`);

        } catch (stripeError) {
            console.error(`Error updating subscription ${currentSubscriptionId}:`, stripeError);

            // Fallback to the old cancel+create method if update fails
            console.log('Falling back to cancel+create method due to stripe error');

            try {
                await stripe.subscriptions.cancel(currentSubscriptionId);
                console.log(`Cancelled old subscription ${currentSubscriptionId} as fallback`);

                const newSubscription = await stripe.subscriptions.create({
                    customer: domain.subscription.stripeCustomerId,
                    items: [
                        {
                            price: priceId,
                        },
                    ],
                    metadata: {
                        userId: transaction.userId,
                        domainId: transaction.domainId,
                        planType: newPlanType
                    }
                });

                console.log(`Created new subscription ${newSubscription.id} as fallback for domain ${transaction.domainId}`);

                // Update domain with new subscription ID
                await Domain.findByIdAndUpdate(transaction.domainId, {
                    subscription: {
                        active: true,
                        planType: newPlanType,
                        credits: newPlanType === 'Daily' ? 1 : newPlanType === 'Monthly' ? 30 : newPlanType === 'Yearly' ? 365 : 0,
                        stripeCustomerId: domain.subscription.stripeCustomerId,
                        stripeSubscriptionId: newSubscription.id,
                        updatedAt: new Date(),
                        canceledAt: domain.subscription?.canceledAt,
                        expiresAt: domain.subscription?.expiresAt
                    }
                });

                transaction.stripeSubscriptionId = newSubscription.id;
                transaction.metadata = {
                    ...transaction.metadata,
                    newSubscriptionId: newSubscription.id,
                    subscriptionUpdatedAt: new Date(),
                    planChangeCompleted: true,
                    fallbackUsed: true
                };
                await transaction.save();
                console.log(`Fallback completed - new subscription ID: ${newSubscription.id}`);
            } catch (fallbackError) {
                console.error(`Fallback also failed for transaction ${transaction._id}:`, fallbackError);
            }
        }
    } catch (error) {
        console.error(`Error processing prorated payment for transaction ${transaction._id}:`, error);
    }
}

// Function to handle customer.subscription.updated event
async function handleSubscriptionUpdated(event) {
    const subscription = event.data.object;
    console.log(`Processing customer.subscription.updated: ${subscription.id}`);

    try {
        // Skip if no metadata (we need userId and domainId)
        if (!subscription.metadata || !subscription.metadata.userId || !subscription.metadata.domainId) {
            console.log(`Subscription ${subscription.id} has no metadata or missing user/domain IDs, skipping`);
            return;
        }

        const userId = subscription.metadata.userId;
        const domainId = subscription.metadata.domainId;
        const planType = subscription.metadata.planType;

        // Find the user and domain
        const user = await User.findById(userId);
        const domain = await Domain.findById(domainId);

        if (!user || !domain) {
            console.log(`User or domain not found for subscription ${subscription.id}, skipping`);
            return;
        }

        // Update domain subscription with latest data from Stripe
        const isActive = subscription.status === 'active' || subscription.status === 'trialing';
        const currentPlan = subscription.metadata.planType || subscription.items.data?.[0]?.price?.metadata?.planType;

        if (currentPlan) {
            const credits = currentPlan === 'Daily' ? 1 :
                currentPlan === 'Monthly' ? 30 :
                    currentPlan === 'Yearly' ? 365 : 0;

            // Update domain with current subscription data
            await Domain.findByIdAndUpdate(domainId, {
                'subscription.active': isActive,
                'subscription.planType': isActive ? currentPlan : 'Free',
                'subscription.credits': isActive ? credits : 0,
                'subscription.updatedAt': new Date(),
                'subscription.canceledAt': subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : domain.subscription?.canceledAt,
                'subscription.expiresAt': subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : domain.subscription?.expiresAt
            });
            console.log(`Updated domain ${domainId} subscription data: active=${isActive}, plan=${currentPlan}, credits=${isActive ? credits : 0}`);
        }

        // Check if this is a plan change by looking at the previous attributes
        if (event.data.previous_attributes && event.data.previous_attributes.items) {
            const previousPlan = event.data.previous_attributes.items.data?.[0]?.price?.metadata?.planType;

            if (previousPlan && currentPlan && previousPlan !== currentPlan) {
                console.log(`Plan changed from ${previousPlan} to ${currentPlan} for subscription ${subscription.id}`);

                // Create a transaction record if it doesn't exist
                let transaction = await Transaction.findOne({
                    stripeSubscriptionId: subscription.id,
                    transactionType: 'Plan Change'
                });

                if (!transaction) {
                    transaction = new Transaction({
                        userId: userId,
                        domainId: domainId,
                        stripeSubscriptionId: subscription.id,
                        stripeCustomerId: subscription.customer,
                        transactionType: 'Plan Change',
                        planType: currentPlan,
                        status: 'completed',
                        amount: currentPlan === 'Monthly' ? 59.99 : 159.99,
                        currency: 'usd',
                        subscriptionPeriod: {
                            startDate: subscription.current_period_start ? new Date(subscription.current_period_start * 1000) : null,
                            endDate: subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null,
                            nextPaymentDate: subscription.status === 'active' ?
                                (subscription.current_period_end ? new Date(subscription.current_period_end * 1000) : null) : null
                        },
                        metadata: {
                            previousPlanType: previousPlan,
                            newPlanType: currentPlan,
                            updatedAt: new Date()
                        }
                    });
                    await transaction.save();
                    console.log(`Created plan change transaction: ${transaction._id}`);
                }

                // Send plan change email
                try {
                    // Prepare proration details
                    const proratedDetails = {
                        currentPlanType: previousPlan || 'previous plan',
                        newPlanType: currentPlan,
                        originalAmount: currentPlan === 'Monthly' ? 59.99 : 159.99,
                        unusedCredit: 0, // We don't have this info in the webhook
                        finalAmount: currentPlan === 'Monthly' ? 59.99 : 159.99,
                        invoiceUrl: null // We don't have this info in the webhook
                    };

                    // Send email
                    await emailService.sendPlanProrationEmail(user, proratedDetails);
                    console.log(`Plan change email sent to ${user.email}`);
                } catch (emailError) {
                    console.error('Error sending plan change email:', emailError);
                }
            }
        }
    } catch (error) {
        console.error(`Error processing subscription update: ${error.message}`);
    }
}

// Function to handle customer.subscription.deleted event
async function handleSubscriptionDeleted(event) {
    const subscription = event.data.object;
    console.log(`Processing customer.subscription.deleted: ${subscription.id}`);

    try {
        // Find the domain with this subscription ID
        const domain = await Domain.findOne({ 'subscription.stripeSubscriptionId': subscription.id });
        if (!domain) {
            console.log(`No domain found for subscription ID: ${subscription.id}`);
            return;
        }

        // Update domain subscription status
        await Domain.findByIdAndUpdate(domain._id, {
            'subscription.active': false,
            'subscription.planType': 'Free',
            'subscription.credits': 0,
            'subscription.canceledAt': new Date(),
            'subscription.updatedAt': new Date()
        });
        console.log(`Domain ${domain._id} subscription marked as inactive`);

        // Update user plan to free
        await Plan.findOneAndUpdate(
            { userId: domain.userId },
            {
                type: 'Free',
                displayType: 'Free',
                features: {
                    maxDomains: 1,
                    maxArticlesPerMonth: 10,
                    customBranding: false
                },
                startDate: new Date(),
                endDate: null,
                status: 'active'
            },
            { upsert: true }
        );
        console.log(`User ${domain.userId} plan downgraded to Free`);

        // Create a record of the cancellation if it doesn't exist
        let transaction = await Transaction.findOne({
            stripeSubscriptionId: subscription.id,
            transactionType: 'Subscription Cancellation'
        });

        if (!transaction) {
            transaction = new Transaction({
                userId: domain.userId,
                domainId: domain._id,
                stripeSessionId: `webhook_cancel_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`, // Generate a unique dummy session ID for webhook cancellations
                stripeSubscriptionId: subscription.id,
                stripeCustomerId: domain.subscription.stripeCustomerId,
                transactionType: 'Subscription Cancellation',
                planType: domain.subscription.planType || 'Premium',
                status: 'completed',
                amount: 0,
                currency: 'usd',
                metadata: {
                    canceledAt: new Date(),
                    cancelImmediately: true,
                    previousPlan: domain.subscription.planType || 'Premium'
                }
            });
            await transaction.save();
            console.log(`Created cancellation transaction: ${transaction._id}`);
        }

        // Send cancellation email
        try {
            const user = await User.findById(domain.userId);
            if (user) {
                console.log(`Found user for webhook cancellation email: ${user.email}`);

                // Try sending email directly with nodemailer first
                try {
                    console.log('Attempting to send webhook cancellation email directly with nodemailer');
                    const transporter = nodemailer.createTransport({
                        service: 'gmail',
                        auth: {
                            user: process.env.EMAIL_USER || '<EMAIL>',
                            pass: process.env.EMAIL_APP_PASSWORD || 'eewnuizuylfddyml'
                        }
                    });

                    const name = user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;
                    const planType = domain.subscription.planType || 'Premium';
                    const date = new Date().toLocaleDateString();

                    // Professional email template matching other templates
                    const emailHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Subscription Cancellation</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body style="
  margin: 0; 
  padding: 0; 
  background-color: #fff7ed; 
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
">
  
  <!-- Email Container -->
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #fff7ed;">
    <tr>
      <td style="padding: 40px 20px;">
        
        <!-- Main Content Card -->
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="
          max-width: 600px; 
          width: 100%; 
          margin: 0 auto; 
          background-color: #ffffff; 
          border-radius: 16px; 
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          overflow: hidden;
        ">
          
          <!-- Header -->
          <tr>
            <td style="
              background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
              padding: 40px 40px 32px;
              text-align: center;
            ">
              
              <h1 style="
                color: #ffffff; 
                font-size: 32px; 
                font-weight: 700; 
                margin: 0 0 8px 0; 
                letter-spacing: -0.02em;
              ">Subscription Cancelled</h1>
              
              <p style="
                color: rgba(255, 255, 255, 0.9); 
                font-size: 16px; 
                margin: 0; 
                font-weight: 400;
              ">We're sorry to see you go</p>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td style="padding: 48px 40px;">
              
              <p style="
                color: #111827; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
              ">Hi ${name},</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">We've received your request to cancel your <strong style="color: #ea580c;">${planType} Plan</strong> subscription. Your subscription has been cancelled immediately and your plan has been downgraded to Free.</p>
              
              <!-- Cancellation Details -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="
                margin-bottom: 32px;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                overflow: hidden;
              ">
                <tr style="background-color: #f9fafb;">
                  <td style="padding: 16px; border-bottom: 1px solid #e5e7eb;">
                    <p style="margin: 0; font-weight: 600; color: #111827;">Cancellation Details</p>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 16px;">
                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                      <tr>
                        <td style="padding-bottom: 8px;"><p style="margin: 0; color: #6b7280;">Cancelled Plan:</p></td>
                        <td style="padding-bottom: 8px; text-align: right;"><p style="margin: 0; font-weight: 500; color: #111827;">${planType}</p></td>
                      </tr>
                      <tr>
                        <td style="padding-bottom: 8px;"><p style="margin: 0; color: #6b7280;">Cancellation Date:</p></td>
                        <td style="padding-bottom: 8px; text-align: right;"><p style="margin: 0; font-weight: 500; color: #111827;">${date}</p></td>
                      </tr>
                      <tr>
                        <td style="padding-bottom: 8px;"><p style="margin: 0; color: #6b7280;">Access Until:</p></td>
                        <td style="padding-bottom: 8px; text-align: right;"><p style="margin: 0; font-weight: 500; color: #111827;">Immediately</p></td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">We're sorry to see you go. If you change your mind, you can resubscribe at any time to regain access to all premium features. We'd love to have you back!</p>
              
              <p style="
                color: #6b7280; 
                font-size: 16px; 
                margin: 0 0 24px 0; 
                font-weight: 400;
                line-height: 1.6;
              ">If you have any feedback on how we could improve our service, please reply to this email. We value your input.</p>
              
              <!-- Footer -->
              <tr>
                <td style="
                  background-color: #fff7ed; 
                  border-top: 1px solid #fed7aa; 
                  padding: 32px 40px; 
                  text-align: center;
                ">
                  <p style="
                    color: #64748b; 
                    font-size: 16px; 
                    margin: 0; 
                    font-weight: 400;
                  ">Thanks,<br><strong style="color: #ea580c;">The BlogBuster Team</strong></p>
                  
                  <!-- Copyright -->
                  <p style="
                    color: #9ca3af; 
                    font-size: 12px; 
                    margin: 24px 0 0 0;
                  ">© ${new Date().getFullYear()} BlogBuster. All rights reserved.</p>
                </td>
              </tr>
              
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>`;

                    await transporter.sendMail({
                        from: `"BlogBuster" <${process.env.EMAIL_USER || '<EMAIL>'}>`,
                        to: user.email,
                        subject: 'Your BlogBuster Subscription Cancellation',
                        html: emailHtml
                    });

                    console.log(`Direct webhook cancellation email sent to ${user.email}`);
                } catch (directEmailError) {
                    console.error('Error sending direct webhook cancellation email:', directEmailError);

                    // Fall back to the email service
                    const cancellationDetails = {
                        planType: domain.subscription.planType || 'Premium',
                        cancelImmediately: true,
                        expiresAt: new Date()
                    };

                    const emailSent = await emailService.sendPlanCancellationEmail(user, cancellationDetails);
                    console.log(`Webhook cancellation email sent via service to ${user.email}, result: ${emailSent}`);

                    if (!emailSent) {
                        console.log('Email service reported failure in webhook, will retry once');
                        // Retry once with a delay
                        setTimeout(async () => {
                            try {
                                const retryResult = await emailService.sendPlanCancellationEmail(user, cancellationDetails);
                                console.log(`Retry sending webhook cancellation email to ${user.email}, result: ${retryResult}`);
                            } catch (retryError) {
                                console.error('Error in retry sending webhook cancellation email:', retryError);
                            }
                        }, 2000);
                    }
                }
            } else {
                console.error(`User not found for webhook cancellation email, ID: ${domain.userId}`);
            }
        } catch (emailError) {
            console.error('Error sending webhook cancellation email:', emailError);
        }
    } catch (error) {
        console.error(`Error processing subscription deletion: ${error.message}`);
    }
}

function handleWebhook(req, res) {
    const sig = req.headers['stripe-signature'];
    let event;

    try {
        event = stripe.webhooks.constructEvent(
            req.body,
            sig,
            process.env.STRIPE_WEBHOOK_SECRET
        );
    } catch (err) {
        console.error(`Webhook Error: ${err.message}`);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Handle the event
    (async () => {
        try {
            logWebhookEvent(event.type, event.data.object);

            // Check if this is a prorated payment event
            const isProrated =
                event.data.object.metadata?.isProrated === 'true' ||
                (event.type === 'checkout.session.completed' &&
                    event.data.object.mode === 'payment' &&
                    event.data.object.metadata?.isProrated === 'true');

            if (isProrated) {
                console.log(`Received prorated payment event: ${event.type}`);
            }

            switch (event.type) {
                case 'checkout.session.completed':
                    console.log(`Processing checkout.session.completed: ${event.data.object.id}`);
                    await handleCheckoutSessionCompleted(event);
                    break;
                case 'invoice.payment_succeeded':
                    console.log(`Processing invoice.payment_succeeded: ${event.data.object.id}`);
                    await handleInvoicePaymentSucceeded(event);
                    break;
                case 'payment_intent.succeeded':
                    console.log(`Processing payment_intent.succeeded: ${event.data.object.id}`);
                    await handlePaymentIntentSucceeded(event);
                    break;
                case 'customer.subscription.updated':
                    console.log(`Processing customer.subscription.updated: ${event.data.object.id}`);
                    await handleSubscriptionUpdated(event);
                    break;
                case 'customer.subscription.deleted':
                    console.log(`Processing customer.subscription.deleted: ${event.data.object.id}`);
                    await handleSubscriptionDeleted(event);
                    break;
                default:
                    console.log(`Unhandled event type: ${event.type}`);
            }
        } catch (error) {
            console.error(`Error processing webhook event ${event.type}:`, error);
            // We don't want to send an error response to Stripe as it will retry
            // Just log the error and continue
        }
    })();

    // Return a 200 response to acknowledge receipt of the event
    res.send({ received: true });
}

// Helper function to ensure a value is a valid number
const safeNumber = (value) => {
    const num = Number(value);
    return !isNaN(num) && isFinite(num) ? num : 0;
};

// Helper function to safely create dates from Stripe timestamps
const safeDateFromTimestamp = (timestamp, fallbackDate = new Date()) => {
    if (!timestamp) return fallbackDate;
    const date = new Date(timestamp * 1000);
    return isNaN(date.getTime()) ? fallbackDate : date;
};

// Calculate prorated amount when changing between plans
router.get('/calculate-proration', protect, async (req, res) => {
    try {
        const { domainId, newPlanType } = req.query;

        if (!domainId || !newPlanType) {
            return res.status(400).json({ error: 'Domain ID and new plan type are required' });
        }

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Check if domain has an active subscription
        if (!domain.subscription?.active || !domain.subscription?.stripeSubscriptionId) {
            return res.status(400).json({
                error: 'No active subscription found for this domain',
                message: 'This domain does not have an active subscription to prorate.'
            });
        }

        // Get current subscription from Stripe
        const subscription = await stripe.subscriptions.retrieve(domain.subscription.stripeSubscriptionId);

        // Get plan details to determine subscription period
        const plan = await Plan.findOne({ userId: req.user.id });

        // Calculate remaining days in the current billing period using safe date handling
        const now = new Date();
        const currentPeriodEnd = safeDateFromTimestamp(subscription.current_period_end, new Date(Date.now() + 30 * 24 * 60 * 60 * 1000));
        const currentPeriodStart = safeDateFromTimestamp(subscription.current_period_start, new Date(Date.now() - 1 * 24 * 60 * 60 * 1000));

        // Log what we got from Stripe
        console.log(`DEBUG CALCULATE-PRORATION: Stripe subscription timestamps:`, {
            current_period_start: subscription.current_period_start,
            current_period_end: subscription.current_period_end,
            status: subscription.status
        });

        const remainingDays = safeNumber(Math.ceil((currentPeriodEnd - now) / (1000 * 60 * 60 * 24)));

        console.log(`DEBUG CALCULATE-PRORATION: Stripe subscription period details:`);
        console.log(`  Current period start: ${currentPeriodStart.toISOString()}`);
        console.log(`  Current period end: ${currentPeriodEnd.toISOString()}`);
        console.log(`  Current time: ${now.toISOString()}`);
        console.log(`  Remaining days: ${remainingDays}`);

        // Use the actual billing period from Stripe subscription (most accurate)
        const totalDays = safeNumber(Math.ceil((currentPeriodEnd - currentPeriodStart) / (1000 * 60 * 60 * 24)));
        console.log(`Using Stripe billing period: ${totalDays} days (${currentPeriodStart.toISOString()} to ${currentPeriodEnd.toISOString()})`);

        // Calculate prorated amount based on remaining days
        const currentPlanPrice = safeNumber(domain.subscription.planType === 'Monthly' ? 59.99 : 159.99);
        const newPlanPrice = safeNumber(newPlanType === 'Monthly' ? 59.99 : 159.99);

        // Calculate credit for unused portion of current subscription
        const unusedCredit = safeNumber((remainingDays / totalDays) * currentPlanPrice);
        console.log(`Unused credit calculation: (${remainingDays} / ${totalDays}) * ${currentPlanPrice} = ${unusedCredit}`);

        // If downgrading from yearly to monthly
        if (domain.subscription.planType === 'Yearly' && newPlanType === 'Monthly') {
            // Calculate cost for new monthly plan
            const monthlyPlanCost = safeNumber(newPlanPrice);

            // If credit exceeds new plan cost, no charge needed
            if (unusedCredit >= monthlyPlanCost) {
                return res.json({
                    amount: 0,
                    currency: 'usd',
                    details: {
                        originalAmount: monthlyPlanCost,
                        discount: monthlyPlanCost,
                        finalAmount: 0,
                        remainingDays: remainingDays,
                        totalDays: totalDays,
                        currentPlanPrice: currentPlanPrice,
                        newPlanPrice: monthlyPlanCost,
                        unusedCredit: unusedCredit,
                        currentPlanType: domain.subscription.planType,
                        newPlanType: newPlanType,
                        subscriptionStart: plan?.startDate || null,
                        subscriptionEnd: plan?.endDate || null
                    }
                });
            } else {
                // Charge the difference
                const finalAmount = safeNumber(Math.max(0, monthlyPlanCost - unusedCredit));
                return res.json({
                    amount: finalAmount,
                    currency: 'usd',
                    details: {
                        originalAmount: monthlyPlanCost,
                        discount: unusedCredit,
                        finalAmount: finalAmount,
                        remainingDays: remainingDays,
                        totalDays: totalDays,
                        currentPlanPrice: currentPlanPrice,
                        newPlanPrice: monthlyPlanCost,
                        unusedCredit: unusedCredit,
                        currentPlanType: domain.subscription.planType,
                        newPlanType: newPlanType,
                        subscriptionStart: plan?.startDate || null,
                        subscriptionEnd: plan?.endDate || null
                    }
                });
            }
        }

        // If upgrading from monthly to yearly
        if (domain.subscription.planType === 'Monthly' && newPlanType === 'Yearly') {
            // Calculate cost for new yearly plan
            const yearlyPlanCost = safeNumber(newPlanPrice);

            // Charge the difference
            const finalAmount = safeNumber(Math.max(0, yearlyPlanCost - unusedCredit));
            return res.json({
                amount: finalAmount,
                currency: 'usd',
                details: {
                    originalAmount: yearlyPlanCost,
                    discount: unusedCredit,
                    finalAmount: finalAmount,
                    remainingDays: remainingDays,
                    totalDays: totalDays,
                    currentPlanPrice: currentPlanPrice,
                    newPlanPrice: yearlyPlanCost,
                    unusedCredit: unusedCredit,
                    currentPlanType: domain.subscription.planType,
                    newPlanType: newPlanType,
                    subscriptionStart: plan?.startDate || null,
                    subscriptionEnd: plan?.endDate || null
                }
            });
        }

        // If changing to the same plan type, no proration needed
        return res.json({
            amount: 0,
            currency: 'usd',
            details: {
                originalAmount: safeNumber(newPlanPrice),
                discount: safeNumber(newPlanPrice),
                finalAmount: 0,
                remainingDays: safeNumber(remainingDays),
                totalDays: safeNumber(totalDays),
                currentPlanPrice: currentPlanPrice,
                newPlanPrice: newPlanPrice,
                unusedCredit: currentPlanPrice,
                currentPlanType: domain.subscription.planType,
                newPlanType: newPlanType,
                subscriptionStart: plan?.startDate || null,
                subscriptionEnd: plan?.endDate || null
            }
        });
    } catch (error) {
        console.error('Error calculating prorated amount:', error);
        // Return a safe default response even in case of error
        res.json({
            amount: 0,
            currency: 'usd',
            details: {
                originalAmount: 0,
                discount: 0,
                finalAmount: 0,
                remainingDays: 0,
                totalDays: 30,
                currentPlanPrice: 0,
                newPlanPrice: 0,
                unusedCredit: 0,
                currentPlanType: 'Unknown',
                newPlanType: 'Unknown',
                subscriptionStart: null,
                subscriptionEnd: null
            },
            error: error.message
        });
    }
});

// Create a checkout session with prorated amount for plan changes
router.post('/create-prorated-checkout', protect, async (req, res) => {
    try {
        console.log('Creating prorated checkout session with body:', req.body);
        const { domainId, newPlanType } = req.body;

        if (!domainId || !newPlanType) {
            console.log('Missing required parameters:', { domainId, newPlanType });
            return res.status(400).json({ error: 'Domain ID and new plan type are required' });
        }

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            console.log(`Domain not found or not authorized: ${domainId} for user ${req.user.id}`);
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Check if domain has an active subscription
        if (!domain.subscription?.active || !domain.subscription?.stripeSubscriptionId) {
            console.log(`No active subscription found for domain ${domainId}`);
            return res.status(400).json({
                error: 'No active subscription found for this domain',
                message: 'This domain does not have an active subscription to update.'
            });
        }

        console.log(`Processing prorated checkout for domain ${domainId}, current plan: ${domain.subscription.planType}, new plan: ${newPlanType}`);

        // Get current subscription from Stripe
        let subscription;
        try {
            subscription = await stripe.subscriptions.retrieve(domain.subscription.stripeSubscriptionId);
            console.log(`Retrieved subscription from Stripe: ${subscription.id}`);
            console.log(`Subscription status: ${subscription.status}`);
            console.log(`Subscription billing details:`, {
                current_period_start: subscription.current_period_start,
                current_period_end: subscription.current_period_end,
                status: subscription.status,
                cancel_at_period_end: subscription.cancel_at_period_end
            });
        } catch (stripeError) {
            console.error('Error retrieving subscription from Stripe:', stripeError);
            return res.status(500).json({
                error: 'Failed to retrieve subscription details from Stripe',
                message: stripeError.message
            });
        }

        // Get plan details to determine subscription period
        const plan = await Plan.findOne({ userId: req.user.id });

        // Calculate remaining days in the current billing period using safe date handling
        const now = new Date();
        const currentPeriodEnd = safeDateFromTimestamp(subscription.current_period_end, new Date(Date.now() + 30 * 24 * 60 * 60 * 1000));
        const currentPeriodStart = safeDateFromTimestamp(subscription.current_period_start, new Date(Date.now() - 1 * 24 * 60 * 60 * 1000));

        // Log what we got from Stripe
        console.log(`DEBUG CREATE-PRORATED-CHECKOUT: Stripe subscription timestamps:`, {
            current_period_start: subscription.current_period_start,
            current_period_end: subscription.current_period_end,
            status: subscription.status
        });

        const remainingDays = safeNumber(Math.ceil((currentPeriodEnd - now) / (1000 * 60 * 60 * 24)));

        console.log(`DEBUG CREATE-PRORATED-CHECKOUT: Stripe subscription period details:`);
        console.log(`  Current period start: ${currentPeriodStart.toISOString()}`);
        console.log(`  Current period end: ${currentPeriodEnd.toISOString()}`);
        console.log(`  Current time: ${now.toISOString()}`);
        console.log(`  Remaining days: ${remainingDays}`);

        // Use the actual billing period from Stripe subscription (most accurate)  
        const totalDays = safeNumber(Math.ceil((currentPeriodEnd - currentPeriodStart) / (1000 * 60 * 60 * 24)));
        console.log(`Using Stripe billing period: ${totalDays} days (${currentPeriodStart.toISOString()} to ${currentPeriodEnd.toISOString()})`);

        console.log(`Billing period: ${remainingDays} days remaining out of ${totalDays} total days`);

        // Calculate prorated amount based on remaining days
        let currentPlanPrice;
        if (domain.subscription.planType === 'Monthly') {
            currentPlanPrice = safeNumber(59.99);
        } else if (domain.subscription.planType === 'Yearly') {
            currentPlanPrice = safeNumber(159.99);
        } else if (domain.subscription.planType === 'Daily') {
            currentPlanPrice = safeNumber(9.99);
        } else {
            currentPlanPrice = safeNumber(0); // Free plan
        }

        let newPlanPrice;
        if (newPlanType === 'Monthly') {
            newPlanPrice = safeNumber(59.99);
        } else if (newPlanType === 'Yearly') {
            newPlanPrice = safeNumber(159.99);
        } else if (newPlanType === 'Daily') {
            newPlanPrice = safeNumber(9.99);
        } else {
            newPlanPrice = safeNumber(0); // Free plan
        }

        // Set price ID based on new plan type
        let priceId;
        if (newPlanType === 'Monthly') {
            priceId = PRICE_ID_MONTHLY;
        } else if (newPlanType === 'Yearly') {
            priceId = PRICE_ID_YEARLY;
        } else if (newPlanType === 'Daily') {
            priceId = PRICE_ID_DAILY;
        } else {
            console.log(`Invalid plan type: ${newPlanType}`);
            return res.status(400).json({ error: 'Invalid plan type' });
        }

        // Calculate credit for unused portion of current subscription
        const unusedCredit = safeNumber((remainingDays / totalDays) * currentPlanPrice);
        console.log(`Unused credit calculation: (${remainingDays} / ${totalDays}) * ${currentPlanPrice} = ${unusedCredit}`);

        // Calculate prorated amount
        let proratedAmount = 0;
        let discount = 0;

        if (domain.subscription.planType === 'Yearly' && (newPlanType === 'Monthly' || newPlanType === 'Daily')) {
            // Calculate cost for new plan
            const newPlanCost = safeNumber(newPlanPrice);
            // If credit exceeds new plan cost, no charge needed
            if (unusedCredit >= newPlanCost) {
                proratedAmount = 0;
                discount = newPlanCost;
            } else {
                // Charge the difference
                proratedAmount = safeNumber(Math.max(0, newPlanCost - unusedCredit));
                discount = unusedCredit;
            }
        } else if (domain.subscription.planType === 'Monthly' && newPlanType === 'Yearly') {
            // Calculate cost for new yearly plan
            const yearlyPlanCost = safeNumber(newPlanPrice);
            // Charge the difference
            proratedAmount = safeNumber(Math.max(0, yearlyPlanCost - unusedCredit));
            discount = unusedCredit;
        } else if (domain.subscription.planType === 'Monthly' && newPlanType === 'Daily') {
            // Calculate cost for new daily plan
            const dailyPlanCost = safeNumber(newPlanPrice);
            // If credit exceeds new plan cost, no charge needed
            if (unusedCredit >= dailyPlanCost) {
                proratedAmount = 0;
                discount = dailyPlanCost;
            } else {
                // Charge the difference
                proratedAmount = safeNumber(Math.max(0, dailyPlanCost - unusedCredit));
                discount = unusedCredit;
            }
        } else if (domain.subscription.planType === 'Daily' && (newPlanType === 'Monthly' || newPlanType === 'Yearly')) {
            // Calculate cost for new plan
            const newPlanCost = safeNumber(newPlanPrice);
            // Daily plan has minimal credit, likely charge full amount
            proratedAmount = safeNumber(Math.max(0, newPlanCost - unusedCredit));
            discount = unusedCredit;
        }

        console.log(`Calculated prorated amount: ${proratedAmount}, discount: ${discount}, unused credit: ${unusedCredit}`);
        console.log(`Subscription details - Start: ${plan?.startDate || 'N/A'}, End: ${plan?.endDate || 'N/A'}`);
        console.log(`Pricing breakdown: Current plan (${domain.subscription.planType}): $${currentPlanPrice}, New plan (${newPlanType}): $${newPlanPrice}`);
        console.log(`User will pay: $${proratedAmount.toFixed(2)} (Original: $${newPlanPrice.toFixed(2)} - Credit: $${unusedCredit.toFixed(2)})`);

        // If no proration needed (same plan type), return error
        if (domain.subscription.planType === newPlanType) {
            console.log(`No plan change detected: current and new plan types are both ${newPlanType}`);
            return res.status(400).json({
                error: 'No plan change detected',
                message: 'You are already subscribed to this plan type.'
            });
        }

        // If prorated amount is 0, handle the plan change directly without creating a checkout session
        if (proratedAmount === 0) {
            console.log('Prorated amount is 0, handling plan change directly without checkout');
            try {
                // Update the existing subscription instead of canceling and creating new one
                const currentSubscriptionId = domain.subscription.stripeSubscriptionId;

                // Get current subscription details for debugging
                const currentSubscription = await stripe.subscriptions.retrieve(currentSubscriptionId);
                console.log(`[ZERO-AMOUNT] Current subscription before update: Plan=${currentSubscription.items.data[0].price.id}, Status=${currentSubscription.status}`);

                // Update the existing subscription with the new plan
                const updatedSubscription = await stripe.subscriptions.update(currentSubscriptionId, {
                    items: [
                        {
                            id: currentSubscription.items.data[0].id,
                            price: priceId,
                        },
                    ],
                    metadata: {
                        userId: req.user.id,
                        domainId: domainId,
                        planType: newPlanType,
                        plan: newPlanType, // Add this for Stripe dashboard display
                        isProrated: 'true',
                        previousPlanType: domain.subscription.planType,
                        originalAmount: safeNumber(newPlanPrice).toFixed(2),
                        discount: safeNumber(discount).toFixed(2),
                        finalAmount: '0.00',
                        unusedCredit: safeNumber(unusedCredit).toFixed(2),
                        remainingDays: safeNumber(remainingDays),
                        totalDays: safeNumber(totalDays),
                        updatedAt: new Date().toISOString()
                    },
                    proration_behavior: 'create_prorations'
                });

                console.log(`[ZERO-AMOUNT] Updated subscription: Plan=${updatedSubscription.items.data[0].price.id}, Status=${updatedSubscription.status}`);
                console.log(`Updated subscription ${currentSubscriptionId} with new plan ${newPlanType}`);

                // Calculate credits based on the new plan type
                const newPlanCredits = newPlanType === 'Daily' ? 1 :
                    newPlanType === 'Monthly' ? 30 :
                        newPlanType === 'Yearly' ? 365 : 0;

                // Update domain subscription (keeping the same subscription ID)
                await Domain.findByIdAndUpdate(domainId, {
                    subscription: {
                        active: true,
                        planType: newPlanType,
                        credits: newPlanCredits,
                        stripeCustomerId: domain.subscription.stripeCustomerId,
                        stripeSubscriptionId: currentSubscriptionId, // Keep the same subscription ID
                        updatedAt: new Date(),
                        canceledAt: domain.subscription?.canceledAt,
                        expiresAt: domain.subscription?.expiresAt
                    }
                });
                console.log(`Credits updated from previous plan to ${newPlanCredits} credits for ${newPlanType} plan (zero-amount change)`);

                // Update user plan
                let planFeatures;
                let planType;
                let endDate;

                if (newPlanType === 'Monthly') {
                    planFeatures = { maxDomains: 3, maxArticlesPerMonth: 30, customBranding: true };
                    planType = 'Pro';
                    endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
                } else if (newPlanType === 'Yearly') {
                    planFeatures = { maxDomains: 5, maxArticlesPerMonth: 50, customBranding: true };
                    planType = 'Enterprise';
                    endDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 365 days
                } else if (newPlanType === 'Daily') {
                    planFeatures = { maxDomains: 2, maxArticlesPerMonth: 5, customBranding: true };
                    planType = 'Basic';
                    endDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day
                } else {
                    planFeatures = { maxDomains: 1, maxArticlesPerMonth: 10, customBranding: false };
                    planType = 'Free';
                    endDate = null;
                }

                await Plan.findOneAndUpdate(
                    { userId: req.user.id },
                    {
                        type: planType,
                        displayType: newPlanType,
                        features: planFeatures,
                        startDate: new Date(),
                        endDate: endDate,
                        status: 'active'
                    },
                    { upsert: true }
                );

                // Create a completed transaction record
                const transaction = new Transaction({
                    userId: req.user.id,
                    domainId: domainId,
                    stripeSubscriptionId: currentSubscriptionId, // Keep the same subscription ID
                    planType: newPlanType,
                    status: 'completed',
                    amount: 0,
                    currency: 'usd',
                    transactionType: 'Plan Change',
                    metadata: {
                        createdAt: new Date(),
                        isProrated: true,
                        previousPlanType: domain.subscription.planType,
                        originalAmount: safeNumber(newPlanPrice).toFixed(2),
                        discount: safeNumber(discount).toFixed(2),
                        finalAmount: '0.00',
                        unusedCredit: safeNumber(unusedCredit).toFixed(2),
                        remainingDays: safeNumber(remainingDays),
                        totalDays: safeNumber(totalDays),
                        automaticUpgrade: true,
                        maintainedSameSubscriptionId: true
                    }
                });

                try {
                    await transaction.save();
                    console.log('Zero-amount plan change transaction created:', transaction._id);
                } catch (validationError) {
                    console.error('Transaction validation error:', validationError);
                    // If there's a validation error, log it but don't fail the request
                    // The plan change is already completed, so we can continue
                    if (validationError.errors?.transactionType) {
                        console.error('Invalid transaction type. Please update the Transaction model enum values.');
                    }
                }

                // Return success with redirect URL
                return res.json({
                    success: true,
                    url: `${process.env.FRONTEND_URL}/dashboard/domain/${domainId}?payment_success=true&plan_type=${newPlanType}&prorated=true&automatic=true`
                });
            } catch (error) {
                console.error('Error processing zero-amount plan change:', error);
                return res.status(500).json({ error: error.message });
            }
        }

        // Create a checkout session for the prorated amount
        console.log('Creating Stripe checkout session for prorated amount');
        let session;
        try {
            session = await stripe.checkout.sessions.create({
                payment_method_types: ['card'],
                line_items: [
                    {
                        price_data: {
                            currency: 'usd',
                            product_data: {
                                name: `Upgrade to ${newPlanType} Plan (Prorated)`,
                                description: `Change from ${domain.subscription.planType} to ${newPlanType} plan with prorated adjustment. Original price: $${newPlanPrice}, Credit from current plan: $${unusedCredit.toFixed(2)}, Final payment: $${proratedAmount.toFixed(2)}`
                            },
                            unit_amount: Math.round(safeNumber(proratedAmount) * 100), // Convert to cents and ensure it's a valid number
                        },
                        quantity: 1,
                    },
                ],
                mode: 'payment', // One-time payment for proration
                success_url: `${process.env.FRONTEND_URL}/dashboard/domain/${domainId}?payment_success=true&plan_type=${newPlanType}&prorated=true`,
                cancel_url: `${process.env.FRONTEND_URL}/dashboard/plans`,
                client_reference_id: domainId,
                customer_email: req.user.email,
                metadata: {
                    userId: req.user.id,
                    domainId: domainId,
                    planType: newPlanType,
                    isProrated: 'true',
                    previousPlanType: domain.subscription.planType,
                    originalAmount: safeNumber(newPlanPrice).toFixed(2), // Full price of the new plan
                    discount: safeNumber(discount).toFixed(2),
                    finalAmount: safeNumber(proratedAmount).toFixed(2), // This is what user actually pays
                    unusedCredit: safeNumber(unusedCredit).toFixed(2),
                    remainingDays: String(safeNumber(remainingDays)),
                    totalDays: String(safeNumber(totalDays)),
                    currentPlanPrice: safeNumber(currentPlanPrice).toFixed(2),
                    newPlanPrice: safeNumber(newPlanPrice).toFixed(2)
                }
            });

            console.log('Stripe checkout session created:', session.id);
        } catch (stripeError) {
            console.error('Error creating Stripe checkout session:', stripeError);
            return res.status(500).json({
                error: 'Failed to create checkout session',
                message: stripeError.message
            });
        }

        // Create a pending transaction record
        try {
            const transaction = new Transaction({
                userId: req.user.id,
                domainId: domainId,
                stripeSessionId: session.id,
                stripeSubscriptionId: domain.subscription.stripeSubscriptionId, // Keep the existing subscription ID
                planType: newPlanType,
                status: 'pending',
                amount: safeNumber(proratedAmount),
                currency: 'usd',
                transactionType: 'Plan Change',
                metadata: {
                    sessionUrl: session.url,
                    createdAt: new Date(),
                    isProrated: true,
                    previousPlanType: domain.subscription.planType,
                    originalAmount: safeNumber(newPlanPrice).toFixed(2), // Full price of the new plan
                    discount: safeNumber(discount).toFixed(2),
                    finalAmount: safeNumber(proratedAmount).toFixed(2), // This is what user actually pays
                    unusedCredit: safeNumber(unusedCredit).toFixed(2),
                    remainingDays: safeNumber(remainingDays),
                    totalDays: safeNumber(totalDays),
                    currentPlanPrice: safeNumber(currentPlanPrice).toFixed(2),
                    newPlanPrice: safeNumber(newPlanPrice).toFixed(2)
                }
            });

            try {
                await transaction.save();
                console.log('Pending prorated transaction created:', transaction._id);
            } catch (validationError) {
                console.error('Transaction validation error:', validationError);
                // If there's a validation error, log it but don't fail the request
                // The checkout session is already created, so we can continue
                if (validationError.errors?.transactionType) {
                    console.error('Invalid transaction type. Please update the Transaction model enum values.');
                }
            }
        } catch (dbError) {
            console.error('Error creating transaction record:', dbError);
            // Continue even if transaction record creation fails
        }

        console.log('Returning checkout session URL:', session.url);
        res.json({ sessionId: session.id, url: session.url });
    } catch (error) {
        console.error('Error creating prorated checkout session:', error);
        res.status(500).json({ error: error.message });
    }
});

// Add this after the cancel-subscription route

// Manually sync subscription status with Stripe
router.post('/sync-subscription/:domainId', protect, async (req, res) => {
    try {
        const { domainId } = req.params;

        // Verify domain belongs to the user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        console.log(`Syncing subscription for domain ${domainId}`);

        // Special handling for Daily plans - check if it has expired naturally
        if (domain.subscription?.planType === 'Daily' && domain.subscription?.active) {
            console.log('Daily plan detected in sync endpoint, checking if it has expired naturally');

            // Calculate if 24 hours have passed since subscription was created/updated
            const subscriptionUpdated = domain.subscription.updatedAt || new Date();
            const currentTime = new Date();
            const hoursDifference = (currentTime - subscriptionUpdated) / (1000 * 60 * 60);

            if (hoursDifference >= 24) {
                console.log(`Daily plan has likely expired (${hoursDifference.toFixed(2)} hours since update)`);

                // Update domain to Free plan since Daily plan has expired
                const updatedDomain = await Domain.findByIdAndUpdate(domainId, {
                    'subscription.active': false,
                    'subscription.planType': 'Free',
                    'subscription.canceledAt': new Date(),
                    'subscription.expiresAt': new Date(),
                    'subscription.updatedAt': new Date()
                }, { new: true });

                // Create a record of the cancellation due to expiration
                const transaction = new Transaction({
                    userId: req.user.id,
                    domainId: domainId,
                    stripeSessionId: `expire_sync_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
                    stripeSubscriptionId: domain.subscription.stripeSubscriptionId,
                    stripeCustomerId: domain.subscription.stripeCustomerId,
                    transactionType: 'Subscription Cancellation',
                    planType: 'Daily',
                    status: 'completed',
                    amount: 0,
                    currency: 'usd',
                    metadata: {
                        canceledAt: new Date(),
                        expiresAt: new Date(),
                        previousPlan: 'Daily',
                        expiredNaturally: true,
                        syncExpired: true
                    }
                });

                await transaction.save();
                console.log('Expiration transaction recorded during sync:', transaction._id);

                return res.json({
                    success: true,
                    message: 'Daily plan has expired during sync. Your plan has been updated to Free.',
                    subscription: updatedDomain.subscription || {
                        active: false,
                        planType: 'Free',
                        updatedAt: new Date()
                    }
                });
            }
        }

        // If no subscription in our database, nothing to sync
        if (!domain.subscription || !domain.subscription.stripeSubscriptionId) {
            return res.json({
                success: true,
                message: 'No subscription to sync',
                subscription: {
                    active: false,
                    planType: 'Free',
                    updatedAt: new Date()
                }
            });
        }

        const subscriptionId = domain.subscription.stripeSubscriptionId;

        // Try to retrieve the subscription from Stripe
        try {
            const subscription = await stripe.subscriptions.retrieve(subscriptionId);
            console.log(`Retrieved subscription from Stripe: ${subscription.id}, status: ${subscription.status}`);

            // Update our database to match Stripe's status
            const isActive = subscription.status === 'active' || subscription.status === 'trialing';

            // Get plan type from metadata or default to current
            let planType = domain.subscription.planType;
            if (subscription.metadata && subscription.metadata.planType) {
                planType = subscription.metadata.planType;
            }

            // Update domain subscription
            await Domain.findByIdAndUpdate(domainId, {
                'subscription.active': isActive,
                'subscription.planType': isActive ? planType : 'Free',
                'subscription.updatedAt': new Date(),
                'subscription.canceledAt': subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : domain.subscription.canceledAt,
                'subscription.expiresAt': subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : domain.subscription.expiresAt
            });

            return res.json({
                success: true,
                message: `Subscription synced successfully. Status: ${subscription.status}`,
                subscription: {
                    active: isActive,
                    planType: isActive ? planType : 'Free',
                    stripeSubscriptionId: subscriptionId,
                    status: subscription.status,
                    updatedAt: new Date()
                }
            });
        } catch (stripeError) {
            console.error('Error retrieving subscription from Stripe:', stripeError);

            // If subscription doesn't exist in Stripe, update our records
            if (stripeError.code === 'resource_missing') {
                console.log('Subscription not found in Stripe, updating local records');

                // Update domain subscription
                const updatedDomain = await Domain.findByIdAndUpdate(
                    domainId,
                    {
                        'subscription.active': false,
                        'subscription.planType': 'Free',
                        'subscription.stripeSubscriptionId': null,
                        'subscription.canceledAt': new Date(),
                        'subscription.updatedAt': new Date()
                    },
                    { new: true }
                );

                return res.json({
                    success: true,
                    message: 'Subscription not found in Stripe. Local records updated.',
                    subscription: {
                        active: false,
                        planType: 'Free',
                        updatedAt: new Date()
                    }
                });
            }

            // For other errors, return the error
            return res.status(500).json({
                success: false,
                error: 'Failed to retrieve subscription from Stripe',
                message: stripeError.message
            });
        }
    } catch (error) {
        console.error('Error syncing subscription:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to sync subscription',
            message: error.message
        });
    }
});

// Export the router
// Test endpoint to verify prorated amount calculation
router.post('/debug/test-proration', protect, async (req, res) => {
    try {
        const { domainId, newPlanType } = req.body;

        if (!domainId || !newPlanType) {
            return res.status(400).json({ error: 'Domain ID and new plan type are required' });
        }

        // Get the proration calculation
        const response = await fetch(`${req.protocol}://${req.get('host')}/api/payments/calculate-proration?domainId=${domainId}&newPlanType=${newPlanType}`, {
            headers: {
                'Authorization': req.headers.authorization
            }
        });

        const prorationData = await response.json();

        res.json({
            success: true,
            prorationData,
            explanation: {
                currentPlan: prorationData.details?.currentPlanType,
                newPlan: prorationData.details?.newPlanType,
                originalPrice: `$${prorationData.details?.newPlanPrice}`,
                unusedCredit: `$${prorationData.details?.unusedCredit}`,
                finalAmount: `$${prorationData.details?.finalAmount}`,
                calculation: `$${prorationData.details?.newPlanPrice} - $${prorationData.details?.unusedCredit} = $${prorationData.details?.finalAmount}`
            }
        });
    } catch (error) {
        console.error('Error testing proration:', error);
        res.status(500).json({ error: error.message });
    }
});

// Test endpoint to manually trigger subscription update for testing
router.post('/debug/trigger-subscription-update', protect, async (req, res) => {
    try {
        const { transactionId } = req.body;

        if (!transactionId) {
            return res.status(400).json({ error: 'Transaction ID is required' });
        }

        const transaction = await Transaction.findById(transactionId);
        if (!transaction) {
            return res.status(404).json({ error: 'Transaction not found' });
        }

        if (transaction.userId.toString() !== req.user.id) {
            return res.status(403).json({ error: 'Not authorized to access this transaction' });
        }

        console.log(`[DEBUG] Manually triggering subscription update for transaction ${transactionId}`);

        await processCompletedProratedPayment(transaction);

        res.json({
            success: true,
            message: 'Subscription update triggered successfully',
            transactionId: transaction._id,
            planType: transaction.planType
        });
    } catch (error) {
        console.error('Error triggering subscription update:', error);
        res.status(500).json({ error: error.message });
    }
});

// Debug endpoint to check price IDs and their metadata
router.get('/debug/price-ids', protect, async (req, res) => {
    try {
        const priceIds = {
            Daily: PRICE_ID_DAILY,
            Monthly: PRICE_ID_MONTHLY,
            Yearly: PRICE_ID_YEARLY
        };

        const priceDetails = {};

        for (const [planType, priceId] of Object.entries(priceIds)) {
            if (priceId) {
                try {
                    const price = await stripe.prices.retrieve(priceId, {
                        expand: ['product']
                    });
                    priceDetails[planType] = {
                        id: price.id,
                        amount: price.unit_amount,
                        currency: price.currency,
                        interval: price.recurring?.interval,
                        productName: price.product.name,
                        productMetadata: price.product.metadata,
                        priceMetadata: price.metadata
                    };
                } catch (error) {
                    priceDetails[planType] = { error: error.message };
                }
            } else {
                priceDetails[planType] = { error: 'Price ID not configured' };
            }
        }

        res.json({
            priceIds,
            priceDetails,
            environment: process.env.NODE_ENV || 'development'
        });
    } catch (error) {
        console.error('Error fetching price details:', error);
        res.status(500).json({ error: error.message });
    }
});

module.exports = router; 