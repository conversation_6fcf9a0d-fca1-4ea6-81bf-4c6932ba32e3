@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&family=Plus+Jakarta+Sans:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&family=DM+Sans:wght@400;500;700&family=Manrope:wght@400;500;600;700&family=Space+Grotesk:wght@400;500;600;700&family=Cormorant+Garamond:wght@400;500;600;700&family=Spectral:wght@400;500;600;700&family=Fraunces:wght@400;500;600;700&family=Newsreader:wght@400;500;600;700&family=Comfortaa:wght@400;500;600;700&family=Josefin+Sans:wght@400;500;600;700&family=Lexend:wght@400;500;600;700&family=Outfit:wght@400;500;600;700&family=Sora:wght@400;500;600;700&family=Space+Mono:wght@400;700&family=IBM+Plex+Mono:wght@400;500;600;700&family=Inconsolata:wght@400;700&family=Great+Vibes&family=Sacramento&family=Kalam:wght@400;700&family=Indie+Flower&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Article content styles */
.article-content img,
.article-image {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
    border-radius: 8px;
    display: block;
}

.prose img {
    max-width: 100% !important;
    height: auto !important;
    margin: 1rem auto !important;
    border-radius: 8px;
}

/* Ensure consistent image sizing in the editor */
.ProseMirror img {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
    border-radius: 8px;
    display: block;
}

/* Video styles */
.article-video-container {
    width: 100%;
    max-width: 800px;
    margin: 2rem auto;
    background-color: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.article-video {
    width: 100%;
    height: auto;
    display: block;
    aspect-ratio: 16/9;
    object-fit: cover;
}

.video-caption {
    padding: 1rem;
    margin: 0;
    font-style: italic;
    color: #666;
    font-size: 0.9rem;
}

.video-credit {
    padding: 0.5rem 1rem;
    margin: 0;
    font-size: 0.8rem;
    color: #888;
    border-top: 1px solid #eee;
}

.video-credit a {
    color: #0066cc;
    text-decoration: none;
}

.video-credit a:hover {
    text-decoration: underline;
}

/* Font Classes */
.inter {
  font-family: 'Inter', sans-serif;
}

.poppins {
  font-family: 'Poppins', sans-serif;
}

.roboto {
  font-family: 'Roboto', sans-serif;
}

.plus-jakarta-sans {
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.quicksand {
  font-family: 'Quicksand', sans-serif;
}

.dm-sans {
  font-family: 'DM Sans', sans-serif;
}

.manrope {
  font-family: 'Manrope', sans-serif;
}

.space-grotesk {
  font-family: 'Space Grotesk', sans-serif;
}

.cormorant {
  font-family: 'Cormorant Garamond', serif;
}

.spectral {
  font-family: 'Spectral', serif;
}

.fraunces {
  font-family: 'Fraunces', serif;
}

.newsreader {
  font-family: 'Newsreader', serif;
}

.comfortaa {
  font-family: 'Comfortaa', display;
}

.josefin-sans {
  font-family: 'Josefin Sans', display;
}

.lexend {
  font-family: 'Lexend', display;
}

.outfit {
  font-family: 'Outfit', display;
}

.sora {
  font-family: 'Sora', display;
}

.space-mono {
  font-family: 'Space Mono', monospace;
}

.ibm-plex-mono {
  font-family: 'IBM Plex Mono', monospace;
}

.inconsolata {
  font-family: 'Inconsolata', monospace;
}

.great-vibes {
  font-family: 'Great Vibes', cursive;
}

.sacramento {
  font-family: 'Sacramento', cursive;
}

.kalam {
  font-family: 'Kalam', cursive;
}

.indie-flower {
  font-family: 'Indie Flower', cursive;
}

.georgia {
  font-family: Georgia, serif;
}

.helvetica {
  font-family: Helvetica, Arial, sans-serif;
}

.arial {
  font-family: Arial, sans-serif;
}

.times-new-roman {
  font-family: 'Times New Roman', Times, serif;
}

/* Add your existing styles below */ 