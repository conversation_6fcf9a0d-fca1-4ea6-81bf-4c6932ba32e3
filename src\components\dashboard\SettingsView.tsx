// import React, { useState, useEffect } from 'react';
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
// import ContentSettings from '@/components/ContentSettings';
// import HostingSettings from '@/components/HostingSettings';
// import AccountSettings from '@/components/AccountSettings';

// export const SettingsView = () => {
//   const [activeTab, setActiveTab] = useState('brand');

//   useEffect(() => {
//     document.title = 'Settings | BlogBuster';
//   }, []);

//   return (
//     <div>
//       <Tabs defaultValue="brand" className="w-full" onValueChange={setActiveTab}>
//         <TabsList className="mb-6">
//           <TabsTrigger value="brand">Brand</TabsTrigger>
//           <TabsTrigger value="content">Content</TabsTrigger>
//           <TabsTrigger value="hosting">Hosting</TabsTrigger>
//         </TabsList>
//         <TabsContent value="brand">
//           <ContentSettings />
//         </TabsContent>
//         <TabsContent value="content">
//           <ContentSettings />
//         </TabsContent>
//         <TabsContent value="hosting">
//           <HostingSettings />
//         </TabsContent>
//       </Tabs>
//     </div>
//   );
// };


import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDomain } from '@/contexts/DomainContext';
import { domainService } from '@/services/domainService';
import { toast } from 'sonner';
import { X, Loader2 } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import ContentSettings from '@/components/ContentSettings';
import AccountSettings from '@/components/AccountSettings';
import HostingSettings from '@/components/HostingSettings';
import { useAuth } from '@/contexts/AuthContext';

const defaultBrandInfo = {
  name: '',
  description: '',
  targetAudience: '',
  logo: '',
  audienceLocation: 'Global',
  benefits: ['Quality products/services', 'Professional expertise', 'Customer satisfaction focus'],
  toneOfVoice: 'Professional and informative',
  industry: 'General',
  tags: []
};

const MAX_LOGO_SIZE = 5 * 1024 * 1024; // 5MB

export const SettingsView = () => {
  const [activeTab, setActiveTab] = useState('brand');
  const { currentDomain, refreshDomains } = useDomain();
  const location = useLocation();
  const [brandInfo, setBrandInfo] = useState(defaultBrandInfo);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { logout } = useAuth();
  const navigate = useNavigate();
  const [logoError, setLogoError] = useState('');

  useEffect(() => {
    document.title = 'Settings | BlogBuster';
  }, []);

  useEffect(() => {
    if (currentDomain?.brandInfo) {
      setBrandInfo({
        ...defaultBrandInfo,
        ...currentDomain.brandInfo,
        benefits: currentDomain.brandInfo.benefits || defaultBrandInfo.benefits,
        tags: currentDomain.brandInfo.tags || defaultBrandInfo.tags
      });
    }
  }, [currentDomain]);

  const handleSaveBrandInfo = async () => {
    if (!currentDomain) {
      toast.error('No domain selected');
      return;
    }

    if (!brandInfo.name?.trim()) {
      toast.error('Brand name is required');
      return;
    }

    setIsSaving(true);
    try {
      await domainService.updateDomainBrandInfo(currentDomain._id, brandInfo);
      await refreshDomains();
      toast.success('Brand settings saved successfully');
    } catch (error: any) {
      console.error('Error saving brand info:', error);
      toast.error(error.response?.data?.error || error.message || 'Failed to save brand settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddBenefit = () => {
    setBrandInfo(prev => ({
      ...prev,
      benefits: [...(prev.benefits || []), '']
    }));
  };

  const handleRemoveBenefit = (index: number) => {
    setBrandInfo(prev => ({
      ...prev,
      benefits: (prev.benefits || []).filter((_, i) => i !== index)
    }));
  };

  const handleBenefitChange = (index: number, value: string) => {
    setBrandInfo(prev => ({
      ...prev,
      benefits: (prev.benefits || []).map((benefit, i) => i === index ? value : benefit)
    }));
  };

  const handleAddTag = () => {
    const input = document.getElementById('new-tag') as HTMLInputElement;
    if (input && input.value.trim()) {
      setBrandInfo(prev => ({
        ...prev,
        tags: [...(prev.tags || []), input.value.trim()]
      }));
      input.value = '';
    }
  };

  const handleRemoveTag = (tag: string) => {
    setBrandInfo(prev => ({
      ...prev,
      tags: (prev.tags || []).filter(t => t !== tag)
    }));
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Error logging out:', error);
      toast.error('Failed to log out');
    }
  };

  const handleDeleteAccount = () => {
    // TODO: Implement account deletion
    toast.info('Account deletion will be available soon');
  };

  const handleManageSubscription = () => {
    // TODO: Implement subscription management
    toast.info('Subscription management will be available soon');
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    setLogoError('');

    if (!file) return;

    // Validate file size
    if (file.size > MAX_LOGO_SIZE) {
      setLogoError('Logo file size must be less than 5MB');
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setLogoError('Please upload an image file');
      return;
    }

    try {
      // Convert to base64 for preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setBrandInfo(prev => ({
          ...prev,
          logo: reader.result as string
        }));
      };
      reader.readAsDataURL(file);

      // Optional: Upload to server/CDN here if needed
      // const formData = new FormData();
      // formData.append('logo', file);
      // const response = await api.post('/upload', formData);
      // setBrandInfo(prev => ({
      //   ...prev,
      //   logo: response.data.url
      // }));
    } catch (error) {
      console.error('Error uploading logo:', error);
      setLogoError('Failed to upload logo');
    }
  };

  const handleRemoveLogo = () => {
    setBrandInfo(prev => ({
      ...prev,
      logo: ''
    }));
    setLogoError('');
  };

  const tabs = [
    { id: 'brand', label: 'Brand' },
    { id: 'content', label: 'Content' },
    { id: 'hosting', label: 'Hosting' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${activeTab === tab.id
              ? 'border-orange-500 text-orange-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {activeTab === 'brand' && (
        <div className="space-y-6">
          <Card className="p-6 bg-white shadow-sm">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold mb-2">Brand Info</h2>
                  <p className="text-gray-600 text-sm mb-4">
                    Information used to generate bespoke and relevant articles to your business
                  </p>
                </div>

                <div>
                  <Label htmlFor="brandName">Brand Name (used by customers)</Label>
                  <Input
                    id="brandName"
                    value={brandInfo.name}
                    onChange={(e) => setBrandInfo({ ...brandInfo, name: e.target.value })}
                    className="mt-1"
                    placeholder="Enter your brand name"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={brandInfo.description}
                    onChange={(e) => setBrandInfo({ ...brandInfo, description: e.target.value })}
                    className="mt-1 min-h-[120px]"
                    placeholder="Describe your brand and its unique value proposition"
                  />
                </div>

                <div>
                  <Label htmlFor="targetAudience">Target Audience</Label>
                  <Textarea
                    id="targetAudience"
                    value={brandInfo.targetAudience}
                    onChange={(e) => setBrandInfo({ ...brandInfo, targetAudience: e.target.value })}
                    className="mt-1 min-h-[100px]"
                    placeholder="Describe your target audience"
                  />
                </div>

                <div>
                  <Label>Audience location</Label>
                  <Input
                    value={brandInfo.audienceLocation}
                    onChange={(e) => setBrandInfo({ ...brandInfo, audienceLocation: e.target.value })}
                    className="mt-1"
                  />
                </div>
              </div>

              <div className="space-y-6">
                {/* <div>
                  <h2 className="text-lg font-semibold mb-2">Brand Logo</h2>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                    <div className="flex flex-col items-center">
                      {brandInfo.logo ? (
                        <div className="relative w-32 h-32 mb-4">
                          <img
                            src={brandInfo.logo}
                            alt="Brand Logo"
                            className="w-full h-full object-contain rounded-lg"
                            onError={(e) => {
                              const img = e.target as HTMLImageElement;
                              img.src = '/placeholder.svg';
                              setLogoError('Failed to load logo image');
                            }}
                          />
                          <button
                            onClick={handleRemoveLogo}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                            type="button"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ) : (
                        <div className="w-32 h-32 bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                          <span className="text-gray-400 text-center text-sm">
                            No logo uploaded
                            <br />
                            Click to upload
                          </span>
                        </div>
                      )}

                      <div className="space-y-2 w-full">
                        {logoError && (
                          <p className="text-sm text-red-600 text-center">{logoError}</p>
                        )}
                        <p className="text-sm text-gray-500 text-center">
                          Recommended: Square image, at least 200x200px
                          <br />
                          Maximum size 5 MB
                        </p>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          className="hidden"
                          id="logo-upload"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById('logo-upload')?.click()}
                          className="w-full"
                        >
                          {brandInfo.logo ? 'Change Logo' : 'Upload Logo'}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div> */}

                <div>
                  <Label>Benefits</Label>
                  <div className="space-y-2 mt-2">
                    {(brandInfo.benefits || []).map((benefit, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={benefit}
                          onChange={(e) => handleBenefitChange(index, e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleRemoveBenefit(index)}
                          className="shrink-0"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      onClick={handleAddBenefit}
                      className="text-orange-600 hover:text-orange-700"
                    >
                      + Add new benefits
                    </Button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="toneOfVoice">Tone of voice</Label>
                  <Textarea
                    id="toneOfVoice"
                    value={brandInfo.toneOfVoice}
                    onChange={(e) => setBrandInfo({ ...brandInfo, toneOfVoice: e.target.value })}
                    className="mt-1 min-h-[100px]"
                    placeholder="Describe your brand's tone of voice"
                  />
                </div>

                <div>
                  <Label htmlFor="industry">Industry</Label>
                  <Textarea
                    id="industry"
                    value={brandInfo.industry}
                    onChange={(e) => setBrandInfo({ ...brandInfo, industry: e.target.value })}
                    className="mt-1"
                    placeholder="Describe your industry"
                  />
                </div>

                <div>
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {(brandInfo.tags || []).map((tag) => (
                      <div
                        key={tag}
                        className="bg-orange-50 text-orange-700 px-3 py-1 rounded-full text-sm flex items-center gap-2"
                      >
                        {tag}
                        <button onClick={() => handleRemoveTag(tag)} className="text-orange-400 hover:text-orange-600">
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                  <div className="mt-2 flex gap-2">
                    <Input
                      placeholder="Add a new tag"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          const value = (e.target as HTMLInputElement).value.trim();
                          if (value && !brandInfo.tags.includes(value)) {
                            setBrandInfo({
                              ...brandInfo,
                              tags: [...brandInfo.tags, value]
                            });
                            (e.target as HTMLInputElement).value = '';
                          }
                        }
                      }}
                    />
                    <Button
                      variant="outline"
                      onClick={handleAddTag}
                      className="shrink-0"
                    >
                      Add
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-4 bg-white shadow-sm">
            <div className="flex justify-end gap-4">
              <Button
                variant="outline"
                onClick={() => refreshDomains()}
                disabled={isLoading || isSaving}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  'Sync again'
                )}
              </Button>
              <Button
                onClick={handleSaveBrandInfo}
                disabled={isLoading || isSaving}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'content' && (
        <ContentSettings onSave={async (settings) => {
          try {
            // TODO: Implement save functionality
            toast.success('Content settings saved successfully');
          } catch (error) {
            console.error('Error saving content settings:', error);
            toast.error('Failed to save content settings');
          }
        }} />
      )}

      {activeTab === 'hosting' && (
        <HostingSettings />
      )}
    </div>
  );
};
