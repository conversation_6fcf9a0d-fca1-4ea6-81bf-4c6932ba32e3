const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/db');

async function updateUsersActiveStatus() {
    try {
        // Connect to MongoDB
        await mongoose.connect('mongodb://127.0.0.1:27017/seo-autoblog', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Connected to MongoDB successfully');

        // Update all existing users to have isActive = true
        const result = await User.updateMany(
            { isActive: { $exists: false } }, // Find documents where isActive doesn't exist
            { $set: { isActive: true } }      // Set isActive to true
        );

        console.log(`Updated ${result.modifiedCount} users with isActive status`);
        console.log('Migration completed successfully');
    } catch (error) {
        console.error('Error during migration:', error);
    } finally {
        // Close the MongoDB connection
        await mongoose.connection.close();
        console.log('MongoDB connection closed');
    }
}

// Run the migration
updateUsersActiveStatus(); 