import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Calendar } from 'lucide-react';

interface ScheduledPost {
    id: string;
    title: string;
    date: string;
    status: 'scheduled' | 'published' | 'failed';
}

export const SchedulerView = () => {
    useEffect(() => {
        document.title = 'Scheduler | BlogBuster';
    }, []);

    const [scheduledPosts, setScheduledPosts] = React.useState<ScheduledPost[]>([
        {
            id: '1',
            title: 'Top 10 SEO Strategies for 2024',
            date: '2024-03-15T10:00:00',
            status: 'scheduled'
        },
        {
            id: '2',
            title: 'How to Optimize Your Content for Search Engines',
            date: '2024-03-20T14:30:00',
            status: 'scheduled'
        }
    ]);

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'scheduled':
                return 'text-blue-600 bg-blue-50';
            case 'published':
                return 'text-green-600 bg-green-50';
            case 'failed':
                return 'text-red-600 bg-red-50';
            default:
                return 'text-gray-600 bg-gray-50';
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h1 className="text-2xl font-semibold text-gray-900">Content Schedule</h1>
                <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                    Schedule New Post
                </Button>
            </div>

            <Card className="p-6">
                <div className="space-y-4">
                    <div className="flex items-center gap-2 text-gray-600">
                        <Calendar className="w-5 h-5" />
                        <span>Upcoming Posts</span>
                    </div>

                    <div className="divide-y">
                        {scheduledPosts.map((post) => (
                            <div key={post.id} className="py-4 flex items-center justify-between">
                                <div className="space-y-1">
                                    <h3 className="font-medium text-gray-900">{post.title}</h3>
                                    <p className="text-sm text-gray-500">
                                        Scheduled for: {new Date(post.date).toLocaleString()}
                                    </p>
                                </div>
                                <div className="flex items-center gap-4">
                                    <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(post.status)}`}>
                                        {post.status.charAt(0).toUpperCase() + post.status.slice(1)}
                                    </span>
                                    <Button variant="outline" size="sm">
                                        Edit
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </Card>

            <Card className="p-6">
                <div className="space-y-4">
                    <h2 className="text-lg font-medium text-gray-900">Schedule Settings</h2>
                    <div className="grid gap-4">
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h3 className="font-medium text-gray-900">Auto-scheduling</h3>
                                <p className="text-sm text-gray-500">Automatically schedule posts at optimal times</p>
                            </div>
                            <Button variant="outline">Configure</Button>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h3 className="font-medium text-gray-900">Time Zone</h3>
                                <p className="text-sm text-gray-500">Current: UTC+00:00</p>
                            </div>
                            <Button variant="outline">Change</Button>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    );
}; 