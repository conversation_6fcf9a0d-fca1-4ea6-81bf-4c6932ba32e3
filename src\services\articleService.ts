import apiClient from '../lib/api-client';
import { Article } from '../types/Article';

export const fetchDomainAndArticles = async (domainId: string): Promise<Article[]> => {
    try {
        const response = await apiClient.get(`/articles?domainId=${domainId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching articles:', error);
        throw error;
    }
};

export const fetchArticleById = async (articleId: string): Promise<Article> => {
    try {
        const response = await apiClient.get(`/articles/${articleId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching article:', error);
        throw error;
    }
};

export const createArticle = async (articleData: Partial<Article>): Promise<Article> => {
    try {
        const response = await apiClient.post('/articles', articleData);
        return response.data;
    } catch (error) {
        console.error('Error creating article:', error);
        throw error;
    }
};

export const updateArticle = async (articleId: string, articleData: Partial<Article>): Promise<Article> => {
    try {
        const response = await apiClient.put(`/articles/${articleId}`, articleData);
        return response.data;
    } catch (error) {
        console.error('Error updating article:', error);
        throw error;
    }
};

export const deleteArticle = async (articleId: string): Promise<void> => {
    try {
        await apiClient.delete(`/articles/${articleId}`);
    } catch (error) {
        console.error('Error deleting article:', error);
        throw error;
    }
};

export interface ArticleStats {
    all: number;
    draft: number;
    scheduled: number;
    generated: number;
    published: number;
}

class ArticleService {
    private baseUrl = '/articles';

    async getArticles(domainId?: string): Promise<Article[]> {
        try {
            const params = domainId ? { domainId } : {};
            const response = await apiClient.get(this.baseUrl, { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching articles:', error);
            throw error;
        }
    }

    async getArticlesByDomainUrl(subdomain: string, domain: string): Promise<Article[]> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/by-domain`, {
                params: {
                    domain: `${subdomain}.${domain}`
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching articles by domain URL:', error);
            throw error;
        }
    }

    async getArticleStats(): Promise<ArticleStats> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/stats`);
            return response.data;
        } catch (error) {
            console.error('Error fetching article stats:', error);
            throw error;
        }
    }

    async getArticleBySlug(slug: string): Promise<Article> {
        try {
            const response = await apiClient.get(`/articles/by-slug/${encodeURIComponent(slug)}`);
            if (!response.data) {
                throw new Error('Article not found');
            }
            return response.data;
        } catch (error) {
            console.error('Error fetching article by slug:', error);
            throw error;
        }
    }

    async fetchArticleById(articleId: string): Promise<Article> {
        try {
            const response = await apiClient.get(`${this.baseUrl}/${articleId}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching article:', error);
            throw error;
        }
    }

    async updateArticle(articleId: string, articleData: Partial<Article>): Promise<Article> {
        try {
            const response = await apiClient.put(`${this.baseUrl}/${articleId}`, articleData);
            return response.data;
        } catch (error) {
            console.error('Error updating article:', error);
            throw error;
        }
    }
}

export const articleService = new ArticleService(); 