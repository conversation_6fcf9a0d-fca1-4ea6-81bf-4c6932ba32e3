import express, { Request } from 'express';
import { Article as ArticleModel } from '../models/Article';

interface AuthRequest extends Request {
    user: {
        id: string;
    };
}

const router = express.Router();

// Get all articles
router.get('/', async (req: AuthRequest, res) => {
    try {
        const articles = await ArticleModel.find({ userId: req.user.id }).sort({ date: -1 });
        res.json(articles);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching articles', error });
    }
});

// Get article stats
router.get('/stats', async (req: AuthRequest, res) => {
    try {
        const articles = await ArticleModel.find({ userId: req.user.id });
        const stats = {
            all: articles.length,
            draft: articles.filter(a => a.status.toLowerCase() === 'draft').length,
            scheduled: articles.filter(a => a.status.toLowerCase() === 'scheduled').length,
            generated: articles.filter(a => a.status.toLowerCase() === 'generated').length,
            published: articles.filter(a => a.status.toLowerCase() === 'published').length,
        };
        res.json(stats);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching article stats', error });
    }
});

// Get single article
router.get('/:id', async (req: AuthRequest, res) => {
    try {
        const article = await ArticleModel.findOne({ _id: req.params.id, userId: req.user.id });
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }
        res.json(article);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching article', error });
    }
});

// Create article
router.post('/', async (req: AuthRequest, res) => {
    try {
        const article = new ArticleModel({
            ...req.body,
            userId: req.user.id
        });
        await article.save();
        res.status(201).json(article);
    } catch (error) {
        res.status(400).json({ message: 'Error creating article', error });
    }
});

// Update article
router.put('/:id', async (req: AuthRequest, res) => {
    try {
        const article = await ArticleModel.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            req.body,
            { new: true, runValidators: true }
        );
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }
        res.json(article);
    } catch (error) {
        res.status(400).json({ message: 'Error updating article', error });
    }
});

// Delete article
router.delete('/:id', async (req, res) => {
    try {
        const article = await ArticleModel.findByIdAndDelete(req.params.id);
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }
        res.json({ message: 'Article deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting article', error });
    }
});

export default router; 