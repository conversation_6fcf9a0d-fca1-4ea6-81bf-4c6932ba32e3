import { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextStyle from '@tiptap/extension-text-style';
import TextAlign from '@tiptap/extension-text-align';
import Color from '@tiptap/extension-color';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import { articleApi } from '@/services/api';
import { toast } from '@/components/ui/use-toast';
import { ArrowLeft } from 'lucide-react';
import { Bold, Italic, Underline as UnderlineIcon, List, ListOrdered, Link2, Link2Off } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useDomain } from '@/contexts/DomainContext';

export const WritePage = () => {
    const navigate = useNavigate();
    const [title, setTitle] = useState('');
    const [intro, setIntro] = useState('');
    const [urlSlug, setUrlSlug] = useState('');
    const [metaDescription, setMetaDescription] = useState('');
    const [excerpt, setExcerpt] = useState('');
    const [keywords, setKeywords] = useState<string[]>([]);
    const [content, setContent] = useState('');
    const [keywordsInput, setKeywordsInput] = useState('');
    const [status, setStatus] = useState<'draft' | 'published'>('draft');
    const [image, setImage] = useState<{
        url: string;
        thumb?: string;
        description?: string;
    }>({
        url: '/lovable-uploads/default-article.png',
        thumb: '/lovable-uploads/default-article-thumb.png',
        description: ''
    });

    const imageInputRef = useRef<HTMLInputElement>(null);
    const editor = useEditor({
        extensions: [
            StarterKit,
            Underline,
            TextStyle,
            Color,
            Link,
            Image.configure({
                inline: true,
                allowBase64: true,
            }),
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            }),
        ],
        content: '',
        onUpdate: ({ editor }) => {
            setContent(editor.getHTML());
        },
        editorProps: {
            attributes: {
                class: 'prose prose-lg max-w-none min-h-[500px] focus:outline-none w-full h-full p-4',
                style: 'cursor: text'
            }
        },
    });

    const { toast: useToastToast } = useToast();
    const { currentDomain } = useDomain();

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const result = e.target?.result;
                if (typeof result === 'string') {
                    editor?.chain().focus().insertContent({
                        type: 'image',
                        attrs: { src: result }
                    }).run();
                }
            };
            reader.readAsDataURL(file);
        }
    };

    const handleImageUrl = () => {
        const url = window.prompt('Enter image URL:');
        if (url) {
            editor?.chain().focus().insertContent({
                type: 'image',
                attrs: { src: url }
            }).run();
        }
    };

    const handleKeywordsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        setKeywordsInput(inputValue);

        if (inputValue.trim()) {
            const newKeywords = inputValue
                .split(',')
                .map(k => k.trim())
                .filter(k => k !== '');
            setKeywords(newKeywords);
        } else {
            setKeywords([]);
        }
    };

    const handleCreateArticle = async () => {
        if (!currentDomain) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Please select a domain first"
            });
            return;
        }

        if (!title.trim()) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Please enter a title for the article"
            });
            return;
        }

        if (!content.trim()) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Please enter content for the article"
            });
            return;
        }

        try {
            const article = await articleApi.createArticle({
                title,
                description: intro,
                content,
                tag: 'General',
                status: status,
                author: 'AI Writer',
                date: new Date().toISOString(),
                image: {
                    url: image.url,
                    thumb: image.thumb,
                    description: image.description
                },
                urlSlug: urlSlug || title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
                metaDescription,
                excerpt,
                keywords,
                domainId: currentDomain._id
            });

            toast({
                title: "Success",
                description: "Article created successfully",
            });

            navigate('/dashboard/articles');
        } catch (error) {
            console.error('Error creating article:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: error instanceof Error ? error.message : "Failed to create article"
            });
        }
    };

    const EditorToolbar = () => (
        <div className="border-b bg-white p-2">
            <div className="flex flex-wrap items-center gap-1">
                {/* Text Style Group */}
                <div className="flex items-center gap-1 border-r pr-1">
                    <button
                        onClick={() => editor?.chain().focus().toggleBold().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('bold') ? 'bg-gray-200' : ''}`}
                        title="Bold"
                    >
                        <Bold className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleItalic().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('italic') ? 'bg-gray-200' : ''}`}
                        title="Italic"
                    >
                        <Italic className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleUnderline().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('underline') ? 'bg-gray-200' : ''}`}
                        title="Underline"
                    >
                        <UnderlineIcon className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleStrike().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('strike') ? 'bg-gray-200' : ''}`}
                        title="Strike"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="5" y1="12" x2="19" y2="12"></line><path d="M16 6C16 6 14.5 4 12 4C9.5 4 7 6 7 8"></path><path d="M8 16C8 16 10 18 12 18C14 18 16 16 16 14"></path></svg>
                    </button>
                </div>

                {/* Heading Group */}
                <div className="flex items-center gap-1 border-r pr-1">
                    <button
                        onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''}`}
                        title="Heading 1"
                    >
                        H1
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}`}
                        title="Heading 2"
                    >
                        H2
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''}`}
                        title="Heading 3"
                    >
                        H3
                    </button>
                </div>

                {/* Alignment Group */}
                <div className="flex items-center gap-1 border-r pr-1">
                    <button
                        onClick={() => editor?.chain().focus().setTextAlign('left').run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive({ textAlign: 'left' }) ? 'bg-gray-200' : ''}`}
                        title="Align Left"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="12" x2="15" y2="12"></line><line x1="3" y1="18" x2="18" y2="18"></line></svg>
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().setTextAlign('center').run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive({ textAlign: 'center' }) ? 'bg-gray-200' : ''}`}
                        title="Align Center"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="3" y1="6" x2="21" y2="6"></line><line x1="6" y1="12" x2="18" y2="12"></line><line x1="4" y1="18" x2="20" y2="18"></line></svg>
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().setTextAlign('right').run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive({ textAlign: 'right' }) ? 'bg-gray-200' : ''}`}
                        title="Align Right"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="3" y1="6" x2="21" y2="6"></line><line x1="9" y1="12" x2="21" y2="12"></line><line x1="6" y1="18" x2="21" y2="18"></line></svg>
                    </button>
                </div>

                {/* List Group */}
                <div className="flex items-center gap-1 border-r pr-1">
                    <button
                        onClick={() => editor?.chain().focus().toggleBulletList().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('bulletList') ? 'bg-gray-200' : ''}`}
                        title="Bullet List"
                    >
                        <List className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleOrderedList().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('orderedList') ? 'bg-gray-200' : ''}`}
                        title="Numbered List"
                    >
                        <ListOrdered className="w-4 h-4" />
                    </button>
                </div>

                {/* Additional Features */}
                <div className="flex items-center gap-1">
                    <button
                        onClick={() => {
                            const url = window.prompt('Enter link URL:');
                            if (url) {
                                editor?.chain().focus().setLink({ href: url }).run();
                            }
                        }}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('link') ? 'bg-gray-200' : ''}`}
                        title="Add Link"
                    >
                        <Link2 className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().toggleBlockquote().run()}
                        className={`p-1.5 rounded hover:bg-gray-100 ${editor?.isActive('blockquote') ? 'bg-gray-200' : ''}`}
                        title="Block Quote"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path><path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path></svg>
                    </button>
                    <button
                        onClick={() => editor?.chain().focus().setHorizontalRule().run()}
                        className="p-1.5 rounded hover:bg-gray-100"
                        title="Horizontal Line"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2"><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                    </button>
                </div>

                {/* Add Image Group */}
                <div className="flex items-center gap-1 border-l pl-1">
                    <input
                        type="file"
                        ref={imageInputRef}
                        onChange={handleImageUpload}
                        accept="image/*"
                        className="hidden"
                    />
                    <button
                        onClick={() => imageInputRef.current?.click()}
                        className="p-1.5 rounded hover:bg-gray-100"
                        title="Upload Image"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21 15 16 10 5 21"></polyline>
                        </svg>
                    </button>
                    <button
                        onClick={handleImageUrl}
                        className="p-1.5 rounded hover:bg-gray-100"
                        title="Insert Image URL"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 19H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h18a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2zM3 7v10h18V7H3z"></path>
                            <path d="M9.5 13a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5z"></path>
                            <path d="M21 19l-4.5-4.5L14 17l-4-4L3 19"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    );

    // Function to process and resize image
    const processImage = async (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = document.createElement('img');
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    let width = img.width;
                    let height = img.height;

                    // Calculate new dimensions while maintaining aspect ratio
                    if (width > 800) {
                        height = (height * 800) / width;
                        width = 800;
                    }
                    if (height > 600) {
                        width = (width * 600) / height;
                        height = 600;
                    }

                    canvas.width = width;
                    canvas.height = height;
                    const ctx = canvas.getContext('2d');
                    ctx?.drawImage(img, 0, 0, width, height);
                    resolve(canvas.toDataURL('image/jpeg', 0.85));
                };
                img.onerror = reject;
                img.src = e.target?.result as string;
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="h-14 bg-white border-b flex items-center justify-between px-6">
                <button
                    onClick={() => navigate('/dashboard')}
                    className="text-sm text-gray-600 hover:text-gray-900 flex items-center space-x-2"
                >
                    <ArrowLeft className="h-4 w-4" />
                    <span>Back to Dashboard</span>
                </button>
                <div className="flex items-center space-x-3">
                    <Button
                        variant="outline"
                        onClick={() => navigate('/dashboard')}
                        className="text-sm"
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleCreateArticle}
                        className="text-sm bg-orange-500 hover:bg-orange-600 text-white"
                    >
                        Create Article
                    </Button>
                </div>
            </div>

            <div className="flex h-[calc(100vh-3.5rem)]">
                {/* Main Content Area */}
                <div className="flex-1 overflow-y-auto">
                    <div className="mx-auto p-6">
                        <div className="space-y-6">
                            {/* Title Section */}
                            <div className="bg-white rounded-lg shadow-sm border p-6">
                                <h2 className="text-xl font-semibold mb-6 text-gray-800">Title & Introduction</h2>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Blog Title
                                        </label>
                                        <Input
                                            placeholder="Enter your blog title here..."
                                            value={title}
                                            onChange={(e) => setTitle(e.target.value)}
                                            className="w-full text-lg py-2"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Article Introduction
                                        </label>
                                        <Textarea
                                            placeholder="Write a brief introduction to your article..."
                                            value={intro}
                                            onChange={(e) => setIntro(e.target.value)}
                                            className="w-full min-h-[120px] text-base"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Content Section */}
                            <div className="bg-white rounded-lg shadow-sm border">
                                <div className="p-6 border-b">
                                    <div className="flex items-center justify-between">
                                        <h2 className="text-xl font-semibold text-gray-800">Content</h2>
                                        <span className="text-sm text-gray-500">Maximum size 5 MB</span>
                                    </div>
                                </div>
                                <div className="border-b">
                                    <div className="bg-gray-50 p-2 flex flex-wrap items-center gap-1">
                                        <div className="flex items-center gap-1 border-r pr-2">
                                            <button
                                                onClick={() => editor?.chain().focus().toggleBold().run()}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('bold') ? 'bg-gray-200' : ''}`}
                                            >
                                                <Bold className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => editor?.chain().focus().toggleItalic().run()}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('italic') ? 'bg-gray-200' : ''}`}
                                            >
                                                <Italic className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => editor?.chain().focus().toggleUnderline().run()}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('underline') ? 'bg-gray-200' : ''}`}
                                            >
                                                <UnderlineIcon className="w-4 h-4" />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 border-r pr-2">
                                            <button
                                                onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''}`}
                                            >
                                                H1
                                            </button>
                                            <button
                                                onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}`}
                                            >
                                                H2
                                            </button>
                                            <button
                                                onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('heading', { level: 3 }) ? 'bg-gray-200' : ''}`}
                                            >
                                                H3
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 border-r pr-2">
                                            <button
                                                onClick={() => editor?.chain().focus().toggleBulletList().run()}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('bulletList') ? 'bg-gray-200' : ''}`}
                                            >
                                                <List className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => editor?.chain().focus().toggleOrderedList().run()}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('orderedList') ? 'bg-gray-200' : ''}`}
                                            >
                                                <ListOrdered className="w-4 h-4" />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <button
                                                onClick={() => {
                                                    const url = window.prompt('Enter link URL:');
                                                    if (url) {
                                                        editor?.chain().focus().setLink({ href: url }).run();
                                                    }
                                                }}
                                                className={`p-2 rounded hover:bg-gray-200 ${editor?.isActive('link') ? 'bg-gray-200' : ''}`}
                                            >
                                                <Link2 className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => editor?.chain().focus().unsetLink().run()}
                                                className="p-2 rounded hover:bg-gray-200"
                                            >
                                                <Link2Off className="w-4 h-4" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div className="p-6">
                                    <EditorContent
                                        editor={editor}
                                        className="prose max-w-none min-h-[400px] w-full h-full cursor-text focus:outline-none"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Sidebar */}
                <div className="w-96 border-l bg-gray-50">
                    <div className="p-6 space-y-6">
                        <div className="bg-white rounded-lg shadow-sm border p-6">
                            <h2 className="text-lg font-semibold mb-6 text-gray-800">Article Settings</h2>
                            <div className="space-y-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        URL Slug
                                    </label>
                                    <Input
                                        placeholder="your-article-url-slug"
                                        value={urlSlug}
                                        onChange={(e) => setUrlSlug(e.target.value)}
                                        className="w-full"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        This will be the URL of your article
                                    </p>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Meta Description
                                    </label>
                                    <Textarea
                                        placeholder="Enter SEO meta description..."
                                        value={metaDescription}
                                        onChange={(e) => setMetaDescription(e.target.value)}
                                        className="w-full min-h-[100px]"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Brief description for search engines
                                    </p>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Excerpt
                                    </label>
                                    <Textarea
                                        placeholder="Write a brief excerpt of your article..."
                                        value={excerpt}
                                        onChange={(e) => setExcerpt(e.target.value)}
                                        className="w-full min-h-[100px]"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Short preview text for article listings
                                    </p>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Keywords
                                    </label>
                                    <Input
                                        placeholder="Enter keywords separated by commas..."
                                        value={keywordsInput}
                                        onChange={handleKeywordsChange}
                                        className="w-full"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Add relevant keywords for better SEO
                                    </p>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Status
                                    </label>
                                    <div className="flex gap-3">
                                        <label className="flex items-center space-x-2">
                                            <input
                                                type="radio"
                                                value="draft"
                                                checked={status === 'draft'}
                                                onChange={(e) => setStatus(e.target.value as 'draft')}
                                                className="h-4 w-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                                            />
                                            <span className="text-sm text-gray-700">Draft</span>
                                        </label>
                                        <label className="flex items-center space-x-2">
                                            <input
                                                type="radio"
                                                value="published"
                                                checked={status === 'published'}
                                                onChange={(e) => setStatus(e.target.value as 'published')}
                                                className="h-4 w-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                                            />
                                            <span className="text-sm text-gray-700">Published</span>
                                        </label>
                                    </div>
                                    <p className="text-sm text-gray-500 mt-1">
                                        Choose whether to save as draft or publish immediately
                                    </p>
                                </div>

                                {/* Featured Image Section */}
                                <div className="mb-6">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Featured Image
                                    </label>
                                    <div className="space-y-4">
                                        {image.url && image.url !== '/lovable-uploads/default-article.png' ? (
                                            <div className="relative">
                                                <img
                                                    src={image.url}
                                                    alt="Featured image"
                                                    className="w-full h-40 object-cover rounded-lg"
                                                />
                                                <button
                                                    onClick={() => setImage({
                                                        url: '/lovable-uploads/default-article.png',
                                                        thumb: '/lovable-uploads/default-article-thumb.png',
                                                        description: ''
                                                    })}
                                                    className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                                                    title="Remove image"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                                    </svg>
                                                </button>
                                            </div>
                                        ) : (
                                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                                <p className="text-sm text-gray-500">No image uploaded</p>
                                            </div>
                                        )}

                                        <div>
                                            <input
                                                type="file"
                                                accept="image/*"
                                                className="hidden"
                                                ref={imageInputRef}
                                                onChange={async (e) => {
                                                    const file = e.target.files?.[0];
                                                    if (file) {
                                                        try {
                                                            const resizedImage = await processImage(file);
                                                            setImage({
                                                                url: resizedImage,
                                                                thumb: resizedImage,
                                                                description: file.name.split('.')[0]
                                                            });
                                                            e.target.value = '';
                                                        } catch (error) {
                                                            console.error('Error processing image:', error);
                                                            toast({
                                                                title: "Error",
                                                                description: "Failed to process image",
                                                                variant: "destructive"
                                                            });
                                                        }
                                                    }
                                                }}
                                            />
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={() => imageInputRef.current?.click()}
                                                className="w-full flex items-center justify-center gap-2"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                                                </svg>
                                                Upload Image
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}; 