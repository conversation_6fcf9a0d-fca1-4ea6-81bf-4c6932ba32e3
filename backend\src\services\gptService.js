const { OpenAI } = require('openai');

class GPTService {
    constructor() {
        // Initialize with environment variables or defaults
        this.apiKey = process.env.OPENAI_API_KEY;
        this.isConfigured = !!this.apiKey;

        if (this.isConfigured) {
            this.openai = new OpenAI({
                apiKey: this.apiKey
            });
        }

        // Define available models
        this.models = {
            default: 'gpt-4o-mini',
            fallback: 'gpt-4o-mini'
        };

        // Define default word count ranges
        this.defaultWordCountRanges = {
            short: { min: 50, max: 100 },
            medium: { min: 100, max: 200 },
            long: { min: 200, max: 300 }
        };
    }

    async callOpenAI(messages, temperature = 0.7, maxTokens = 2000, model = this.models.default) {
        try {
            // Check if service is configured
            if (!this.isConfigured) {
                console.error('OpenAI API is not configured. Please set OPENAI_API_KEY in your environment.');
                return this.getOfflineContent(messages[messages.length - 1].content);
            }

            const completion = await this.openai.chat.completions.create({
                model: model,
                messages: messages,
                temperature: temperature,
                max_tokens: maxTokens
            });
            return completion.choices[0].message.content;
        } catch (error) {
            console.error('OpenAI API Error:', error);

            // Handle specific error types
            if (error.status === 429 || error.code === 'insufficient_quota') {
                console.log('API quota exceeded or insufficient quota. Using offline mode.');
                return this.getOfflineContent(messages[messages.length - 1].content);
            }

            // If the error is related to the model, try fallback model
            if (error.message?.includes('model') && model !== this.models.fallback) {
                console.log('Attempting fallback to', this.models.fallback);
                return this.callOpenAI(messages, temperature, maxTokens, this.models.fallback);
            }

            // For authentication errors, provide clear message
            if (error.status === 401) {
                throw new Error('Invalid OpenAI API key. Please check your configuration.');
            }

            throw new Error(`Failed to generate content: ${error.message}`);
        }
    }

    getOfflineContent(prompt) {
        // Generate a more sophisticated offline content based on the prompt
        const currentDate = new Date().toLocaleDateString();
        const wordCount = Math.floor(Math.random() * (1500 - 800 + 1) + 800);

        return `
<h2>Temporary Article: ${prompt.split('\n')[0] || 'New Content'}</h2>

<p><em>Note: This is automatically generated offline content. The AI content generation service is currently unavailable.</em></p>

<h3>About This Content</h3>
<p>This article is a placeholder generated on ${currentDate}. The actual AI-powered content generation service is temporarily unavailable due to one of the following reasons:</p>

<ul>
    <li>OpenAI API key is not configured</li>
    <li>API quota has been exceeded</li>
    <li>Temporary API service disruption</li>
</ul>

<h3>Next Steps</h3>
<p>To restore full functionality, please:</p>

<ol>
    <li>Check your OpenAI API key configuration in the environment settings</li>
    <li>Verify your API quota and billing status at platform.openai.com</li>
    <li>Try regenerating the article once the service is restored</li>
</ol>

<h3>Content Specifications</h3>
<ul>
    <li>Word Count: ${wordCount} words</li>
    <li>Generated: ${currentDate}</li>
    <li>Status: Offline Mode</li>
</ul>

<p>You can edit this content manually or wait for the AI service to be restored to generate new content.</p>`;
    }

    async validateContent(content, title, articleSettings, brandInfo, wordCountRange, existingArticlesSummary = []) {
        // Count words in content (more accurate word counting)
        const wordCount = content
            .replace(/<[^>]*>/g, '') // Remove HTML tags
            .replace(/[\r\n]+/g, ' ') // Replace newlines with spaces
            .replace(/\s+/g, ' ') // Replace multiple spaces with single space
            .trim() // Trim whitespace
            .split(/\s+/) // Split by whitespace
            .filter(word => word.length > 0) // Remove empty strings
            .length;

        // Build requirements list dynamically based on available fields
        const requirements = [
            `1. Word Count: Must be between ${wordCountRange.min}-${wordCountRange.max} words (Current: ${wordCount})`
        ];

        let reqIndex = 2;

        // Only add requirements for fields that exist and have meaningful content
        if (articleSettings?.language) {
            requirements.push(`${reqIndex++}. Language: Content must be entirely in ${articleSettings.language}`);
        }

        if (brandInfo?.toneOfVoice) {
            requirements.push(`${reqIndex++}. Tone: Must maintain ${brandInfo.toneOfVoice} tone throughout`);
        }

        if (brandInfo?.industry) {
            requirements.push(`${reqIndex++}. Industry: Should demonstrate knowledge of ${brandInfo.industry} industry`);
        }

        if (brandInfo?.targetAudience && brandInfo?.audienceLocation) {
            requirements.push(`${reqIndex++}. Target Audience: Should be suitable for ${brandInfo.targetAudience} in ${brandInfo.audienceLocation}`);
        }

        if (brandInfo?.name && brandInfo?.description) {
            requirements.push(`${reqIndex++}. Brand Alignment: Should align with ${brandInfo.name}'s identity`);
        }

        // Benefits are now optional - we don't validate them strictly
        if (brandInfo?.benefits?.length > 0) {
            requirements.push(`${reqIndex++}. Benefits: Consider mentioning these benefits where relevant: ${brandInfo.benefits.join(', ')}`);
        }

        if (articleSettings?.specificInstructions) {
            requirements.push(`${reqIndex++}. Required Elements: Must include: ${articleSettings.specificInstructions}`);
        }

        if (articleSettings?.exclusions) {
            requirements.push(`${reqIndex++}. Excluded Elements: Must NOT include: ${articleSettings.exclusions}`);
        }

        // Add uniqueness requirement if we have existing articles
        if (existingArticlesSummary && existingArticlesSummary.length > 0) {
            requirements.push(`${reqIndex++}. Uniqueness: Content must be unique compared to existing articles`);
        }

        // Check word count first
        if (wordCount < wordCountRange.min || wordCount > wordCountRange.max) {
            const difference = wordCount < wordCountRange.min
                ? `${wordCountRange.min - wordCount} words too short`
                : `${wordCount - wordCountRange.max} words too long`;
            throw new Error(`Word count (${wordCount}) is outside the required range (${wordCountRange.min}-${wordCountRange.max}). Article is ${difference}.`);
        }

        // Create validation prompt that's more lenient
        const validationPrompt = `Analyze this content and verify if it meets the available requirements. For each requirement, respond with PASS or FAIL and a brief explanation.
Note: The content should try to incorporate available brand elements naturally where relevant, but not all elements must be present.

Content to analyze:
Title: ${title}
Content preview: ${content.substring(0, 1000)}...
Word count: ${wordCount} words (required range: ${wordCountRange.min}-${wordCountRange.max})

Requirements to check:
${requirements.join('\n')}

Analyze each requirement and provide a detailed response.`;

        const validation = await this.callOpenAI(
            [
                {
                    role: "system",
                    content: "You are a content quality expert. Analyze content against specific requirements and provide detailed feedback. Be strict about word count but very lenient about other requirements - they should be considered as guidelines rather than strict rules. Only fail content that severely violates word count requirements."
                },
                {
                    role: "user",
                    content: validationPrompt
                }
            ],
            0.1,
            500
        );

        // Parse validation results
        const results = validation.split('\n').filter(line => line.trim().length > 0);

        // Only consider word count failures as critical
        const failures = results.filter(line => {
            const isFailure = line.toLowerCase().includes('fail');
            const isWordCount = line.toLowerCase().includes('word count');
            return isFailure && isWordCount;
        });

        if (failures.length > 0) {
            throw new Error(`Content validation failed:\n${failures.join('\n')}`);
        }

        return true;
    }

    async generateArticle(websiteData, existingArticles = []) {
        try {
            // Create a summary of existing articles to ensure uniqueness
            const existingArticlesSummary = existingArticles.map(article => ({
                title: article.title,
                content: article.content.substring(0, 200) // First 200 chars for context
            }));

            // Get domain settings
            const domain = websiteData.domain;
            if (!domain || !domain.brandInfo || !domain.articleSettings) {
                throw new Error('Invalid domain data: Missing required settings');
            }

            const brandInfo = domain.brandInfo;
            const articleSettings = domain.articleSettings;

            // Default ranges if not provided
            const defaultRanges = {
                short: { min: 50, max: 100 },
                medium: { min: 100, max: 200 },
                long: { min: 200, max: 300 }
            };

            // Get word count range for the selected article length
            let wordCountRange;

            // Handle both Map and regular object cases
            if (articleSettings.wordCountRanges instanceof Map) {
                wordCountRange = articleSettings.wordCountRanges.get(articleSettings.articleLength);
            } else if (typeof articleSettings.wordCountRanges === 'object') {
                wordCountRange = articleSettings.wordCountRanges[articleSettings.articleLength];
            }

            // Use default range if no valid range is found
            if (!wordCountRange || typeof wordCountRange.min !== 'number' || typeof wordCountRange.max !== 'number') {
                wordCountRange = defaultRanges[articleSettings.articleLength];
                if (!wordCountRange) {
                    throw new Error(`Invalid article length: ${articleSettings.articleLength}`);
                }
            }

            // Create system message with strict language and brand requirements
            const systemMessage = `You are a professional content writer specializing in ${brandInfo.industry} content.
CRITICAL REQUIREMENTS - YOU MUST FOLLOW THESE EXACTLY:

⚠️ MOST IMPORTANT - WORD COUNT REQUIREMENT ⚠️
You MUST write content that is EXACTLY between ${wordCountRange.min} to ${wordCountRange.max} words.
This is a STRICT requirement. The content will be rejected if it's even 1 word under ${wordCountRange.min} or over ${wordCountRange.max}.
Count your words carefully before completing the response.
TIP: Aim for the middle of the range (around ${Math.floor((wordCountRange.min + wordCountRange.max) / 2)} words) to ensure compliance.

⚠️ UNIQUENESS REQUIREMENT ⚠️
You MUST create COMPLETELY UNIQUE content that is different from existing articles.
Analyze the existing articles provided and ensure your content has:
- A unique title that doesn't resemble existing ones
- A unique approach, angle, or perspective on the topic
- Different examples, metaphors, and supporting points
- Unique structure and flow

Other Requirements:
1. LANGUAGE: Write ONLY in ${articleSettings.language}. This is non-negotiable.
2. BRAND VOICE: Use ONLY ${brandInfo.toneOfVoice} tone throughout the article.
3. TARGET AUDIENCE: Write SPECIFICALLY for ${brandInfo.targetAudience} in ${brandInfo.audienceLocation}.
4. INDUSTRY FOCUS: Write with expertise in ${brandInfo.industry} industry.
5. BRAND ALIGNMENT: Incorporate ${brandInfo.name}'s unique value proposition: ${brandInfo.description}
${brandInfo.benefits?.length > 0 ? `6. BENEFITS: Try to naturally incorporate these benefits where relevant (not all must be used): ${brandInfo.benefits.join(', ')}` : ''}
${brandInfo.tags?.length > 0 ? `7. KEYWORDS: Use these keywords naturally: ${brandInfo.tags.join(', ')}` : ''}
${articleSettings.specificInstructions ? `8. REQUIRED ELEMENTS: Include: ${articleSettings.specificInstructions}` : ''}
${articleSettings.exclusions ? `9. PROHIBITED ELEMENTS: DO NOT include: ${articleSettings.exclusions}` : ''}

Article Topic: ${websiteData.topic || 'Generate a relevant topic based on brand info and settings'}

Format using semantic HTML (no H1 tags). Maintain high perplexity and burstiness for natural, human-like writing.

⚠️ FINAL CHECKLIST BEFORE SUBMITTING ⚠️
1. Have you counted the words? It MUST be between ${wordCountRange.min}-${wordCountRange.max}
2. Is it formatted using semantic HTML? (no H1 tags)
3. Does it maintain high perplexity and burstiness for natural, human-like writing?
4. Have you included all required elements and avoided all exclusions?
5. Have you incorporated benefits naturally where they fit?
6. Is your content COMPLETELY UNIQUE compared to the existing articles?

DO NOT submit the response until you've verified all requirements are met, especially the word count and uniqueness.`;

            // Create the appropriate prompt based on settings
            const prompt = articleSettings.useBrandInfo
                ? this.createBrandedPrompt(websiteData, brandInfo, articleSettings, existingArticlesSummary)
                : this.createBasicPrompt(websiteData, articleSettings, existingArticlesSummary);

            let attempts = 0;
            const maxAttempts = 6; // Increased from 4 to 6 attempts
            let lastError = null;

            while (attempts < maxAttempts) {
                try {
                    // Generate initial content
                    const content = await this.callOpenAI(
                        [
                            { role: "system", content: systemMessage },
                            { role: "user", content: prompt }
                        ],
                        0.8, // Increased temperature for more creativity and uniqueness
                        this.getMaxTokensByLength(articleSettings.articleLength, articleSettings.wordCountRanges)
                    );

                    // Remove any h1 tags if they exist
                    let cleanContent = content.replace(/<h1[^>]*>.*?<\/h1>/g, '');

                    // Create title prompt requirements dynamically
                    const titleRequirements = [
                        "Must be between 40-60 characters",
                        "Must be unique and engaging"
                    ];

                    if (existingArticlesSummary.length > 0) {
                        titleRequirements.push(`Must be COMPLETELY DIFFERENT from these existing titles: ${existingArticlesSummary.map(a => `"${a.title}"`).join(', ')}`);
                    }

                    if (articleSettings?.language) {
                        titleRequirements.push(`Must be in ${articleSettings.language}`);
                    }

                    if (brandInfo?.toneOfVoice) {
                        titleRequirements.push(`Must use ${brandInfo.toneOfVoice} tone`);
                    }

                    if (brandInfo?.targetAudience) {
                        titleRequirements.push(`Must appeal to ${brandInfo.targetAudience}`);
                    }

                    if (brandInfo?.industry) {
                        titleRequirements.push(`Must be relevant to ${brandInfo.industry} industry`);
                    }

                    if (brandInfo?.name) {
                        titleRequirements.push(`Must align with ${brandInfo.name}'s brand identity`);
                    }

                    titleRequirements.push("Must include a primary keyword if possible");

                    // Create title prompt
                    const titlePrompt = `Create a unique, SEO-friendly title for this article that is COMPLETELY DIFFERENT from any existing titles.

Content Preview:
${cleanContent.substring(0, 500)}...

${existingArticlesSummary.length > 0 ? `
EXISTING TITLES TO AVOID SIMILARITY WITH:
${existingArticlesSummary.map(a => `- "${a.title}"`).join('\n')}
` : ''}

REQUIREMENTS:
${titleRequirements.map((req, index) => `${index + 1}. ${req}`).join('\n')}

Return ONLY the title, no other text.`;

                    // Generate title with higher temperature for more uniqueness
                    const title = await this.callOpenAI(
                        [
                            {
                                role: "system",
                                content: `You are an SEO expert who creates unique, engaging titles${articleSettings?.language ? ` in ${articleSettings.language}` : ''
                                    }${brandInfo?.toneOfVoice ? ` with a ${brandInfo.toneOfVoice} tone` : ''
                                    }. Your titles must be completely original and different from existing ones.`
                            },
                            { role: "user", content: titlePrompt }
                        ],
                        0.9, // Higher temperature for more creative and unique titles
                        50
                    );

                    // Clean up the content
                    cleanContent = this.cleanAndStyleContent(cleanContent);

                    // Validate the content
                    await this.validateContent(cleanContent, title.trim(), articleSettings, brandInfo, wordCountRange, existingArticlesSummary);

                    // Check for uniqueness compared to existing articles
                    if (existingArticlesSummary.length > 0) {
                        const uniquenessPrompt = `Evaluate if this new content is sufficiently unique compared to existing articles.

NEW TITLE: "${title.trim()}"

NEW CONTENT PREVIEW: 
${cleanContent.substring(0, 1000)}...

EXISTING ARTICLES:
${existingArticlesSummary.map((a, i) => `Article ${i + 1}:\nTitle: "${a.title}"\nContent: "${a.content}..."`).join('\n\n')}

Rate the uniqueness on a scale of 1-10, where:
1-2 = Almost identical, clearly plagiarized
3-4 = Very similar with only minor differences
5-6 = Moderately unique with some similar themes
7-10 = Very unique, clearly different approach and content

Only respond with a number between 1 and 10.`;

                        const uniquenessScore = await this.callOpenAI(
                            [
                                { role: "system", content: "You are an expert content analyst who evaluates content uniqueness." },
                                { role: "user", content: uniquenessPrompt }
                            ],
                            0.1, // Low temperature for consistent evaluation
                            10
                        );

                        // Parse the uniqueness score
                        const score = parseInt(uniquenessScore.trim(), 10);

                        // If score is below 4, reject and try again (lowered from 7 to be less strict)
                        if (!isNaN(score) && score < 4) {
                            throw new Error(`Content not unique enough (score: ${score}/10). Generating new content.`);
                        }
                    }

                    // If validation passes, continue with the rest of the generation
                    // Extract description from first paragraph if not provided
                    const descMatch = cleanContent.match(/<p.*?>(.*?)<\/p>/);
                    let description = websiteData.description || (descMatch ? descMatch[1].trim() : '');

                    // Ensure description is unique if we have existing articles
                    if (existingArticlesSummary.length > 0) {
                        const descriptionPrompt = `Create a unique, engaging meta description for this article that is COMPLETELY DIFFERENT from existing ones.

Article Title: ${title.trim()}
Article Content Preview: ${cleanContent.substring(0, 500)}...

${existingArticlesSummary.length > 0 ? `
EXISTING ARTICLES TO AVOID SIMILARITY WITH:
${existingArticlesSummary.map((a, i) => `Article ${i + 1}:\nTitle: "${a.title}"\nContent: "${a.content}..."`).join('\n\n')}
` : ''}

Requirements:
1. Must be between 120-160 characters
2. Must be compelling and include main keywords
3. Must be COMPLETELY UNIQUE compared to existing articles
4. Must be in ${articleSettings.language}
5. Must match the ${brandInfo.toneOfVoice} tone of the article

Return ONLY the description, no other text.`;

                        description = await this.callOpenAI(
                            [
                                { role: "system", content: "You create unique, engaging meta descriptions for articles." },
                                { role: "user", content: descriptionPrompt }
                            ],
                            0.8, // Higher temperature for uniqueness
                            50
                        );
                    }

                    // Generate keywords
                    const keywordsPrompt = `Generate 3-5 SEO keywords for this article.

Content Context:
Title: ${title}
Description: ${description}
Brand Tags: ${brandInfo.tags.join(', ')}
Industry: ${brandInfo.industry}
Target Audience: ${brandInfo.targetAudience}

STRICT REQUIREMENTS:
1. MUST be in ${articleSettings.language} language
2. MUST match ${brandInfo.industry} industry focus
3. MUST target ${brandInfo.targetAudience} audience
4. MUST align with ${brandInfo.name}'s brand identity
5. Return EXACTLY 3 to 5 individual words
6. Each word must be a single word
7. Words must be SEO-friendly
${articleSettings.specificInstructions ? `8. Consider including: ${articleSettings.specificInstructions}` : ''}
${articleSettings.exclusions ? `9. Avoid: ${articleSettings.exclusions}` : ''}

Return only the keywords, one per line, no other text.`;

                    const keywordsResponse = await this.callOpenAI(
                        [
                            { role: "system", content: `You are an SEO expert. Generate ONLY 3-5 individual words as keywords in ${articleSettings.language}, one word per line.` },
                            { role: "user", content: keywordsPrompt }
                        ],
                        0.7,
                        50
                    );

                    const keywords = keywordsResponse
                        .toLowerCase()
                        .split('\n')
                        .map(word => word.trim())
                        .filter(word => word.length > 0)
                        .slice(0, 5);

                    // Generate image search query using brand context
                    const imageSearchQuery = await this.generateImageSearchQuery(
                        title,
                        description,
                        `${brandInfo.industry} industry, ${brandInfo.toneOfVoice} style, for ${brandInfo.targetAudience}`
                    );
                    const unsplashService = require('./unsplashService');
                    const image = await unsplashService.searchImage(imageSearchQuery).catch(error => {
                        console.error('Image search error:', error);
                        return null;
                    });

                    // Generate video search query using brand context
                    const videoSearchQuery = await this.generateVideoSearchQuery(
                        title,
                        description,
                        `${brandInfo.industry} business, ${brandInfo.toneOfVoice} tone, for ${brandInfo.targetAudience}`
                    );
                    const pexelsService = require('./pexelsService');
                    const video = await pexelsService.searchVideo(videoSearchQuery).catch(error => {
                        console.error('Video search error:', error);
                        return null;
                    });

                    // Insert video in the middle of the content if available
                    if (video) {
                        const paragraphs = cleanContent.split('</p>');
                        const middleIndex = Math.floor(paragraphs.length / 2);

                        const videoHtml = `
                            <div class="article-video-container">
                                <video 
                                    controls 
                                    poster="${video.thumbnail}"
                                    width="${video.width}"
                                    height="${video.height}"
                                    class="article-video">
                                    <source src="${video.url}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                                ${video.description ? `<p class="video-caption">${video.description}</p>` : ''}
                                <p class="video-credit">Video by <a href="${video.credit.url}" target="_blank" rel="noopener noreferrer">${video.credit.name}</a> on Pexels</p>
                            </div>
                        `;

                        paragraphs.splice(middleIndex, 0, videoHtml);
                        cleanContent = paragraphs.join('</p>');
                    }

                    // Final validation
                    const finalValidationPrompt = `Verify if this content meets the basic requirements:

Content preview:
${cleanContent.substring(0, 500)}...

Required checks:
1. Language: Must be in ${articleSettings.language}
2. Word count: Must be between ${wordCountRange.min}-${wordCountRange.max} words

Optional checks (evaluate only if specified):
${brandInfo.toneOfVoice ? `- Tone: Should use ${brandInfo.toneOfVoice} tone` : ''}
${brandInfo.industry ? `- Industry: Should relate to ${brandInfo.industry} industry` : ''}
${brandInfo.targetAudience ? `- Target audience: Should be suitable for ${brandInfo.targetAudience}` : ''}
${brandInfo.name ? `- Brand alignment: Should align with ${brandInfo.name}'s identity` : ''}
${brandInfo.benefits?.length > 0 ? `- Benefits: Consider mentioning these where relevant: ${brandInfo.benefits.join(', ')}` : ''}
${articleSettings.specificInstructions ? `- Required elements: ${articleSettings.specificInstructions}` : ''}
${articleSettings.exclusions ? `- Excluded elements: ${articleSettings.exclusions}` : ''}

Return "VALID" if the required checks pass (language and word count).
Return "INVALID" only if the language or word count requirements are not met.
Optional checks should not cause an INVALID result.`;

                    const finalValidation = await this.callOpenAI(
                        [
                            {
                                role: "system",
                                content: "You are a content quality expert. Verify if content meets the basic requirements (language and word count). Be lenient with optional brand-related requirements."
                            },
                            { role: "user", content: finalValidationPrompt }
                        ],
                        0.1,
                        10
                    );

                    if (finalValidation.trim() === "INVALID") {
                        throw new Error("Generated content does not meet all specified requirements. Please try again.");
                    }

                    return {
                        title: title.trim(),
                        description: description || websiteData.description,
                        content: cleanContent,
                        tag: brandInfo.industry,
                        status: "published",
                        author: "AI Writer",
                        date: new Date().toLocaleDateString('en-GB', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric'
                        }),
                        image: image || {
                            url: '/lovable-uploads/default-article.png',
                            thumb: '/lovable-uploads/default-article-thumb.png',
                            description: title,
                            credit: null
                        },
                        video: video,
                        keywords: keywords,
                        sourceUrl: websiteData.url,
                        metadata: {
                            originalTitle: websiteData.title,
                            originalDescription: websiteData.description,
                            generatedAt: new Date().toISOString(),
                            usedBrandInfo: articleSettings.useBrandInfo,
                            language: articleSettings.language,
                            brandName: brandInfo.name,
                            industry: brandInfo.industry,
                            targetAudience: brandInfo.targetAudience,
                            toneOfVoice: brandInfo.toneOfVoice,
                            articleLength: articleSettings.articleLength,
                            wordCountRange: {
                                min: wordCountRange.min,
                                max: wordCountRange.max
                            },
                            domainId: domain.id
                        }
                    };
                } catch (error) {
                    lastError = error;
                    attempts++;
                    console.log(`Attempt ${attempts} failed: ${error.message}`);

                    if (attempts >= maxAttempts) {
                        throw new Error(`Failed to generate valid content after ${maxAttempts} attempts. Last error: ${lastError.message}`);
                    }
                }
            }
        } catch (error) {
            console.error('Error generating article:', error);
            throw error;
        }
    }

    createBrandedPrompt(websiteData, brandInfo, articleSettings, existingArticlesSummary = []) {
        // Get word count range
        let wordCountRange;
        if (articleSettings.wordCountRanges instanceof Map) {
            wordCountRange = articleSettings.wordCountRanges.get(articleSettings.articleLength);
        } else if (typeof articleSettings.wordCountRanges === 'object') {
            wordCountRange = articleSettings.wordCountRanges[articleSettings.articleLength];
        }

        // Use default range if no valid range is found
        if (!wordCountRange || typeof wordCountRange.min !== 'number' || typeof wordCountRange.max !== 'number') {
            const defaultRanges = {
                short: { min: 50, max: 100 },
                medium: { min: 100, max: 200 },
                long: { min: 200, max: 300 }
            };
            wordCountRange = defaultRanges[articleSettings.articleLength] || defaultRanges.medium;
        }

        // Build dynamic input fields based on available data
        const dynamicFields = [];

        if (brandInfo.name) dynamicFields.push(`Brand Name: ${brandInfo.name}`);
        if (brandInfo.description) dynamicFields.push(`Description: ${brandInfo.description}`);
        if (brandInfo.targetAudience) dynamicFields.push(`Target Audience: ${brandInfo.targetAudience}`);
        if (brandInfo.audienceLocation) dynamicFields.push(`Audience Location: ${brandInfo.audienceLocation}`);
        if (brandInfo.benefits?.length > 0) dynamicFields.push(`Benefits: ${brandInfo.benefits.join(', ')}`);
        if (brandInfo.toneOfVoice) dynamicFields.push(`Tone of Voice: ${brandInfo.toneOfVoice}`);
        if (brandInfo.industry) dynamicFields.push(`Industry: ${brandInfo.industry}`);
        if (brandInfo.tags?.length > 0) dynamicFields.push(`Tags / Keywords: ${brandInfo.tags.join(', ')}`);

        // Required fields
        dynamicFields.push(`Language: ${articleSettings.language || 'English'}`);
        dynamicFields.push(`Article Length: EXACTLY between ${wordCountRange.min} to ${wordCountRange.max} words (STRICT REQUIREMENT)`);

        if (articleSettings.specificInstructions) {
            dynamicFields.push(`Specific Inclusions (must include): ${articleSettings.specificInstructions}`);
        }
        if (articleSettings.exclusions) {
            dynamicFields.push(`Specific Exclusions (must avoid): ${articleSettings.exclusions}`);
        }

        // Add existing articles section for uniqueness
        let existingArticlesSection = '';
        if (existingArticlesSummary && existingArticlesSummary.length > 0) {
            existingArticlesSection = `
EXISTING ARTICLES (YOU MUST CREATE CONTENT COMPLETELY DIFFERENT FROM THESE):
${existingArticlesSummary.map((article, index) => `
Article ${index + 1}: "${article.title}"
${article.content}...
`).join('\n')}

CRITICAL: Your content must be 100% unique and different from the above articles in:
- Title and headline approach
- Content structure and organization
- Examples, metaphors, and supporting points
- Overall perspective and angle on the topic
`;
        }

        return `You are an elite-level content writer trained to produce outstanding, formal, humanlike articles suitable for publication on global websites and professional platforms.
Your writing must reflect deep understanding of purpose, structure, clarity, emotional engagement, and strategic language use — with a distinct focus on perplexity, burstiness, and depth.
 
This article is not just content — it is a representation of a brand's voice, a communication tool, and a conversion engine.
You must treat each instruction as mandatory and execute every detail with care.
 
Available Input Fields:
${dynamicFields.join('\n')}
${existingArticlesSection}
 
Smart Writing Instructions (PRIORITIES)
1. Clear Purpose & Audience
${brandInfo.targetAudience ? `\nWrite directly for ${brandInfo.targetAudience}${brandInfo.audienceLocation ? ` in ${brandInfo.audienceLocation}` : ''}.` : '\nWrite for a general audience with a professional tone.'}
${brandInfo.description ? `\nEnsure the article aligns with the brand's goal: ${brandInfo.description}` : ''}

2. Content Structure & Quality
- Write engaging, informative content that provides value to readers
- Use clear headings and subheadings for easy navigation
- Keep paragraphs concise and focused
${brandInfo.toneOfVoice ? `- Maintain a ${brandInfo.toneOfVoice} tone throughout` : ''}
${brandInfo.industry ? `- Show expertise in the ${brandInfo.industry} industry` : ''}

3. Key Elements to Include
${brandInfo.benefits?.length > 0 ? `- Try to naturally incorporate relevant benefits: ${brandInfo.benefits.join(', ')}` : ''}
${brandInfo.tags?.length > 0 ? `- Use appropriate keywords naturally: ${brandInfo.tags.join(', ')}` : ''}
${articleSettings.specificInstructions ? `- Required elements: ${articleSettings.specificInstructions}` : ''}

4. Elements to Avoid
${articleSettings.exclusions ? `- Strictly avoid: ${articleSettings.exclusions}` : '- Avoid jargon, clichés, and repetitive phrasing'}
- DO NOT include word count information anywhere in the article
- DO NOT add "Word Count" or number of words at the end of the article

5. Final Requirements
- Write in ${articleSettings.language || 'English'}
- STRICT word count: ${wordCountRange.min}-${wordCountRange.max} words
- Use semantic HTML (<h2>, <h3>, <p>, <ul>, <li>, <strong>)
- Maintain high perplexity and burstiness for natural writing
- No H1 tags allowed
- MUST be 100% unique compared to existing articles
- DO NOT include word count in the article content

Return the article as properly formatted HTML content.`;
    }

    createBasicPrompt(websiteData, articleSettings, existingArticlesSummary = []) {
        // Get word count range
        let wordCountRange;
        if (articleSettings.wordCountRanges instanceof Map) {
            wordCountRange = articleSettings.wordCountRanges.get(articleSettings.articleLength);
        } else if (typeof articleSettings.wordCountRanges === 'object') {
            wordCountRange = articleSettings.wordCountRanges[articleSettings.articleLength];
        }

        // Use default range if no valid range is found
        if (!wordCountRange || typeof wordCountRange.min !== 'number' || typeof wordCountRange.max !== 'number') {
            const defaultRanges = {
                short: { min: 50, max: 100 },
                medium: { min: 100, max: 200 },
                long: { min: 200, max: 300 }
            };
            wordCountRange = defaultRanges[articleSettings.articleLength] || defaultRanges.medium;
        }

        // Add existing articles section for uniqueness
        let existingArticlesSection = '';
        if (existingArticlesSummary && existingArticlesSummary.length > 0) {
            existingArticlesSection = `
EXISTING ARTICLES (YOU MUST CREATE CONTENT COMPLETELY DIFFERENT FROM THESE):
${existingArticlesSummary.map((article, index) => `
Article ${index + 1}: "${article.title}"
${article.content}...
`).join('\n')}

CRITICAL: Your content must be 100% unique and different from the above articles in:
- Title and headline approach
- Content structure and organization
- Examples, metaphors, and supporting points
- Overall perspective and angle on the topic
`;
        }

        return `Act as a top-tier professional writer trained in editorial-quality content creation.
You are tasked with crafting a humanlike, formal article that sounds authentic, flows naturally, and is rich in structure, substance, and style.
The article must reflect high perplexity and burstiness — meaning it should avoid robotic patterns, instead using dynamic sentence structures, fluid rhythm, and smart transitions.
 
INPUTS:
Brand Name: ${websiteData.title}  
Article Length: EXACTLY between ${wordCountRange.min} to ${wordCountRange.max} words (STRICT REQUIREMENT)  
Language (by default): ${articleSettings.language || 'English'}  
Specific Instructions (Must Include): ${articleSettings.specificInstructions || 'None'}  
Exclusions (Must Avoid): ${articleSettings.exclusions || 'None'}
${existingArticlesSection}
 
WRITING INSTRUCTIONS:
1. Content Structure
- Create a clear, engaging title (H2 format)
- Include an introduction that hooks the reader
- Organize with logical sections and subheadings (H3 format)
- Write concise, focused paragraphs
- End with a conclusion or call to action

2. Style & Tone
- Write in a professional, authoritative voice
- Use varied sentence structures for natural flow
- Incorporate appropriate industry terminology
- Maintain consistent tone throughout

3. Technical Requirements
- Format using semantic HTML (<h2>, <h3>, <p>, <ul>, <li>, <strong>)
- NO H1 tags (use H2 for main title)
- Ensure proper nesting of elements
- Use proper paragraph breaks for readability
- DO NOT include word count information anywhere in the article
- DO NOT add "Word Count" or number of words at the end of the article

4. Content Quality
- Provide valuable, actionable information
- Back claims with reasoning (not specific data unless provided)
- Focus on practical applications and benefits
- Avoid fluff, repetition, and obvious statements
- MUST be unique compared to existing articles

Return ONLY the formatted HTML content, nothing else.`;
    }

    getMaxTokensByLength(length, wordCountRanges) {
        // Default token ranges based on word count
        const defaultTokens = {
            short: 800,    // For 50-100 words
            medium: 1600,  // For 100-200 words
            long: 2400     // For 200-300 words
        };

        // Get the word count range
        let range;
        if (wordCountRanges instanceof Map) {
            range = wordCountRanges.get(length);
        } else if (typeof wordCountRanges === 'object') {
            range = wordCountRanges[length];
        }

        // If no valid range found, use default
        if (!range || !range.max) {
            return defaultTokens[length] || 1600; // Default to medium if length is invalid
        }

        // Calculate tokens based on max words (roughly 4 tokens per word plus overhead)
        return Math.ceil(range.max * 4 * 1.2); // 20% overhead for HTML tags and formatting
    }

    getWordCountByLength(length, wordCountRanges) {
        // If custom ranges are provided, use them
        if (wordCountRanges && wordCountRanges[length]) {
            const range = wordCountRanges[length];
            return `${range.min} - ${range.max}`;
        }

        // Default ranges if not provided
        const defaultRanges = {
            short: { min: 50, max: 100 },
            medium: { min: 100, max: 200 },
            long: { min: 200, max: 300 }
        };

        const range = defaultRanges[length] || defaultRanges.medium;
        return `${range.min} - ${range.max}`;
    }

    async generateKeywords(title, description, brandTags = []) {
        try {
            const prompt = `Generate exactly 3-5 single-word SEO keywords for this article. 

Title: ${title}
Description: ${description}
Brand Tags: ${brandTags.join(', ')}

Requirements:
- Return EXACTLY 3 to 5 individual words
- Each word must be a single word (no phrases, no hyphens)
- Words should be simple and SEO-friendly
- Return each word on a new line
- Example format:
shopping
amazon
online
retail
ecommerce

Return only the keywords, one per line, no other text.`;

            const completion = await this.openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are an SEO expert. Generate ONLY 3-5 individual words as keywords, one word per line."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.7,
                max_tokens: 50
            });

            let keywords = completion.choices[0].message.content
                .toLowerCase()
                .split('\n')
                .map(word => word.trim())
                .filter(word => {
                    // Only keep single words without spaces or special characters
                    return word.length > 0 &&
                        !word.includes(' ') &&
                        !word.includes('-') &&
                        /^[a-z]+$/.test(word);
                })
                .slice(0, 5); // Ensure max 5 keywords

            // If we don't have enough keywords, use the fallback method
            if (keywords.length < 3) {
                const fallbackKeywords = this.extractKeywords(title + ' ' + description);
                keywords = [...new Set([...keywords, ...fallbackKeywords])].slice(0, 5);
            }

            // Ensure we have at least 3 keywords
            if (keywords.length < 3) {
                const commonWords = this.extractKeywords(title);
                keywords = [...new Set([...keywords, ...commonWords])].slice(0, 5);
            }

            return keywords.slice(0, Math.max(3, Math.min(5, keywords.length)));
        } catch (error) {
            console.error('Error generating keywords:', error);
            return this.extractKeywords(title + ' ' + description);
        }
    }

    async generateImageSearchQuery(title, description, brandContext = '') {
        try {
            const prompt = `Generate a creative and unique image search query (3-5 words) for an article with:

Title: ${title}
Description: ${description}
Brand Context: ${brandContext}

Requirements:
- Focus on the main subject/theme
- Use concrete, visual terms
- Keep it under 5 words
- Make it relevant for stock photos
- No special characters or punctuation
- Be creative and avoid obvious/literal terms
- Consider metaphors or related concepts
- Make it different from previous queries for similar topics

Return only the search query words, nothing else.`;

            const completion = await this.openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are a creative assistant that generates unique and diverse image search queries. Return only the search query, no other text."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.9, // Increased temperature for more creativity
                max_tokens: 50
            });

            return completion.choices[0].message.content.trim();
        } catch (error) {
            console.error('Error generating image search query:', error);
            // Return a more diverse fallback by combining random words from title
            const words = title.split(' ').filter(w => w.length > 3);
            const randomWords = [];
            while (randomWords.length < 3 && words.length > 0) {
                const index = Math.floor(Math.random() * words.length);
                randomWords.push(words.splice(index, 1)[0]);
            }
            return randomWords.join(' ');
        }
    }

    async generateVideoSearchQuery(title, description, brandContext = '') {
        try {
            const prompt = `Generate a creative and unique video search query (3-5 words) for an article with:

Title: ${title}
Description: ${description}
Brand Context: ${brandContext}

Requirements:
- Focus on dynamic, motion-related concepts
- Use concrete, visual terms
- Keep it under 5 words
- Make it relevant for stock videos
- No special characters or punctuation
- Be creative and avoid obvious/literal terms
- Consider action words and scenarios
- Make it different from previous queries for similar topics
- Focus on engaging, cinematic moments

Return only the search query words, nothing else.`;

            const completion = await this.openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are a creative assistant that generates unique and diverse video search queries. Return only the search query, no other text."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.9, // Increased temperature for more creativity
                max_tokens: 50
            });

            return completion.choices[0].message.content.trim();
        } catch (error) {
            console.error('Error generating video search query:', error);
            // Return a more diverse fallback by combining random words from title and adding an action word
            const actionWords = ['moving', 'flowing', 'working', 'creating', 'exploring'];
            const words = title.split(' ').filter(w => w.length > 3);
            const randomWords = [actionWords[Math.floor(Math.random() * actionWords.length)]];
            while (randomWords.length < 3 && words.length > 0) {
                const index = Math.floor(Math.random() * words.length);
                randomWords.push(words.splice(index, 1)[0]);
            }
            return randomWords.join(' ');
        }
    }

    createPrompt(websiteData, existingArticlesSummary = []) {
        const existingArticlesContext = existingArticlesSummary.length > 0
            ? `\nExisting articles on this domain:
${existingArticlesSummary.map((article, index) => `${index + 1}. Title: ${article.title}
   Preview: ${article.content}...`).join('\n')}`
            : '';

        return `
Please create a unique and engaging article based on the following website content:

Title: ${websiteData.title}
Description: ${websiteData.description}

Main Topics:
${websiteData.headings.join('\n')}

Reference Content:
${websiteData.mainContent}
${existingArticlesContext}

Requirements:
1. Create a well-structured article with:
   - A clear main title (different from existing articles)
   - A brief introduction
   - Well-organized sections with subheadings
   - Proper paragraphs and lists where appropriate
   - A conclusion

2. Use semantic HTML for structure:
   - <h2> for main sections
   - <h3> for subsections
   - <p> for paragraphs
   - <ul> and <li> for lists
   - <strong> for emphasis

3. Content guidelines:
   - Write at least 800 words
   - Make it SEO-friendly
   - Keep it engaging and informative
   - Include 3-5 relevant keywords
   - Ensure proper flow between sections
   - Must be unique and different from existing articles
   - Cover different aspects or perspectives than existing articles

4. DO NOT include:
   - HTML comments
   - Code block markers
   - Visible HTML tags in the text
   - Markdown syntax
   - Content too similar to existing articles

Return the article as properly formatted HTML content.`;
    }

    extractKeywords(content) {
        // Split content into individual words
        const words = content.toLowerCase()
            .replace(/[^a-z\s]/g, '') // Remove all non-letters
            .split(/\s+/); // Split on whitespace

        const stopWords = new Set([
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'a', 'an', 'is', 'are', 'was', 'were',
            'will', 'be', 'have', 'has', 'had', 'this', 'that', 'these', 'those', 'with', 'from', 'by', 'of',
            'they', 'them', 'their', 'what', 'when', 'where', 'who', 'which', 'why', 'how', 'all', 'any',
            'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'than', 'too', 'very', 'can',
            'will', 'just', 'should', 'now', 'into', 'only', 'about', 'like', 'been', 'its', 'over', 'also'
        ]);

        // Count word frequencies
        const wordFreq = {};
        words.forEach(word => {
            if (word.length > 2 && !stopWords.has(word)) {
                wordFreq[word] = (wordFreq[word] || 0) + 1;
            }
        });

        // Get the most frequent words
        return Object.entries(wordFreq)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([word]) => word)
            .filter(word => word.length > 2)
            .slice(0, Math.max(3, Math.min(5, Object.keys(wordFreq).length)));
    }

    cleanAndStyleContent(content) {
        return content
            // Remove any HTML comments
            .replace(/<!--[\s\S]*?-->/g, '')
            // Remove code block markers
            .replace(/```html/g, '')
            .replace(/```/g, '')
            // Remove any visible HTML tags in text
            .replace(/&lt;.*?&gt;/g, '')
            // Remove all variations of word count information
            .replace(/\*\*Word Count:.*?\*\*/g, '')
            .replace(/Word Count:.*?\n/g, '')
            .replace(/\d+ words?\./g, '')  // Remove "X words." format
            .replace(/\n\s*\d+\s+words?\s*\n/g, '\n')  // Remove word count on its own line
            .replace(/\(Word [Cc]ount:?\s*\d+\s*words?\)/g, '') // Remove (Word count: X words)
            .replace(/\(Word [Cc]ount:?\s*\d+\)/g, '') // Remove (Word count: X)
            .replace(/Word [Cc]ount:?\s*\d+\s*words?/g, '') // Remove Word count: X words
            .replace(/\(\d+\s*words?\)/g, '') // Remove (X words)
            .replace(/<p>\s*Word [Cc]ount:.*?<\/p>/g, '') // Remove <p>Word count: X words</p>
            .replace(/<p>\s*\(\d+\s*words?\)\s*<\/p>/g, '') // Remove <p>(X words)</p>
            // Remove Hindi word count variations
            .replace(/शब्द गणना:?\s*\d+\s*शब्द/g, '') // Hindi: शब्द गणना: X शब्द
            .replace(/\(शब्द गणना:?\s*\d+\s*शब्द\)/g, '') // Hindi: (शब्द गणना: X शब्द)
            .replace(/<p>\s*शब्द गणना:.*?<\/p>/g, '') // Hindi: <p>शब्द गणना: X शब्द</p>
            // Remove any paragraph that only contains numbers and the word "words" in any language
            .replace(/<p>\s*\d+\s*[a-zA-Z\u0900-\u097F\u0600-\u06FF\u0400-\u04FF]+\s*<\/p>/g, '')
            // Add styles to HTML elements
            .replace(/<h1>/g, '<h1 class="text-4xl font-bold mb-6">')
            .replace(/<h2>/g, '<h2 class="text-2xl font-bold mt-8 mb-4">')
            .replace(/<h3>/g, '<h3 class="text-xl font-bold mt-6 mb-3">')
            .replace(/<p>/g, '<p class="mb-4 leading-relaxed">')
            .replace(/<ul>/g, '<ul class="list-disc pl-6 mb-4 space-y-2">')
            .replace(/<ol>/g, '<ol class="list-decimal pl-6 mb-4 space-y-2">')
            .replace(/<li>/g, '<li class="mb-1">')
            .replace(/<strong>/g, '<strong class="font-bold">')
            // Add proper spacing
            .replace(/<\/h2>\s*<p>/g, '</h2>\n<p>')
            .replace(/<\/p>\s*<h2>/g, '</p>\n\n<h2>')
            // Clean up any extra whitespace
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            .trim();
    }

    // Helper method to extract title from content if needed
    extractTitleFromContent(content) {
        // Try to find first heading of any level
        const headingMatch = content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/);
        if (headingMatch) {
            return headingMatch[1].trim();
        }
        // Fallback to first sentence
        const firstSentence = content.split(/[.!?]/, 1)[0];
        return firstSentence.replace(/<[^>]+>/g, '').trim();
    }
}

module.exports = new GPTService(); 