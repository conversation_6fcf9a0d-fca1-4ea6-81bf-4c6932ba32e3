# Stripe Webhook Setup Guide

## Issue Identified
The Stripe webhook is not properly processing payment events because of signature verification issues. The webhook endpoint is correctly set up at `/api/payments/webhook` with the raw body parser, but the signature verification is failing.

## Solution

### 1. Set Up Stripe CLI for Local Testing

1. Download and install the [Stripe CLI](https://stripe.com/docs/stripe-cli)
2. Login to your Stripe account:
   ```
   stripe login
   ```
3. Forward webhook events to your local server:
   ```
   stripe listen --forward-to http://localhost:5000/api/payments/webhook
   ```
4. The CLI will provide a webhook signing secret. Copy this secret.

### 2. Configure Environment Variables

Create or update your `.env` file in the backend directory with the following variables:

```
# Stripe API Keys
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_signing_secret_here
```

Replace `your_stripe_secret_key_here` with your actual Stripe secret key and `whsec_your_webhook_signing_secret_here` with the webhook signing secret provided by the Stripe CLI.

### 3. Restart Your Server

After updating the environment variables, restart your server:

```
cd backend
npm run dev
```

### 4. Test the Webhook

You can test the webhook using the Stripe CLI:

```
stripe trigger payment_intent.succeeded
```

This will send a test event to your webhook endpoint.

## For Production

When deploying to production:

1. Create a webhook endpoint in the Stripe Dashboard:
   - Go to Developers > Webhooks > Add Endpoint
   - Enter your production webhook URL (e.g., https://yourdomain.com/api/payments/webhook)
   - Select the events you want to receive (at minimum: checkout.session.completed, payment_intent.succeeded, invoice.paid)

2. Copy the signing secret from the webhook details page in the Stripe Dashboard.

3. Set the `STRIPE_WEBHOOK_SECRET` environment variable in your production environment.

## Troubleshooting

If you're still experiencing issues:

1. Check the server logs for any error messages related to webhook signature verification.
2. Ensure the raw body parser middleware is correctly applied to the webhook route.
3. Verify that the webhook URL in Stripe matches the endpoint in your application.
4. Make sure the webhook signing secret is correctly set in your environment variables.

The code has been updated to handle cases where the webhook secret is not set, which is useful for development. However, for proper security in production, always set the webhook secret. 