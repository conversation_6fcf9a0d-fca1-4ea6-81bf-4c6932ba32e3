require('dotenv').config({ path: '../../.env' });
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Admin = require('../models/Admin');
const connectDB = require('../config/db');

// Admin credentials to verify
const adminEmail = '<EMAIL>';
const adminPassword = 'Admin@123';

async function verifyAdminCredentials() {
    try {
        // Connect to the database
        await connectDB();
        console.log('Connected to MongoDB');

        // Find the admin by email
        const admin = await Admin.findOne({ email: adminEmail });
        if (!admin) {
            console.error(`Admin with email ${adminEmail} not found`);
            console.log('Creating a new admin user...');

            // Create a new admin user
            const newAdmin = new Admin({
                username: 'admin',
                email: adminEmail,
                password: adminPassword,
                fullName: 'Admin',
                role: 'admin',
                permissions: ['manage_users', 'manage_articles', 'manage_domains', 'manage_settings', 'manage_admins'],
                isActive: true
            });

            await newAdmin.save();
            console.log(`New admin created with email ${adminEmail} and password ${adminPassword}`);
            process.exit(0);
        }

        // Verify the password
        const isMatch = await bcrypt.compare(adminPassword, admin.password);
        if (!isMatch) {
            console.log('Password does not match. Updating password...');

            // Update the password
            admin.password = adminPassword;
            admin.tokens = [];
            await admin.save();

            console.log(`Password for admin ${adminEmail} has been updated to ${adminPassword}`);
        } else {
            console.log(`Admin credentials are valid for ${adminEmail}`);

            // Clear tokens to ensure a fresh login
            admin.tokens = [];
            await admin.save();
            console.log('All authentication tokens have been cleared');
        }

        process.exit(0);
    } catch (error) {
        console.error('Error verifying admin credentials:', error);
        process.exit(1);
    }
}

verifyAdminCredentials(); 