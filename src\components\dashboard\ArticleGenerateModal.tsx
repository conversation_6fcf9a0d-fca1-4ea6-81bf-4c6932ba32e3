import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { X } from 'lucide-react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogClose,
} from '@/components/ui/dialog';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

// Comprehensive list of languages (same as in ContentSettings.tsx)
const LANGUAGES = [
    { value: 'English (India)', label: 'English (India)' },
    { value: 'English (US)', label: 'English (US)' },
    { value: 'English (UK)', label: 'English (UK)' },
    { value: 'Hindi', label: 'Hindi' },
    { value: 'Spanish', label: 'Spanish' },
    { value: 'French', label: 'French' },
    { value: 'German', label: 'German' },
    { value: 'Italian', label: 'Italian' },
    { value: 'Portuguese', label: 'Portuguese' },
    { value: 'Dutch', label: 'Dutch' },
    { value: 'Russian', label: 'Russian' },
    { value: 'Japanese', label: 'Japanese' },
    { value: 'Korean', label: 'Korean' },
    { value: 'Chinese (Simplified)', label: 'Chinese (Simplified)' },
    { value: 'Chinese (Traditional)', label: 'Chinese (Traditional)' },
    { value: 'Arabic', label: 'Arabic' },
    { value: 'Turkish', label: 'Turkish' },
    { value: 'Vietnamese', label: 'Vietnamese' },
    { value: 'Thai', label: 'Thai' },
    { value: 'Indonesian', label: 'Indonesian' }
];

interface ArticleGenerateModalProps {
    isOpen: boolean;
    onClose: () => void;
    onGenerate: (topic: string, language: string) => Promise<void>;
    defaultLanguage: string;
    isGenerating: boolean;
}

export const ArticleGenerateModal: React.FC<ArticleGenerateModalProps> = ({
    isOpen,
    onClose,
    onGenerate,
    defaultLanguage,
    isGenerating
}) => {
    const [topic, setTopic] = useState('');
    const [language, setLanguage] = useState(defaultLanguage);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        await onGenerate(topic, language);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px] bg-white border border-gray-200 rounded-lg shadow-md">
                <div className="absolute right-4 top-4">
                    <DialogClose className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none">
                        <X className="h-4 w-4" />
                        <span className="sr-only">Close</span>
                    </DialogClose>
                </div>
                <DialogHeader className="border-b pb-4">
                    <DialogTitle className="text-xl font-semibold text-gray-800">Generate New Article</DialogTitle>
                    <DialogDescription className="text-gray-600">
                        Enter a topic or keywords for your article and select a language.
                    </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit} className="space-y-4 py-4">
                    <div className="space-y-2">
                        <Label htmlFor="topic" className="text-sm font-medium text-gray-700">Topic or Keywords</Label>
                        <Input
                            id="topic"
                            placeholder="Enter topic, keywords, title, or description"
                            value={topic}
                            onChange={(e) => setTopic(e.target.value)}
                            className="border-orange-200 focus:border-orange-500 focus:ring-1 focus:ring-orange-500 rounded-md"
                            required
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="language" className="text-sm font-medium text-gray-700">Language</Label>
                        <div className="relative">
                            <select
                                id="language"
                                value={language}
                                onChange={(e) => setLanguage(e.target.value)}
                                className="w-full rounded-md border border-orange-200 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-orange-500 focus:outline-none focus:ring-1 focus:ring-orange-500"
                            >
                                {LANGUAGES.map((lang) => (
                                    <option key={lang.value} value={lang.value}>
                                        {lang.label}
                                    </option>
                                ))}
                            </select>
                            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-orange-500">
                                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="pt-4 border-t flex justify-end gap-2">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={isGenerating}
                            className="border-gray-300 hover:bg-gray-100 text-gray-700"
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isGenerating}
                            className="bg-orange-500 hover:bg-orange-600 text-white font-medium"
                        >
                            {isGenerating ? 'Generating...' : 'Generate Article'}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}; 