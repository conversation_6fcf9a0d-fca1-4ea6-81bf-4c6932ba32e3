import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DomainEntry } from './DomainEntry';
import { BrandSetup } from './BrandSetup';
import { TopicGeneration } from './TopicGeneration';
import { OnboardingComplete } from './OnboardingComplete';
import { WebsiteInfo } from '@/lib/services/websiteService';
import { GeneratedArticle } from '@/lib/services/articleService';
import { BrandInfo, Domain, domainService } from '@/services/domainService';
import { useDomain } from '@/contexts/DomainContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/use-toast';

interface OnboardingFlowProps {
  onComplete: () => void;
  onSkip: () => void;
}

export const OnboardingFlow = ({ onComplete, onSkip }: OnboardingFlowProps) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [websiteInfo, setWebsiteInfo] = useState<WebsiteInfo | null>(null);
  const [article, setArticle] = useState<GeneratedArticle | null>(null);
  const [currentDomain, setCurrentDomain] = useState<Domain | null>(null);
  const { refreshDomains } = useDomain();
  const { clearNewUserFlag } = useAuth();

  const handleNextStep = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const handleDomainData = async (data: { websiteInfo: WebsiteInfo; article: GeneratedArticle; domain: Domain }) => {
    setWebsiteInfo(data.websiteInfo);
    setArticle(data.article);
    setCurrentDomain(data.domain);
    handleNextStep();
  };

  const handleBrandData = async (brandInfo: BrandInfo) => {
    if (!currentDomain) {
      toast({
        title: 'Error',
        description: 'No domain selected',
        variant: 'destructive'
      });
      return;
    }

    try {
      await domainService.updateDomainBrandInfo(currentDomain._id, brandInfo);
      await refreshDomains();
      handleNextStep();
    } catch (error) {
      console.error('Error updating brand info:', error);
      toast({
        title: 'Error',
        description: 'Failed to update brand info',
        variant: 'destructive'
      });
    }
  };

  const handleComplete = () => {
    // Clear the new user flag when onboarding is completed
    clearNewUserFlag();
    onComplete();
    navigate('/dashboard/articles');
  };

  const handleSkip = () => {
    // Clear the new user flag even if onboarding is skipped
    clearNewUserFlag();
    onSkip();
    navigate('/dashboard/articles');
  };

  const steps = [
    <DomainEntry key="domain" onNext={handleNextStep} onData={handleDomainData} onSkip={handleSkip} />,
    <BrandSetup
      key="brand"
      onNext={handleComplete} // Changed to handleComplete to skip TopicGeneration
      onData={handleBrandData}
      initialData={{
        name: websiteInfo?.title || '',
        description: websiteInfo?.description || '',
        keywords: websiteInfo?.keywords || []
      }}
    />,
    <OnboardingComplete key="complete" onComplete={handleComplete} />
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-amber-50">
      {steps[currentStep]}
    </div>
  );
};
