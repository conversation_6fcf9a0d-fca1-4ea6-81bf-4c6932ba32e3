const axios = require('axios');

class PexelsService {
    constructor() {
        this.apiKey = process.env.PEXELS_API_KEY;
        this.baseUrl = 'https://api.pexels.com/videos';
        this.usedVideoIds = new Set(); // Keep track of used video IDs
    }

    async searchVideo(query) {
        try {
            if (!this.apiKey) {
                throw new Error('Pexels API key not configured');
            }

            // Fetch multiple videos
            const response = await axios.get(`${this.baseUrl}/search`, {
                params: {
                    query,
                    per_page: 10, // Fetch 10 videos to have a good selection
                    orientation: 'landscape',
                    size: 'medium'
                },
                headers: {
                    Authorization: this.apiKey
                }
            });

            if (response.data.videos && response.data.videos.length > 0) {
                // Filter out previously used videos
                const availableVideos = response.data.videos.filter(video => !this.usedVideoIds.has(video.id));

                // If all videos have been used, clear the history and use all videos
                if (availableVideos.length === 0) {
                    this.usedVideoIds.clear();
                    availableVideos.push(...response.data.videos);
                }

                // Randomly select a video from available ones
                const randomIndex = Math.floor(Math.random() * availableVideos.length);
                const video = availableVideos[randomIndex];

                // Mark this video as used
                this.usedVideoIds.add(video.id);

                // Find the best video file (prefer HD or medium quality)
                const videoFile = video.video_files.find(file =>
                    file.quality === 'hd' || file.quality === 'sd'
                ) || video.video_files[0];

                return {
                    url: videoFile.link,
                    width: videoFile.width,
                    height: videoFile.height,
                    duration: video.duration,
                    description: video.description || video.alt_description,
                    credit: {
                        name: video.user.name,
                        url: video.user.url
                    },
                    thumbnail: video.image
                };
            }

            return null;
        } catch (error) {
            console.error('Error fetching video from Pexels:', error);
            return null;
        }
    }

    // Method to clear used videos history if needed
    clearUsedVideosHistory() {
        this.usedVideoIds.clear();
    }
}

module.exports = new PexelsService(); 