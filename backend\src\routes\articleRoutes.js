const express = require('express');
const router = express.Router();
const articleController = require('../controllers/articleController');
const autoArticleService = require('../services/autoArticleService');
const { protect } = require('../middleware/auth');
const Domain = require('../models/Domain');

// Get all articles for a user
router.get('/', protect, articleController.getArticles);

// Get article by slug - must be before the /:id route to avoid conflict
router.get('/by-slug/:slug', protect, articleController.getArticleBySlug);

// Generate article
router.post('/generate', protect, articleController.generateArticle);

// Bulk generate articles
router.post('/bulk-generate', protect, async (req, res) => {
    try {
        const { domainId, count = 1, topics = [], language, autoPublish = false, useSettings = true } = req.body;

        if (!domainId) {
            return res.status(400).json({ error: 'Domain ID is required' });
        }

        // Check if domain exists and belongs to user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Detailed subscription check
        if (!domain.subscription) {
            return res.status(400).json({
                error: 'No subscription found',
                details: 'This domain does not have an active subscription.',
                subscription: null
            });
        }

        if (!domain.subscription.active) {
            return res.status(400).json({
                error: 'Inactive subscription',
                details: 'This domain has an inactive subscription.',
                subscription: domain.subscription
            });
        }

        if (domain.subscription.credits < count) {
            return res.status(400).json({
                error: 'Insufficient credits',
                details: `You need at least ${count} credits to generate ${count} articles, but you only have ${domain.subscription.credits} credits.`,
                subscription: domain.subscription
            });
        }

        // Check if subscription is expired
        if (domain.subscription.expiresAt && new Date(domain.subscription.expiresAt) < new Date()) {
            return res.status(400).json({
                error: 'Subscription expired',
                details: `Your subscription expired on ${new Date(domain.subscription.expiresAt).toISOString().split('T')[0]}.`,
                subscription: domain.subscription
            });
        }

        // Check if domain has brand info and article settings set up
        if (useSettings && (!domain.brandInfo || !domain.brandInfo.name)) {
            return res.status(400).json({
                error: 'Missing brand information',
                details: 'Please set up your brand information before generating articles.',
                brandInfo: domain.brandInfo
            });
        }

        if (useSettings && !domain.articleSettings) {
            return res.status(400).json({
                error: 'Missing article settings',
                details: 'Please set up your article settings before generating articles.',
                articleSettings: null
            });
        }

        // Start the bulk generation process
        const generatedArticles = [];
        let remainingCredits = domain.subscription.credits;

        // Generate articles one by one
        for (let i = 0; i < count; i++) {
            // Use custom topic if available
            const customTopic = topics && topics.length > i ? topics[i] : null;

            try {
                // Generate article using the auto article service
                const article = await autoArticleService.generateArticleNow(domainId, {
                    topic: customTopic,
                    language: language || (useSettings ? domain.articleSettings.language : null),
                    autoPublish,
                    useSettings
                });

                generatedArticles.push({
                    id: article._id,
                    title: article.title,
                    status: article.status,
                    date: article.date
                });

                // Update remaining credits
                remainingCredits--;
            } catch (articleError) {
                console.error(`Error generating article ${i + 1}:`, articleError);
                // Continue with next article
            }
        }

        res.json({
            success: true,
            generatedCount: generatedArticles.length,
            articles: generatedArticles,
            remainingCredits,
            subscription: {
                active: domain.subscription.active,
                planType: domain.subscription.planType,
                credits: remainingCredits,
                expiresAt: domain.subscription.expiresAt
            }
        });
    } catch (error) {
        console.error('Error in bulk article generation:', error);
        res.status(500).json({ error: 'Failed to generate articles', details: error.message });
    }
});

// Generate article automatically (for testing auto generation)
router.post('/generate-auto', protect, async (req, res) => {
    try {
        const { domainId } = req.body;

        if (!domainId) {
            return res.status(400).json({ error: 'Domain ID is required' });
        }

        // Check if domain exists and belongs to user
        const domain = await Domain.findOne({ _id: domainId, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found or not authorized' });
        }

        // Check if domain has active subscription and credits
        if (!domain.subscription || !domain.subscription.active || domain.subscription.credits <= 0) {
            return res.status(400).json({
                error: 'Domain has no active subscription or no credits',
                subscription: domain.subscription
            });
        }

        // Trigger auto article generation
        const article = await autoArticleService.generateArticleNow(domainId);

        res.json({
            success: true,
            article: {
                id: article._id,
                title: article.title,
                status: article.status,
                date: article.date,
                remainingCredits: domain.subscription.credits - 1
            }
        });
    } catch (error) {
        console.error('Error in auto article generation:', error);
        res.status(500).json({ error: 'Failed to generate article', details: error.message });
    }
});

// Get article by ID
router.get('/:id', protect, articleController.getArticleById);

// Create article
router.post('/', protect, articleController.createArticle);

// Update article
router.put('/:id', protect, articleController.updateArticle);

// Delete article
router.delete('/:id', protect, articleController.deleteArticle);

module.exports = router; 