import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAdmin } from '../../contexts/AdminContext';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from "../../components/ui/card";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "../../components/ui/table";
import { Button } from "../../components/ui/button";
import {
    ArrowLeft,
    Globe,
    Calendar,
    Mail,
    User,
    FileText,
    CreditCard,
    Download,
    ChevronDown,
    ChevronUp,
    Clock,
    ExternalLink,
    Search,
    DollarSign,
    ChevronLeft,
    ChevronRight
} from 'lucide-react';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "../../components/ui/accordion";
import { Badge } from "../../components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from '../../components/ui/avatar';
import { format } from 'date-fns';
import { transactionService, Transaction } from '@/services/transactionService';

interface UserDetails {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar: string;
    createdAt: string;
    lastLogin: string;
    isActive: boolean;
}

interface Article {
    _id: string;
    title: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    urlSlug?: string;
}

interface Domain {
    _id: string;
    name: string;
    url: string;
    title: string;
    description: string;
    brandInfo: {
        name: string;
        description: string;
        targetAudience: string;
        logo: string;
    };
    benefits: string[];
    toneOfVoice: string;
    industry: string;
    language: string;
    articleSettings: {
        userBrandInfo: boolean;
        articleLength: string;
    };
    designSettings: {
        logo: string;
        articleTheme: string;
        layout: {
            grid: string;
            titleOnImage: boolean;
        };
        colors: {
            brand: string;
            accent: string;
            font: string;
        };
    };
    domain: string;
    subdomain: string;
    isConnected: boolean;
    status: string;
    createdAt: string;
    updatedAt: string;
    articles?: Article[];
}

const UserView = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { token } = useAdmin();
    const [user, setUser] = useState<UserDetails | null>(null);
    const [domains, setDomains] = useState<Domain[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [expandedTransaction, setExpandedTransaction] = useState<string | null>(null);
    const [loadingInvoiceId, setLoadingInvoiceId] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize] = useState<number>(10);

    // Pagination calculation
    const totalPages = Math.ceil(transactions.length / pageSize);
    const paginatedTransactions = transactions.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
    );

    const goToNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const goToPreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    useEffect(() => {
        const fetchUserData = async () => {
            try {
                setLoading(true);
                setError(null);

                const [userResponse, domainsResponse, transactionsResponse] = await Promise.all([
                    axios.get(`http://localhost:5000/api/admin/users/${id}`, {
                        headers: { Authorization: `Bearer ${token}` }
                    }),
                    axios.get(`http://localhost:5000/api/admin/users/${id}/domains`, {
                        headers: { Authorization: `Bearer ${token}` }
                    }),
                    transactionService.getUserTransactionsAdmin(id!)
                ]);

                const domainsData = domainsResponse.data;

                // Fetch articles for each domain
                const domainsWithArticles = await Promise.all(
                    domainsData.map(async (domain: Domain) => {
                        const articlesResponse = await axios.get(
                            `http://localhost:5000/api/admin/domains/${domain._id}/articles`,
                            { headers: { Authorization: `Bearer ${token}` } }
                        );
                        return { ...domain, articles: articlesResponse.data };
                    })
                );

                setUser(userResponse.data);
                setDomains(domainsWithArticles);
                setTransactions(transactionsResponse);
                setCurrentPage(1); // Reset to first page when loading new data
            } catch (err) {
                console.error('Error fetching user data:', err);
                setError('Failed to load user data');
            } finally {
                setLoading(false);
            }
        };

        if (id) {
            fetchUserData();
        }
    }, [id, token]);

    // Add transaction helper functions
    const toggleTransactionDetails = (transactionId: string) => {
        if (expandedTransaction === transactionId) {
            setExpandedTransaction(null);
        } else {
            setExpandedTransaction(transactionId);
        }
    };

    const handleFetchInvoice = async (transactionId: string) => {
        try {
            setLoadingInvoiceId(transactionId);
            const invoiceUrl = await transactionService.generateInvoiceAdmin(transactionId);

            if (invoiceUrl) {
                // Update the transaction in the local state
                setTransactions(prevTransactions =>
                    prevTransactions.map(t =>
                        t._id === transactionId
                            ? { ...t, invoiceUrl }
                            : t
                    )
                );

                // Open the invoice URL
                transactionService.openInvoice(invoiceUrl);
            }
        } catch (error) {
            console.error('Error fetching invoice:', error);
        } finally {
            setLoadingInvoiceId(null);
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'completed':
                return <Badge className="bg-green-500 hover:bg-green-600 text-white font-medium px-3 py-1 rounded-full">Completed</Badge>;
            case 'pending':
                return <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white font-medium px-3 py-1 rounded-full">Pending</Badge>;
            case 'failed':
                return <Badge className="bg-red-500 hover:bg-red-600 text-white font-medium px-3 py-1 rounded-full">Failed</Badge>;
            case 'refunded':
                return <Badge className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-3 py-1 rounded-full">Refunded</Badge>;
            default:
                return <Badge className="px-3 py-1 rounded-full">{status}</Badge>;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    };

    // Add these handler functions to open Stripe pages
    const handleOpenStripeCustomer = (customerId: string) => {
        if (customerId) {
            transactionService.openStripeCustomer(customerId);
        }
    };

    const handleOpenStripeSubscription = (subscriptionId: string) => {
        if (subscriptionId) {
            transactionService.openStripeSubscription(subscriptionId);
        }
    };

    const handleOpenStripePaymentIntent = (paymentIntentId: string) => {
        if (paymentIntentId) {
            transactionService.openStripePaymentIntent(paymentIntentId);
        }
    };

    const handleOpenStripeCharge = (chargeId: string) => {
        if (chargeId) {
            transactionService.openStripeCharge(chargeId);
        }
    };

    const handleArticleClick = (article: Article) => {
        if (article.urlSlug) {
            window.open(`/admin/articles/preview/${article.urlSlug}`, '_blank');
        }
    };

    const getInitials = (firstName: string, lastName: string) => {
        return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
        );
    }

    if (error || !user) {
        return (
            <div className="flex flex-col items-center justify-center min-h-screen">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
                    <p className="text-gray-600 mb-4">{error || 'User not found'}</p>
                    <Button
                        variant="outline"
                        onClick={() => navigate('/admin/users')}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="w-4 h-4" />
                        Back to Users
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center gap-4 mb-6">
                <Button
                    variant="ghost"
                    onClick={() => navigate('/admin/users')}
                    size="icon"
                    className="h-10 w-10"
                >
                    <ArrowLeft className="h-5 w-5" />
                </Button>
                <h1 className="text-2xl font-semibold">User Details</h1>
            </div>

            {/* User Information Card */}
            <Card className="bg-white shadow-md">
                <CardHeader>
                    <CardTitle>User Information</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-start gap-6">
                        <Avatar className="h-24 w-24">
                            <AvatarImage
                                src={user.avatar || '/user-placeholder.svg'}
                                alt={`${user.firstName} ${user.lastName}`}
                            />
                            <AvatarFallback className="text-2xl">
                                {getInitials(user.firstName, user.lastName)}
                            </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 grid grid-cols-2 gap-6">
                            <div>
                                <p className="text-sm text-gray-500 mb-1">Full Name</p>
                                <p className="font-medium">{user.firstName} {user.lastName}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500 mb-1">Email</p>
                                <p className="font-medium">{user.email}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500 mb-1">Joined</p>
                                <p className="font-medium">{format(new Date(user.createdAt), 'PPP')}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500 mb-1">Last Login</p>
                                <p className="font-medium">{format(new Date(user.lastLogin), 'PPP')}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500 mb-1">Status</p>
                                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    }`}>
                                    {user.isActive ? 'Active' : 'Inactive'}
                                </span>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Transaction History Card */}
            <Card className="bg-white shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <CreditCard className="h-5 w-5" />
                        Transaction History
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {transactions.length === 0 ? (
                        <div className="text-center py-12 text-gray-500 bg-gray-50 rounded-lg">
                            <CreditCard className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                            <p className="font-medium">No transactions found</p>
                            <p className="text-sm mt-1">This user hasn't made any transactions yet</p>
                        </div>
                    ) : (
                        <div className="rounded-lg overflow-hidden border border-gray-100">
                            <Table>
                                <TableHeader className="bg-gray-50">
                                    <TableRow>
                                        <TableHead className="font-semibold text-gray-700">Date</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Domain</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Plan</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Subscription Type</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Amount</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Status</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Invoice</TableHead>
                                        <TableHead className="font-semibold text-gray-700">Details</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {paginatedTransactions.map((transaction) => (
                                        <React.Fragment key={transaction._id}>
                                            <TableRow className="hover:bg-gray-50">
                                                <TableCell className="font-medium text-gray-800">
                                                    <div className="flex items-center gap-2">
                                                        <Calendar className="h-4 w-4 text-gray-500" />
                                                        <span className="font-semibold text-gray-800">{formatDate(transaction.createdAt)}</span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        {transaction.domainId && typeof transaction.domainId !== 'string' ? (
                                                            <span className="px-2 py-0.5 bg-blue-50 text-blue-700 text-sm rounded-md border border-blue-100 inline-flex items-center">
                                                                <Globe className="h-3 w-3 mr-1 text-blue-500" />
                                                                {transaction.domainId.url}
                                                            </span>
                                                        ) : (
                                                            <span>Unknown Domain</span>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="font-medium text-gray-700">
                                                    <span className={`px-2 py-1 rounded text-xs font-semibold ${transaction.planType === 'Monthly' ? 'bg-blue-100 text-blue-700' :
                                                        transaction.planType === 'Yearly' ? 'bg-violet-100 text-violet-700' :
                                                            'bg-gray-100 text-gray-700'
                                                        }`}>
                                                        {transaction.planType}
                                                    </span>
                                                </TableCell>
                                                <TableCell className="font-medium text-gray-700">
                                                    <span className={`px-2 py-1 rounded text-xs font-semibold ${transaction.transactionType === 'New Subscription'
                                                        ? 'bg-green-50 text-green-700 border border-green-200'
                                                        : 'bg-blue-50 text-blue-700 border border-blue-200'
                                                        }`}>
                                                        {transaction.transactionType}
                                                    </span>
                                                </TableCell>
                                                <TableCell className="font-semibold text-gray-800">
                                                    <span className={`${transaction.amount >= 100 ? 'text-green-700' : 'text-green-600'} font-bold`}>
                                                        {transactionService.formatCurrency(transaction.amount, transaction.currency)}
                                                    </span>
                                                </TableCell>
                                                <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                                                <TableCell>
                                                    {transaction.status === 'completed' && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => handleFetchInvoice(transaction._id)}
                                                            disabled={loadingInvoiceId === transaction._id}
                                                            className="flex items-center gap-1 p-1 text-green-600 hover:text-green-800"
                                                        >
                                                            {loadingInvoiceId === transaction._id ? (
                                                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-green-600 border-t-transparent" />
                                                            ) : (
                                                                <>
                                                                    <Download className="h-4 w-4" />
                                                                    <span className="text-xs">Invoice</span>
                                                                </>
                                                            )}
                                                        </Button>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => toggleTransactionDetails(transaction._id)}
                                                        className="flex items-center gap-1 p-1"
                                                    >
                                                        {expandedTransaction === transaction._id ? (
                                                            <ChevronUp className="h-4 w-4" />
                                                        ) : (
                                                            <ChevronDown className="h-4 w-4" />
                                                        )}
                                                        <span className="text-xs">Details</span>
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                            {expandedTransaction === transaction._id && (
                                                <TableRow>
                                                    <TableCell colSpan={8} className="bg-gray-50 p-4">
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                                                            <div>
                                                                <h4 className="font-semibold mb-4 border-b pb-2 text-gray-800">Transaction Details</h4>
                                                                <div className="space-y-3">
                                                                    <div className="flex items-start">
                                                                        <p className="font-medium text-gray-700 w-32">Created:</p>
                                                                        <div className="flex items-center">
                                                                            <Calendar className="h-4 w-4 text-blue-500 mr-1" />
                                                                            <p className="text-gray-800">{formatDate(transaction.createdAt)}</p>
                                                                        </div>
                                                                    </div>

                                                                    <div className="flex items-start">
                                                                        <p className="font-medium text-gray-700 w-32">Updated:</p>
                                                                        <div className="flex items-center">
                                                                            <Clock className="h-4 w-4 text-blue-500 mr-1" />
                                                                            <p className="text-gray-800">{formatDate(transaction.updatedAt)}</p>
                                                                        </div>
                                                                    </div>

                                                                    <div className="flex items-start">
                                                                        <p className="font-medium text-gray-700 w-32">Payment Method:</p>
                                                                        <div className="flex items-center">
                                                                            <CreditCard className="h-4 w-4 text-gray-500 mr-1" />
                                                                            <p className="text-gray-800 capitalize">{transaction.paymentMethod || 'N/A'}</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div>
                                                                <h4 className="font-semibold mb-4 border-b pb-2 text-gray-800">Stripe Information</h4>
                                                                <div className="space-y-3">
                                                                    {transaction.stripeSessionId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Session ID:</p>
                                                                            <p className="text-gray-800 break-all bg-gray-50 px-2 py-1 rounded border border-gray-200 text-xs font-mono">{transaction.stripeSessionId}</p>
                                                                        </div>
                                                                    )}

                                                                    {transaction.stripeCustomerId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Customer ID:</p>
                                                                            <button
                                                                                onClick={() => handleOpenStripeCustomer(transaction.stripeCustomerId!)}
                                                                                className="text-blue-600 hover:underline flex items-center gap-1 bg-blue-50 px-2 py-1 rounded border border-blue-200 text-xs font-mono"
                                                                            >
                                                                                <span className="break-all">{transaction.stripeCustomerId}</span>
                                                                                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                            </button>
                                                                        </div>
                                                                    )}

                                                                    {transaction.stripeSubscriptionId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Subscription ID:</p>
                                                                            <button
                                                                                onClick={() => handleOpenStripeSubscription(transaction.stripeSubscriptionId!)}
                                                                                className="text-blue-600 hover:underline flex items-center gap-1 bg-blue-50 px-2 py-1 rounded border border-blue-200 text-xs font-mono"
                                                                            >
                                                                                <span className="break-all">{transaction.stripeSubscriptionId}</span>
                                                                                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                            </button>
                                                                        </div>
                                                                    )}

                                                                    {transaction.stripePaymentIntentId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Payment Intent:</p>
                                                                            <button
                                                                                onClick={() => handleOpenStripePaymentIntent(transaction.stripePaymentIntentId!)}
                                                                                className="text-blue-600 hover:underline flex items-center gap-1 bg-blue-50 px-2 py-1 rounded border border-blue-200 text-xs font-mono"
                                                                            >
                                                                                <span className="break-all">{transaction.stripePaymentIntentId}</span>
                                                                                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                            </button>
                                                                        </div>
                                                                    )}

                                                                    {transaction.stripeChargeId && (
                                                                        <div className="flex items-start">
                                                                            <p className="font-medium text-gray-700 w-32">Charge ID:</p>
                                                                            <button
                                                                                onClick={() => handleOpenStripeCharge(transaction.stripeChargeId!)}
                                                                                className="text-blue-600 hover:underline flex items-center gap-1 bg-blue-50 px-2 py-1 rounded border border-blue-200 text-xs font-mono"
                                                                            >
                                                                                <span className="break-all">{transaction.stripeChargeId}</span>
                                                                                <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                            </button>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>

                                                            {transaction.billingDetails && Object.keys(transaction.billingDetails).length > 0 && (
                                                                <div className="col-span-1 md:col-span-2">
                                                                    <h4 className="font-semibold mb-4 border-b pb-2 text-gray-800">Billing Details</h4>
                                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                        {transaction.billingDetails.name && (
                                                                            <div className="flex items-start">
                                                                                <p className="font-medium text-gray-700 w-32">Name:</p>
                                                                                <p className="text-gray-800">{transaction.billingDetails.name}</p>
                                                                            </div>
                                                                        )}

                                                                        {transaction.billingDetails.email && (
                                                                            <div className="flex items-start">
                                                                                <p className="font-medium text-gray-700 w-32">Email:</p>
                                                                                <p className="text-gray-800">{transaction.billingDetails.email}</p>
                                                                            </div>
                                                                        )}

                                                                        {transaction.billingDetails.address && (
                                                                            <>
                                                                                {transaction.billingDetails.address.line1 && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">Address:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.line1}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.line2 && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">Address 2:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.line2}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.city && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">City:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.city}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.state && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">State:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.state}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.postal_code && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">Postal Code:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.postal_code}</p>
                                                                                    </div>
                                                                                )}

                                                                                {transaction.billingDetails.address.country && (
                                                                                    <div className="flex items-start">
                                                                                        <p className="font-medium text-gray-700 w-32">Country:</p>
                                                                                        <p className="text-gray-800">{transaction.billingDetails.address.country}</p>
                                                                                    </div>
                                                                                )}
                                                                            </>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </React.Fragment>
                                    ))}
                                </TableBody>
                            </Table>
                            
                            {/* Pagination Controls */}
                            {transactions.length > pageSize && (
                                <div className="flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-100">
                                    <div className="text-sm text-gray-600">
                                        Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, transactions.length)} of {transactions.length} transactions
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={goToPreviousPage}
                                            disabled={currentPage === 1}
                                            className="flex items-center gap-1"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            <span>Previous</span>
                                        </Button>
                                        <div className="text-sm font-medium text-gray-700">
                                            Page {currentPage} of {totalPages}
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={goToNextPage}
                                            disabled={currentPage === totalPages}
                                            className="flex items-center gap-1"
                                        >
                                            <span>Next</span>
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Domains Card */}
            <Card className="bg-white shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Globe className="w-5 h-5" />
                        User Domains
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {domains.length === 0 ? (
                        <div className="text-center text-gray-500 py-4">
                            No domains found
                        </div>
                    ) : (
                        <div className="space-y-6">
                            {domains.map((domain) => (
                                <Accordion
                                    key={domain._id}
                                    type="single"
                                    collapsible
                                    className="bg-gray-50 rounded-lg"
                                >
                                    <AccordionItem value="domain-info" className="border-none">
                                        <AccordionTrigger className="px-6 py-4 hover:no-underline">
                                            <div className="flex items-center justify-between w-full">
                                                <div className="flex items-center gap-4">
                                                    <h3 className="text-lg font-semibold">{domain.name}</h3>
                                                    <Badge variant={domain.status === 'active' ? 'default' : 'secondary'}>
                                                        {domain.status}
                                                    </Badge>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <FileText className="w-4 h-4" />
                                                    <span className="text-sm text-gray-500">
                                                        {domain.articles?.length || 0} Articles
                                                    </span>
                                                </div>
                                            </div>
                                        </AccordionTrigger>
                                        <AccordionContent className="px-6 pb-6">
                                            <div className="space-y-6">
                                                {/* Domain Information */}
                                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                                    {/* Left Column */}
                                                    <div className="space-y-6">
                                                        <div>
                                                            <h4 className="font-medium mb-2 text-gray-700">Basic Information</h4>
                                                            <div className="space-y-2">
                                                                <p>
                                                                    <span className="text-gray-600">URL: </span>
                                                                    <a href={domain.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                                                        {domain.url}
                                                                    </a>
                                                                </p>
                                                                <p>
                                                                    <span className="text-gray-600">Title: </span>
                                                                    {domain.title || "Online Shopping site in India: Shop Online for Mobiles, Books, Watches..."}
                                                                </p>
                                                                <p>
                                                                    <span className="text-gray-600">Description: </span>
                                                                    {domain.description || "Amazon.in: Online Shopping India - Buy mobiles, laptops, cameras, book..."}
                                                                </p>
                                                            </div>
                                                        </div>

                                                        <div>
                                                            <h4 className="font-medium mb-2 text-gray-700">Brand Information</h4>
                                                            <div className="space-y-2">
                                                                <p>
                                                                    <span className="text-gray-600">Brand Name: </span>
                                                                    {domain.brandInfo?.name || "Online Shopping site in India: Shop Online for Mobiles, Books, Watches..."}
                                                                </p>
                                                                <p>
                                                                    <span className="text-gray-600">Target Audience: </span>
                                                                    {domain.brandInfo?.targetAudience || "All online shoppers in India"}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {/* Right Column */}
                                                    <div className="space-y-6">
                                                        <div>
                                                            <h4 className="font-medium mb-2 text-gray-700">Article Settings</h4>
                                                            <div className="space-y-2">
                                                                <p>
                                                                    <span className="text-gray-600">Article Length: </span>
                                                                    {domain.articleSettings?.articleLength || "Medium"}
                                                                </p>
                                                                <p>
                                                                    <span className="text-gray-600">Use Brand Info: </span>
                                                                    {domain.articleSettings?.userBrandInfo ? "Yes" : "No"}
                                                                </p>
                                                            </div>
                                                        </div>

                                                        <div>
                                                            <h4 className="font-medium mb-2 text-gray-700">Design Settings</h4>
                                                            <div className="space-y-2">
                                                                <p>
                                                                    <span className="text-gray-600">Theme: </span>
                                                                    {domain.designSettings?.articleTheme || "Default"}
                                                                </p>
                                                                <p>
                                                                    <span className="text-gray-600">Layout: </span>
                                                                    {domain.designSettings?.layout?.grid || "Standard"}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Articles Section */}
                                                <div className="mt-8">
                                                    <h4 className="font-medium mb-4 text-gray-700 flex items-center gap-2">
                                                        <FileText className="w-4 h-4" />
                                                        Articles
                                                    </h4>
                                                    {domain.articles && domain.articles.length > 0 ? (
                                                        <div className="bg-white rounded-lg border">
                                                            <Table>
                                                                <TableHeader>
                                                                    <TableRow>
                                                                        <TableHead>Title</TableHead>
                                                                        <TableHead>Status</TableHead>
                                                                        <TableHead>Created At</TableHead>
                                                                        <TableHead>Updated At</TableHead>
                                                                    </TableRow>
                                                                </TableHeader>
                                                                <TableBody>
                                                                    {domain.articles.map((article) => (
                                                                        <TableRow key={article._id}>
                                                                            <TableCell>
                                                                                {article.urlSlug ? (
                                                                                    <button
                                                                                        onClick={() => handleArticleClick(article)}
                                                                                        className="font-medium text-blue-600 hover:text-blue-800 hover:underline text-left"
                                                                                    >
                                                                                        {article.title}
                                                                                    </button>
                                                                                ) : (
                                                                                    <span className="font-medium">{article.title}</span>
                                                                                )}
                                                                            </TableCell>
                                                                            <TableCell>
                                                                                <Badge variant={article.status === 'published' ? 'default' : 'secondary'}>
                                                                                    {article.status}
                                                                                </Badge>
                                                                            </TableCell>
                                                                            <TableCell>{formatDate(article.createdAt)}</TableCell>
                                                                            <TableCell>{formatDate(article.updatedAt)}</TableCell>
                                                                        </TableRow>
                                                                    ))}
                                                                </TableBody>
                                                            </Table>
                                                        </div>
                                                    ) : (
                                                        <div className="text-center text-gray-500 py-4 bg-gray-50 rounded-lg">
                                                            No articles found for this domain
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </AccordionContent>
                                    </AccordionItem>
                                </Accordion>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default UserView; 