import React, { useEffect } from 'react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import TransactionHistory from '@/components/dashboard/TransactionHistory';

const TransactionsPage: React.FC = () => {
    useEffect(() => {
        // Set document title directly
        document.title = 'Transactions | BlogBuster';
    }, []);

    return (
        // <DashboardLayout pageTitle="">
        // <div className="container mx-auto py-8 px-4 md:px-6">
        <TransactionHistory />
        // {/* </div> */ }
        // {/* </DashboardLayout> */ }
    );
};

export default TransactionsPage; 