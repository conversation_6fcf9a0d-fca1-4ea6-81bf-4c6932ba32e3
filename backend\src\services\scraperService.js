const axios = require('axios');
const cheerio = require('cheerio');

class ScraperService {
    async scrapeWebsite(url) {
        try {
            // Add multiple user agents to rotate
            const userAgents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
            ];

            // Add common headers to mimic a real browser
            const headers = {
                'User-Agent': userAgents[Math.floor(Math.random() * userAgents.length)],
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0'
            };

            console.log('Attempting to scrape:', url);
            const response = await axios.get(url, {
                headers,
                timeout: 30000,
                maxRedirects: 5,
                validateStatus: function (status) {
                    return status >= 200 && status < 300; // Only accept success status codes
                }
            });

            if (!response.data) {
                throw new Error('No data received from the website');
            }

            const $ = cheerio.load(response.data);

            // Extract relevant data
            const websiteData = {
                title: this.extractTitle($),
                description: this.extractDescription($),
                mainContent: '',
                headings: [],
                paragraphs: [],
                links: [],
                images: [],
                keywords: this.extractKeywords($),
                metadata: this.extractMetadata($)
            };

            try {
                // Get all headings with their text and hierarchy
                $('h1, h2, h3, h4, h5, h6').each((i, elem) => {
                    const level = elem.name.substring(1);
                    const text = $(elem).text().trim();
                    if (text) {
                        websiteData.headings.push({
                            level,
                            text
                        });
                    }
                });

                // Get main content paragraphs from multiple potential content areas
                const contentSelectors = [
                    'article p',
                    'main p',
                    '#content p',
                    '.content p',
                    '.main p',
                    '.article p',
                    '.post p',
                    '.entry-content p',
                    'p'
                ];

                contentSelectors.forEach(selector => {
                    $(selector).each((i, elem) => {
                        const text = $(elem).text().trim();
                        if (text.length > 30) {
                            websiteData.paragraphs.push(text);
                        }
                    });
                });

                // If no paragraphs found, try getting any text content
                if (websiteData.paragraphs.length === 0) {
                    $('body').find('*').contents().each((i, elem) => {
                        if (elem.type === 'text') {
                            const text = $(elem).text().trim();
                            if (text.length > 30) {
                                websiteData.paragraphs.push(text);
                            }
                        }
                    });
                }

                // Get relevant links
                $('a').each((i, elem) => {
                    const href = $(elem).attr('href');
                    const text = $(elem).text().trim();
                    if (href && text && !href.startsWith('#') && !href.startsWith('javascript:')) {
                        try {
                            websiteData.links.push({
                                url: href.startsWith('http') ? href : new URL(href, url).href,
                                text
                            });
                        } catch (e) {
                            console.warn('Invalid URL:', href);
                        }
                    }
                });

                // Get images with alt text
                $('img').each((i, elem) => {
                    const src = $(elem).attr('src');
                    const alt = $(elem).attr('alt');
                    if (src) {
                        try {
                            websiteData.images.push({
                                url: src.startsWith('http') ? src : new URL(src, url).href,
                                alt: alt || ''
                            });
                        } catch (e) {
                            console.warn('Invalid image URL:', src);
                        }
                    }
                });
            } catch (error) {
                console.warn('Error during detailed scraping:', error);
                // Continue with partial data
            }

            // Combine main content from paragraphs
            websiteData.mainContent = this.extractMainContent(websiteData.paragraphs);

            // If we don't have enough content, try alternative methods
            if (!websiteData.mainContent) {
                websiteData.mainContent = $('body').text().trim();
            }

            // Clean up the data
            const cleanedData = this.cleanupData(websiteData);

            // Validate the data
            if (!this.validateScrapedData(cleanedData)) {
                throw new Error('Failed to extract sufficient content from the website');
            }

            return cleanedData;
        } catch (error) {
            console.error('Error scraping website:', error);
            throw new Error(`Failed to scrape website: ${error.message}`);
        }
    }

    validateScrapedData(data) {
        // Check if we have at least some basic content
        return (
            data.title ||
            data.description ||
            (data.mainContent && data.mainContent.length > 100) ||
            (data.paragraphs && data.paragraphs.length > 0)
        );
    }

    extractTitle($) {
        return (
            $('meta[property="og:title"]').attr('content') ||
            $('meta[name="twitter:title"]').attr('content') ||
            $('h1').first().text() ||
            $('title').text() ||
            ''
        ).trim();
    }

    extractDescription($) {
        return (
            $('meta[name="description"]').attr('content') ||
            $('meta[property="og:description"]').attr('content') ||
            $('meta[name="twitter:description"]').attr('content') ||
            ''
        ).trim();
    }

    extractKeywords($) {
        const keywords = new Set();

        // Try different ways to get keywords
        const keywordsMeta = $('meta[name="keywords"]').attr('content');
        if (keywordsMeta) {
            keywordsMeta.split(',').forEach(k => keywords.add(k.trim()));
        }

        // Extract from meta tags
        $('meta').each((i, elem) => {
            const name = $(elem).attr('name')?.toLowerCase();
            const content = $(elem).attr('content');
            if (name && content && name.includes('keyword')) {
                content.split(',').forEach(k => keywords.add(k.trim()));
            }
        });

        // If no keywords found, extract from headings
        if (keywords.size === 0) {
            $('h1, h2, h3').each((i, elem) => {
                const text = $(elem).text().trim();
                if (text) {
                    text.split(/\s+/).forEach(word => {
                        if (word.length > 3) keywords.add(word.toLowerCase());
                    });
                }
            });
        }

        return Array.from(keywords).filter(Boolean);
    }

    extractMetadata($) {
        const metadata = {};

        $('meta').each((i, elem) => {
            const name = $(elem).attr('name') || $(elem).attr('property');
            const content = $(elem).attr('content');
            if (name && content) {
                metadata[name] = content;
            }
        });

        return metadata;
    }

    extractMainContent(paragraphs) {
        // Join paragraphs and limit to a reasonable size
        const content = paragraphs.join('\n\n');
        return content.length > 5000 ? content.substring(0, 5000) + '...' : content;
    }

    cleanupData(data) {
        // Remove any null or undefined values and clean strings
        return Object.fromEntries(
            Object.entries(data).map(([key, value]) => [
                key,
                Array.isArray(value)
                    ? value.filter(Boolean).map(item =>
                        typeof item === 'string'
                            ? item.trim().replace(/\s+/g, ' ')
                            : item
                    )
                    : typeof value === 'string'
                        ? value.trim().replace(/\s+/g, ' ')
                        : value || ''
            ])
        );
    }
}

module.exports = new ScraperService(); 