import { useState, useEffect } from 'react';
import { Article } from '@/types/Article';
import apiClient from '@/lib/api-client';

interface UseArticleReturn {
    article: Article | null;
    isLoading: boolean;
    error: Error | null;
}

export const useArticle = (id: string | undefined): UseArticleReturn => {
    const [article, setArticle] = useState<Article | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
        const fetchArticle = async () => {
            if (!id) {
                setError(new Error('Article ID not provided'));
                setIsLoading(false);
                return;
            }

            try {
                const response = await apiClient.get(`/articles/${id}`);
                setArticle(response.data);
                setError(null);
            } catch (err) {
                console.error('Error fetching article:', err);
                setError(err instanceof Error ? err : new Error('Failed to load article'));
            } finally {
                setIsLoading(false);
            }
        };

        fetchArticle();
    }, [id]);

    return { article, isLoading, error };
}; 