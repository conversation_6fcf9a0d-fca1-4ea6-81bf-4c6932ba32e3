import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Article } from '@/types/Article';
import '@/styles/article.css';
import { useDomain } from '@/contexts/DomainContext';
import DailyDelightPreview from '../preview/DailyDelightPreview';
import ModernCapsulePreview from '../preview/ModernCapsulePreview';

interface ArticlePreviewProps {
    article: Article | null;
    onClose: () => void;
}

export const ArticlePreview = ({ article, onClose }: ArticlePreviewProps) => {
    if (!article) return null;

    const { currentDomain } = useDomain();
    const brandColor = currentDomain?.designSettings?.colors?.brand || '#3c484c';
    const fontColor = currentDomain?.designSettings?.colors?.accent || '#cec4c0';
    const fontFamily = currentDomain?.designSettings?.font || 'Inter';
    const theme = currentDomain?.designSettings?.articleTheme || 'Daily Delight';

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-7xl h-[90vh] p-6 bg-white overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-semibold">Article Preview</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>

                <div className="article-preview-content">
                    {theme === 'Modern Capsule' ? (
                        <ModernCapsulePreview
                            article={article}
                            fontFamily={fontFamily}
                            fontColor={fontColor}
                            brandColor={brandColor}
                        />
                    ) : (
                        <DailyDelightPreview
                            article={article}
                            fontFamily={fontFamily}
                            fontColor={fontColor}
                            brandColor={brandColor}
                        />
                    )}
                </div>
            </Card>
        </div>
    );
}; 