const axios = require('axios');
const cheerio = require('cheerio');

class DomainScraperService {
    async scrapeWebsite(url) {
        try {
            // Ensure URL has protocol
            if (!url.startsWith('http')) {
                url = 'https://' + url;
            }

            console.log('Enhanced scraping started for:', url);

            const response = await axios.get(url, {
                timeout: 15000, // Increased timeout
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'max-age=0'
                },
                maxRedirects: 5
            });

            const $ = cheerio.load(response.data);

            // Extract comprehensive data
            const basicInfo = this.extractBasicInfo($);
            const brandInfo = this.extractComprehensiveBrandInfo($, url);
            const socialInfo = this.extractSocialMediaInfo($);
            const contactInfo = this.extractContactInfo($);
            const businessInfo = this.extractBusinessInfo($);
            const contentAnalysis = this.analyzeContent($);

            // Merge all brand information
            const comprehensiveBrandInfo = {
                name: brandInfo.name || basicInfo.title || this.extractDomainName(url),
                description: brandInfo.description || basicInfo.description || contentAnalysis.summary || '',
                targetAudience: brandInfo.targetAudience || contentAnalysis.targetAudience || 'General audience',
                logo: brandInfo.logo || '',
                audienceLocation: brandInfo.audienceLocation || businessInfo.location || 'Global',
                benefits: [
                    ...brandInfo.benefits,
                    ...contentAnalysis.benefits,
                    ...businessInfo.services
                ].filter(Boolean).slice(0, 10), // Limit and filter
                toneOfVoice: brandInfo.toneOfVoice || contentAnalysis.toneOfVoice || 'Professional',
                industry: brandInfo.industry || businessInfo.industry || contentAnalysis.industry || 'General',
                tags: [
                    ...brandInfo.tags,
                    ...contentAnalysis.keywords,
                    ...businessInfo.keywords
                ].filter(Boolean).slice(0, 15), // Limit and filter
                // Additional comprehensive info
                socialMedia: socialInfo,
                contact: contactInfo,
                companySize: businessInfo.companySize,
                foundedYear: businessInfo.foundedYear,
                headquarters: businessInfo.headquarters
            };

            const result = {
                name: this.extractDomainName(url),
                url: url,
                title: basicInfo.title || this.extractDomainName(url),
                description: basicInfo.description || comprehensiveBrandInfo.description || '',
                brandInfo: comprehensiveBrandInfo,
                keywords: comprehensiveBrandInfo.tags,
                metadata: {
                    scrapedAt: new Date().toISOString(),
                    confidence: this.calculateConfidenceScore(comprehensiveBrandInfo),
                    dataPoints: Object.keys(comprehensiveBrandInfo).filter(key =>
                        comprehensiveBrandInfo[key] &&
                        (typeof comprehensiveBrandInfo[key] !== 'object' || comprehensiveBrandInfo[key].length > 0)
                    ).length
                }
            };

            console.log(`Enhanced scraping completed for ${url}. Confidence: ${result.metadata.confidence}%, Data points: ${result.metadata.dataPoints}`);
            return result;

        } catch (error) {
            console.error('Error in enhanced scraping:', error);
            return this.getFallbackData(url, error);
        }
    }

    extractBasicInfo($) {
        return {
            title: this.extractTitle($),
            description: this.extractDescription($)
        };
    }

    extractTitle($) {
        return (
            $('meta[property="og:title"]').attr('content') ||
            $('meta[name="twitter:title"]').attr('content') ||
            $('title').text() ||
            $('h1').first().text() ||
            ''
        ).trim();
    }

    extractDescription($) {
        return (
            $('meta[name="description"]').attr('content') ||
            $('meta[property="og:description"]').attr('content') ||
            $('meta[name="twitter:description"]').attr('content') ||
            this.extractDescriptionFromContent($) ||
            ''
        ).trim();
    }

    extractDescriptionFromContent($) {
        // Try different common locations for meaningful text
        const selectors = [
            '.hero-description',
            '.intro-text',
            '.about-text',
            '.description',
            'main p:first-of-type',
            '.content p:first-of-type',
            'article p:first-of-type',
            '.hero p',
            '.banner p',
            'p'
        ];

        for (const selector of selectors) {
            const text = $(selector).first().text().trim();
            if (text && text.length > 50 && text.length < 500) {
                return text;
            }
        }
        return '';
    }

    extractComprehensiveBrandInfo($, url) {
        return {
            name: this.extractBrandName($),
            description: this.extractBrandDescription($),
            targetAudience: this.extractTargetAudience($),
            logo: this.extractLogo($),
            audienceLocation: this.extractAudienceLocation($),
            benefits: this.extractBenefits($),
            toneOfVoice: this.determineToneOfVoice($),
            industry: this.determineIndustry($),
            tags: this.extractKeywords($)
        };
    }

    extractBrandName($) {
        // Try multiple approaches to find brand name
        return (
            $('meta[property="og:site_name"]').attr('content') ||
            $('.logo').text().trim() ||
            $('.brand').text().trim() ||
            $('.site-title').text().trim() ||
            $('.company-name').text().trim() ||
            $('header .name').text().trim() ||
            this.extractTitle($)
        ).trim();
    }

    extractBrandDescription($) {
        // Look for brand-specific descriptions
        return (
            $('.brand-description').text().trim() ||
            $('.company-description').text().trim() ||
            $('.about-us').first().text().trim() ||
            $('.mission').text().trim() ||
            $('.tagline').text().trim() ||
            this.extractDescription($)
        ).trim();
    }

    extractSocialMediaInfo($) {
        const socialMedia = {};
        const socialPlatforms = {
            facebook: ['facebook.com', 'fb.com'],
            twitter: ['twitter.com', 'x.com'],
            instagram: ['instagram.com'],
            linkedin: ['linkedin.com'],
            youtube: ['youtube.com'],
            tiktok: ['tiktok.com'],
            pinterest: ['pinterest.com']
        };

        $('a[href*="facebook"], a[href*="twitter"], a[href*="instagram"], a[href*="linkedin"], a[href*="youtube"], a[href*="tiktok"], a[href*="pinterest"]').each((i, elem) => {
            const href = $(elem).attr('href');
            if (href) {
                for (const [platform, domains] of Object.entries(socialPlatforms)) {
                    if (domains.some(domain => href.includes(domain))) {
                        socialMedia[platform] = href;
                        break;
                    }
                }
            }
        });

        return socialMedia;
    }

    extractContactInfo($) {
        const contact = {};

        // Extract email
        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
        const bodyText = $('body').text();
        const emails = bodyText.match(emailRegex);
        if (emails && emails.length > 0) {
            contact.email = emails.filter(email =>
                !email.includes('example.com') &&
                !email.includes('test.com') &&
                !email.includes('placeholder')
            )[0];
        }

        // Extract phone numbers
        const phoneRegex = /(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
        const phones = bodyText.match(phoneRegex);
        if (phones && phones.length > 0) {
            contact.phone = phones[0];
        }

        // Extract address
        const addressSelectors = ['.address', '.location', '.contact-address', '[class*="address"]'];
        for (const selector of addressSelectors) {
            const addressText = $(selector).text().trim();
            if (addressText && addressText.length > 10) {
                contact.address = addressText;
                break;
            }
        }

        return contact;
    }

    extractBusinessInfo($) {
        const business = {};

        // Extract company size indicators
        const text = $('body').text().toLowerCase();
        if (text.includes('enterprise') || text.includes('corporation') || text.includes('global')) {
            business.companySize = 'Large';
        } else if (text.includes('startup') || text.includes('small business')) {
            business.companySize = 'Small';
        } else if (text.includes('medium') || text.includes('growing')) {
            business.companySize = 'Medium';
        }

        // Extract founded year
        const yearRegex = /(founded|established|since)\s+(\d{4})/i;
        const yearMatch = text.match(yearRegex);
        if (yearMatch) {
            business.foundedYear = yearMatch[2];
        }

        // Extract headquarters/location
        const locationKeywords = ['headquarters', 'based in', 'located in', 'head office'];
        for (const keyword of locationKeywords) {
            const index = text.indexOf(keyword);
            if (index !== -1) {
                const locationText = text.substring(index, index + 100);
                const locationMatch = locationText.match(/in ([A-Za-z\s,]+)/);
                if (locationMatch) {
                    business.headquarters = locationMatch[1].trim();
                    break;
                }
            }
        }

        // Extract services/products
        const services = [];
        $('.services li, .products li, .offerings li').each((i, elem) => {
            const service = $(elem).text().trim();
            if (service && service.length < 100) {
                services.push(service);
            }
        });
        business.services = services.slice(0, 5);

        // Extract industry-specific keywords
        business.keywords = this.extractIndustryKeywords($);
        business.industry = this.determineIndustry($);
        business.location = business.headquarters || this.extractAudienceLocation($);

        return business;
    }

    analyzeContent($) {
        const analysis = {};
        const bodyText = $('body').text().toLowerCase();

        // Analyze target audience
        analysis.targetAudience = this.extractTargetAudience($);

        // Extract benefits from various sources
        analysis.benefits = this.extractComprehensiveBenefits($);

        // Analyze tone of voice
        analysis.toneOfVoice = this.determineToneOfVoice($);

        // Determine industry
        analysis.industry = this.determineIndustry($);

        // Extract keywords
        analysis.keywords = this.extractKeywords($);

        // Create content summary
        analysis.summary = this.createContentSummary($);

        return analysis;
    }

    extractComprehensiveBenefits($) {
        const benefits = new Set();

        // Look for benefit indicators in various formats
        const benefitSelectors = [
            '.benefits li',
            '.features li',
            '.advantages li',
            '.why-choose li',
            '.services li',
            '.offerings li',
            '.what-we-do li',
            'ul li',
            'ol li'
        ];

        benefitSelectors.forEach(selector => {
            $(selector).each((i, elem) => {
                const text = $(elem).text().trim();
                if (text.length > 10 && text.length < 200) {
                    benefits.add(text);
                }
            });
        });

        // Look for benefit keywords in headings
        $('h2, h3, h4').each((i, elem) => {
            const text = $(elem).text().trim();
            const lowerText = text.toLowerCase();
            if ((lowerText.includes('benefit') || lowerText.includes('advantage') ||
                lowerText.includes('feature') || lowerText.includes('why')) &&
                text.length < 100) {
                benefits.add(text);
            }
        });

        // Look for benefit patterns in paragraphs
        $('p').each((i, elem) => {
            const text = $(elem).text().trim();
            const lowerText = text.toLowerCase();
            if ((lowerText.includes('we help') || lowerText.includes('we provide') ||
                lowerText.includes('our solution') || lowerText.includes('get more') ||
                lowerText.includes('increase your') || lowerText.includes('improve your')) &&
                text.length > 20 && text.length < 300) {
                benefits.add(text);
            }
        });

        return Array.from(benefits).slice(0, 8);
    }

    extractKeywords($) {
        const keywords = new Set();

        // Extract from meta keywords
        const metaKeywords = $('meta[name="keywords"]').attr('content');
        if (metaKeywords) {
            metaKeywords.split(',').forEach(k => keywords.add(k.trim().toLowerCase()));
        }

        // Extract from headings
        $('h1, h2, h3').each((i, elem) => {
            const text = $(elem).text().trim().toLowerCase();
            const words = text.split(/\s+/).filter(word => word.length > 3 && word.length < 15);
            words.forEach(word => keywords.add(word));
        });

        // Extract from important classes and IDs
        $('.tag, .keyword, .category, .skill').each((i, elem) => {
            const text = $(elem).text().trim().toLowerCase();
            if (text.length > 2 && text.length < 20) {
                keywords.add(text);
            }
        });

        // Extract from alt tags
        $('img[alt]').each((i, elem) => {
            const alt = $(elem).attr('alt').toLowerCase();
            const words = alt.split(/\s+/).filter(word => word.length > 3);
            words.forEach(word => keywords.add(word));
        });

        return Array.from(keywords).slice(0, 15);
    }

    extractTargetAudience($) {
        const text = $('body').text().toLowerCase();
        const audienceIndicators = {
            'Small Business Owners': ['small business', 'entrepreneurs', 'startups', 'local business'],
            'Enterprise Clients': ['enterprise', 'corporation', 'large company', 'fortune 500'],
            'Developers': ['developers', 'programmers', 'engineers', 'technical team'],
            'Marketers': ['marketers', 'marketing team', 'agencies', 'brands'],
            'Consumers': ['customers', 'consumers', 'individuals', 'personal use'],
            'Healthcare Professionals': ['doctors', 'healthcare', 'medical', 'patients'],
            'Students': ['students', 'education', 'learning', 'academic'],
            'Freelancers': ['freelancers', 'consultants', 'independent', 'remote workers']
        };

        let maxScore = 0;
        let targetAudience = 'General audience';

        for (const [audience, keywords] of Object.entries(audienceIndicators)) {
            const score = keywords.reduce((acc, keyword) => {
                return acc + (text.match(new RegExp(keyword, 'g')) || []).length;
            }, 0);

            if (score > maxScore) {
                maxScore = score;
                targetAudience = audience;
            }
        }

        return targetAudience;
    }

    extractAudienceLocation($) {
        const text = $('body').text().toLowerCase();
        const locationKeywords = {
            'United States': ['usa', 'america', 'us-based', 'american'],
            'Europe': ['europe', 'european', 'eu', 'gdpr'],
            'Asia': ['asia', 'asian', 'china', 'japan', 'india'],
            'Global': ['worldwide', 'global', 'international', 'everywhere']
        };

        for (const [location, keywords] of Object.entries(locationKeywords)) {
            if (keywords.some(keyword => text.includes(keyword))) {
                return location;
            }
        }

        return 'Global';
    }

    extractLogo($) {
        // Try multiple approaches to find logo
        const logoSelectors = [
            'img[alt*="logo" i]',
            '.logo img',
            'header img:first-child',
            '.site-logo img',
            '.brand img',
            '[class*="logo"] img',
            'img[src*="logo"]'
        ];

        for (const selector of logoSelectors) {
            const logoSrc = $(selector).attr('src');
            if (logoSrc) {
                try {
                    return logoSrc.startsWith('http') ? logoSrc : new URL(logoSrc, url).href;
                } catch (e) {
                    // Continue to next selector
                }
            }
        }

        return '';
    }

    extractBenefits($) {
        return this.extractComprehensiveBenefits($);
    }

    determineToneOfVoice($) {
        const text = $('body').text().toLowerCase();
        const wordCount = text.split(/\s+/).length;

        const indicators = {
            'Technical': ['api', 'integration', 'platform', 'solution', 'technology', 'system', 'software', 'data'],
            'Professional': ['professional', 'enterprise', 'business', 'corporate', 'industry', 'service'],
            'Friendly': ['friendly', 'welcome', 'community', 'help', 'support', 'team', 'together'],
            'Casual': ['hey', 'awesome', 'cool', 'great', 'amazing', 'love', 'fun'],
            'Authoritative': ['expert', 'leader', 'proven', 'trusted', 'authority', 'excellence'],
            'Innovative': ['innovative', 'cutting-edge', 'revolutionary', 'breakthrough', 'advanced']
        };

        let maxScore = 0;
        let dominantTone = 'Professional';

        for (const [tone, keywords] of Object.entries(indicators)) {
            const score = keywords.reduce((acc, keyword) => {
                return acc + (text.match(new RegExp(keyword, 'g')) || []).length;
            }, 0) / wordCount;

            if (score > maxScore) {
                maxScore = score;
                dominantTone = tone;
            }
        }

        return dominantTone;
    }

    determineIndustry($) {
        const text = $('body').text().toLowerCase();

        const industries = {
            'Technology': ['software', 'tech', 'digital', 'app', 'platform', 'saas', 'cloud', 'ai', 'machine learning'],
            'E-commerce': ['shop', 'store', 'product', 'buy', 'purchase', 'cart', 'checkout', 'retail'],
            'Healthcare': ['health', 'medical', 'wellness', 'care', 'patient', 'doctor', 'clinic', 'hospital'],
            'Finance': ['finance', 'banking', 'investment', 'money', 'financial', 'insurance', 'loan'],
            'Education': ['education', 'learning', 'course', 'training', 'teach', 'student', 'school', 'university'],
            'Marketing': ['marketing', 'advertising', 'brand', 'promotion', 'media', 'campaign', 'seo'],
            'Real Estate': ['real estate', 'property', 'home', 'house', 'rent', 'buy', 'sell', 'mortgage'],
            'Food & Beverage': ['food', 'restaurant', 'cafe', 'beverage', 'recipe', 'cooking', 'dining'],
            'Travel': ['travel', 'vacation', 'hotel', 'flight', 'tourism', 'trip', 'destination'],
            'Fitness': ['fitness', 'gym', 'workout', 'exercise', 'health', 'training', 'sport'],
            'Consulting': ['consulting', 'consultant', 'advisory', 'strategy', 'expert', 'professional services']
        };

        let maxScore = 0;
        let detectedIndustry = 'General';

        for (const [industry, keywords] of Object.entries(industries)) {
            const score = keywords.reduce((acc, keyword) => {
                return acc + (text.match(new RegExp(keyword, 'g')) || []).length;
            }, 0);

            if (score > maxScore) {
                maxScore = score;
                detectedIndustry = industry;
            }
        }

        return detectedIndustry;
    }

    extractIndustryKeywords($) {
        const keywords = new Set();
        const text = $('body').text().toLowerCase();

        // Industry-specific terms
        const industryTerms = [
            // Tech
            'api', 'cloud', 'saas', 'software', 'platform', 'digital',
            // Business
            'business', 'enterprise', 'solution', 'service', 'professional',
            // Marketing
            'marketing', 'seo', 'social media', 'advertising', 'brand'
        ];

        industryTerms.forEach(term => {
            if (text.includes(term)) {
                keywords.add(term);
            }
        });

        return Array.from(keywords);
    }

    createContentSummary($) {
        // Get first few paragraphs to create a summary
        const paragraphs = [];
        $('p').each((i, elem) => {
            if (i < 3) { // First 3 paragraphs
                const text = $(elem).text().trim();
                if (text.length > 50) {
                    paragraphs.push(text);
                }
            }
        });

        const summary = paragraphs.join(' ').substring(0, 300);
        return summary || '';
    }

    calculateConfidenceScore(brandInfo) {
        // Calculate confidence based on available data
        let score = 0;
        const weights = {
            name: 20,
            description: 15,
            logo: 10,
            industry: 10,
            targetAudience: 10,
            benefits: 15,
            tags: 10,
            socialMedia: 5,
            contact: 5
        };

        Object.keys(weights).forEach(key => {
            if (brandInfo[key]) {
                if (Array.isArray(brandInfo[key]) && brandInfo[key].length > 0) {
                    score += weights[key];
                } else if (typeof brandInfo[key] === 'object' && Object.keys(brandInfo[key]).length > 0) {
                    score += weights[key];
                } else if (typeof brandInfo[key] === 'string' && brandInfo[key].trim()) {
                    score += weights[key];
                }
            }
        });

        return Math.min(100, score);
    }

    extractDomainName(url) {
        try {
            const hostname = new URL(url).hostname;
            return hostname.replace(/^www\./, '');
        } catch {
            return url.replace(/^www\./, '').split('/')[0];
        }
    }

    getFallbackData(url, error) {
        console.warn('Using fallback data for:', url, error.message);
        const domainName = this.extractDomainName(url);

        return {
            name: domainName,
            url: url,
            title: domainName,
            description: `Website for ${domainName}`,
            brandInfo: {
                name: domainName,
                description: `Professional website for ${domainName}`,
                targetAudience: 'General audience',
                logo: '',
                audienceLocation: 'Global',
                benefits: [`Quality services from ${domainName}`, 'Professional expertise', 'Reliable solutions'],
                toneOfVoice: 'Professional',
                industry: 'General',
                tags: [domainName.split('.')[0], 'professional', 'services'],
                socialMedia: {},
                contact: {},
                companySize: 'Unknown',
                foundedYear: null,
                headquarters: 'Unknown'
            },
            keywords: [domainName.split('.')[0], 'professional', 'services'],
            metadata: {
                scrapedAt: new Date().toISOString(),
                confidence: 30, // Low confidence for fallback
                dataPoints: 3,
                fallback: true,
                error: error.message
            }
        };
    }
}

module.exports = new DomainScraperService(); 