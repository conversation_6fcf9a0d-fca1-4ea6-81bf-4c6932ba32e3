const cron = require('node-cron');
const Domain = require('../models/Domain');
const Article = require('../models/Article');
const gptService = require('./gptService');
const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const ensureLogsDirectory = () => {
    try {
        const logsDir = path.join(__dirname, '..', '..', 'logs');
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
        return logsDir;
    } catch (error) {
        console.error('Error creating logs directory:', error);
        return null;
    }
};

// Logger function
const logAutoArticleEvent = (eventType, data, isError = false) => {
    try {
        const timestamp = new Date().toISOString();
        const logDir = ensureLogsDirectory();

        if (!logDir) return;

        // Format for console log
        const logPrefix = isError ? '❌ ERROR' : '✅ EVENT';
        console.log(`${logPrefix} [${timestamp}] AUTO-ARTICLE ${eventType}: ${JSON.stringify(data, null, 2)}`);

        // Write to log file
        const logFile = path.join(logDir, `auto-article-${new Date().toISOString().split('T')[0]}.log`);
        const logEntry = `[${timestamp}] ${isError ? 'ERROR' : 'EVENT'} AUTO-ARTICLE ${eventType}: ${JSON.stringify(data)}\n`;

        fs.appendFileSync(logFile, logEntry);
    } catch (err) {
        console.error('❌ Error writing to auto-article log:', err);
    }
};

class AutoArticleService {
    constructor() {
        // Schedule the job to run at 11:00 AM every day
        this.job = cron.schedule('0 11 * * *', () => {
            this.generateScheduledArticles();
        }, {
            scheduled: false
        });
    }

    start() {
        this.job.start();
        logAutoArticleEvent('service_started', { timestamp: new Date() });
        console.log('Auto article generation service started - scheduled for 11:00 AM daily');
    }

    stop() {
        this.job.stop();
        logAutoArticleEvent('service_stopped', { timestamp: new Date() });
        console.log('Auto article generation service stopped');
    }

    async generateScheduledArticles() {
        try {
            logAutoArticleEvent('daily_generation_started', { timestamp: new Date() });

            // Find all domains with active subscriptions and credits > 0
            const domainsWithCredits = await Domain.find({
                'subscription.active': true,
                'subscription.credits': { $gt: 0 }
            });

            logAutoArticleEvent('domains_found', { count: domainsWithCredits.length });

            if (domainsWithCredits.length === 0) {
                return;
            }

            // Process each domain
            for (const domain of domainsWithCredits) {
                try {
                    await this.generateArticleForDomain(domain);
                } catch (domainError) {
                    logAutoArticleEvent('domain_processing_error', {
                        domainId: domain._id,
                        error: domainError.message
                    }, true);
                }
            }

            logAutoArticleEvent('daily_generation_completed', {
                timestamp: new Date(),
                processedDomains: domainsWithCredits.length
            });
        } catch (error) {
            logAutoArticleEvent('daily_generation_error', { error: error.message }, true);
            console.error('Error in scheduled article generation:', error);
        }
    }

    async generateArticleForDomain(domain) {
        try {
            logAutoArticleEvent('domain_processing_started', {
                domainId: domain._id,
                name: domain.name,
                credits: domain.subscription.credits
            });

            // Validate required domain settings
            if (!domain.brandInfo || !domain.brandInfo.name) {
                logAutoArticleEvent('domain_missing_brand_info', { domainId: domain._id }, true);
                return;
            }

            if (!domain.articleSettings) {
                logAutoArticleEvent('domain_missing_article_settings', { domainId: domain._id }, true);
                return;
            }

            // Get existing articles for this domain
            const existingArticles = await Article.find({ domainId: domain._id }).select('title content');

            // Prepare brand info with defaults
            const brandInfo = {
                name: domain.brandInfo.name,
                description: domain.brandInfo.description || domain.description || '',
                targetAudience: domain.brandInfo.targetAudience || 'General audience',
                audienceLocation: domain.brandInfo.audienceLocation || 'Global',
                benefits: domain.brandInfo.benefits || [],
                toneOfVoice: domain.brandInfo.toneOfVoice || 'Professional',
                industry: domain.brandInfo.industry || 'General',
                tags: domain.brandInfo.tags || [],
                logo: domain.brandInfo.logo || ''
            };

            // Prepare article settings with defaults
            const articleSettings = {
                useBrandInfo: domain.articleSettings.useBrandInfo ?? true,
                articleLength: domain.articleSettings.articleLength || 'medium',
                language: domain.articleSettings.language || 'English',
                specificInstructions: domain.articleSettings.specificInstructions || '',
                exclusions: domain.articleSettings.exclusions || ''
            };

            // Generate a unique topic
            let uniqueTopic = '';
            try {
                const existingTitles = existingArticles.map(article => article.title).join(', ');
                const existingTopics = existingArticles.map(article => {
                    // Extract first paragraph or first 100 characters as topic summary
                    const firstParagraph = article.content.match(/<p.*?>(.*?)<\/p>/);
                    return firstParagraph ? firstParagraph[1].substring(0, 100) : article.content.substring(0, 100);
                }).join(', ');

                const uniqueTopicPrompt = `Generate a unique article topic/angle for a ${brandInfo.industry} website that is completely different from existing content.

Existing article titles: ${existingTitles}

Existing article topics: ${existingTopics}

Brand information:
- Name: ${brandInfo.name}
- Target audience: ${brandInfo.targetAudience}
- Industry: ${brandInfo.industry}
- Tone of voice: ${brandInfo.toneOfVoice}
- Keywords/Tags: ${brandInfo.tags.join(', ')}

Requirements:
1. Generate a unique topic that hasn't been covered in existing articles
2. Topic should be relevant to ${brandInfo.industry} industry
3. Topic should interest ${brandInfo.targetAudience}
4. Topic should align with ${brandInfo.name}'s brand identity
5. Return ONLY the topic as a brief phrase or sentence (max 10 words)

Return only the topic, no explanations or other text.`;

                uniqueTopic = await gptService.callOpenAI(
                    [
                        { role: "system", content: "You are an expert content strategist who creates unique article topics." },
                        { role: "user", content: uniqueTopicPrompt }
                    ],
                    0.9, // Higher temperature for more creativity
                    50
                );

                // Clean up the response
                uniqueTopic = uniqueTopic.trim().replace(/^["']|["']$/g, '');

                logAutoArticleEvent('topic_generated', {
                    domainId: domain._id,
                    topic: uniqueTopic
                });
            } catch (topicError) {
                logAutoArticleEvent('topic_generation_error', {
                    domainId: domain._id,
                    error: topicError.message
                }, true);
                // Continue with empty topic if generation fails
            }

            // Generate article using GPT
            const websiteData = {
                title: domain.title || domain.name,
                description: domain.description || '',
                url: domain.url,
                topic: uniqueTopic || '',
                mainContent: domain.description || '',
                keywords: brandInfo.tags,
                domain: {
                    id: domain._id.toString(),
                    name: domain.name,
                    brandInfo: brandInfo,
                    articleSettings: articleSettings
                }
            };

            const generatedArticle = await gptService.generateArticle(websiteData, existingArticles);

            // Create and save the article
            const article = new Article({
                ...generatedArticle,
                userId: domain.userId,
                domainId: domain._id,
                status: 'generated',
                date: new Date().toISOString(),
                metadata: {
                    ...generatedArticle.metadata,
                    usedBrandInfo: articleSettings.useBrandInfo,
                    brandName: brandInfo.name,
                    language: articleSettings.language,
                    industry: brandInfo.industry,
                    targetAudience: brandInfo.targetAudience,
                    toneOfVoice: brandInfo.toneOfVoice,
                    articleLength: articleSettings.articleLength,
                    generatedAt: new Date().toISOString(),
                    autoGenerated: true
                }
            });

            const savedArticle = await article.save();

            // Decrement domain credits
            await Domain.findByIdAndUpdate(domain._id, {
                $inc: { 'subscription.credits': -1 }
            });

            logAutoArticleEvent('article_generated', {
                domainId: domain._id,
                articleId: savedArticle._id,
                title: savedArticle.title,
                remainingCredits: domain.subscription.credits - 1
            });

            return savedArticle;
        } catch (error) {
            logAutoArticleEvent('article_generation_error', {
                domainId: domain._id,
                error: error.message
            }, true);
            throw error;
        }
    }

    // Method to manually trigger article generation for testing
    async generateArticleNow(domainId, options = {}) {
        try {
            const domain = await Domain.findById(domainId);

            if (!domain) {
                throw new Error('Domain not found');
            }

            if (!domain.subscription.active || domain.subscription.credits <= 0) {
                throw new Error('Domain has no active subscription or no credits');
            }

            const { topic, language, autoPublish = false, useSettings = true } = options;

            logAutoArticleEvent('manual_generation_started', {
                domainId: domain._id,
                name: domain.name,
                credits: domain.subscription.credits,
                options
            });

            // Validate required domain settings only if useSettings is true
            if (useSettings && (!domain.brandInfo || !domain.brandInfo.name)) {
                logAutoArticleEvent('domain_missing_brand_info', { domainId: domain._id }, true);
                throw new Error('Domain brand info is not set up');
            }

            if (useSettings && !domain.articleSettings) {
                logAutoArticleEvent('domain_missing_article_settings', { domainId: domain._id }, true);
                throw new Error('Domain article settings are not set up');
            }

            // Get existing articles for this domain
            const existingArticles = await Article.find({ domainId: domain._id }).select('title content');

            // Prepare brand info with defaults
            const brandInfo = useSettings ? {
                name: domain.brandInfo.name,
                description: domain.brandInfo.description || domain.description || '',
                targetAudience: domain.brandInfo.targetAudience || 'General audience',
                audienceLocation: domain.brandInfo.audienceLocation || 'Global',
                benefits: domain.brandInfo.benefits || [],
                toneOfVoice: domain.brandInfo.toneOfVoice || 'Professional',
                industry: domain.brandInfo.industry || 'General',
                tags: domain.brandInfo.tags || [],
                logo: domain.brandInfo.logo || ''
            } : {
                name: domain.name,
                description: domain.description || '',
                targetAudience: 'General audience',
                audienceLocation: 'Global',
                benefits: [],
                toneOfVoice: 'Professional',
                industry: 'General',
                tags: [],
                logo: ''
            };

            // Prepare article settings with defaults
            const articleSettings = useSettings ? {
                useBrandInfo: domain.articleSettings.useBrandInfo ?? true,
                articleLength: domain.articleSettings.articleLength || 'medium',
                language: language || domain.articleSettings.language || 'English',
                specificInstructions: domain.articleSettings.specificInstructions || '',
                exclusions: domain.articleSettings.exclusions || ''
            } : {
                useBrandInfo: true,
                articleLength: 'medium',
                language: language || 'English',
                specificInstructions: '',
                exclusions: ''
            };

            // Generate a unique topic if not provided
            let uniqueTopic = topic || '';
            if (!uniqueTopic) {
                try {
                    const existingTitles = existingArticles.map(article => article.title).join(', ');
                    const existingTopics = existingArticles.map(article => {
                        // Extract first paragraph or first 100 characters as topic summary
                        const firstParagraph = article.content.match(/<p.*?>(.*?)<\/p>/);
                        return firstParagraph ? firstParagraph[1].substring(0, 100) : article.content.substring(0, 100);
                    }).join(', ');

                    const uniqueTopicPrompt = `Generate a unique article topic/angle for a ${brandInfo.industry} website that is completely different from existing content.

Existing article titles: ${existingTitles}

Existing article topics: ${existingTopics}

Brand information:
- Name: ${brandInfo.name}
- Target audience: ${brandInfo.targetAudience}
- Industry: ${brandInfo.industry}
- Tone of voice: ${brandInfo.toneOfVoice}
- Keywords/Tags: ${brandInfo.tags.join(', ')}

Requirements:
1. Generate a unique topic that hasn't been covered in existing articles
2. Topic should be relevant to ${brandInfo.industry} industry
3. Topic should interest ${brandInfo.targetAudience}
4. Topic should align with ${brandInfo.name}'s brand identity
5. Return ONLY the topic as a brief phrase or sentence (max 10 words)

Return only the topic, no explanations or other text.`;

                    uniqueTopic = await gptService.callOpenAI(
                        [
                            { role: "system", content: "You are an expert content strategist who creates unique article topics." },
                            { role: "user", content: uniqueTopicPrompt }
                        ],
                        0.9, // Higher temperature for more creativity
                        50
                    );

                    // Clean up the response
                    uniqueTopic = uniqueTopic.trim().replace(/^["']|["']$/g, '');

                    logAutoArticleEvent('topic_generated', {
                        domainId: domain._id,
                        topic: uniqueTopic
                    });
                } catch (topicError) {
                    logAutoArticleEvent('topic_generation_error', {
                        domainId: domain._id,
                        error: topicError.message
                    }, true);
                    // Continue with empty topic if generation fails
                }
            } else {
                logAutoArticleEvent('using_provided_topic', {
                    domainId: domain._id,
                    topic: uniqueTopic
                });
            }

            // Generate article using GPT
            const websiteData = {
                title: domain.title || domain.name,
                description: domain.description || '',
                url: domain.url,
                topic: uniqueTopic || '',
                mainContent: domain.description || '',
                keywords: brandInfo.tags,
                domain: {
                    id: domain._id.toString(),
                    name: domain.name,
                    brandInfo: brandInfo,
                    articleSettings: articleSettings
                }
            };

            const generatedArticle = await gptService.generateArticle(websiteData, existingArticles);

            // Create and save the article
            const article = new Article({
                ...generatedArticle,
                userId: domain.userId,
                domainId: domain._id,
                status: autoPublish ? 'published' : 'generated',
                date: new Date().toISOString(),
                metadata: {
                    ...generatedArticle.metadata,
                    usedBrandInfo: articleSettings.useBrandInfo,
                    brandName: brandInfo.name,
                    language: articleSettings.language,
                    industry: brandInfo.industry,
                    targetAudience: brandInfo.targetAudience,
                    toneOfVoice: brandInfo.toneOfVoice,
                    articleLength: articleSettings.articleLength,
                    generatedAt: new Date().toISOString(),
                    autoGenerated: true
                }
            });

            const savedArticle = await article.save();

            // Decrement domain credits
            await Domain.findByIdAndUpdate(domain._id, {
                $inc: { 'subscription.credits': -1 }
            });

            logAutoArticleEvent('article_generated', {
                domainId: domain._id,
                articleId: savedArticle._id,
                title: savedArticle.title,
                remainingCredits: domain.subscription.credits - 1
            });

            return savedArticle;
        } catch (error) {
            logAutoArticleEvent('manual_generation_error', {
                domainId,
                error: error.message
            }, true);
            throw error;
        }
    }
}

module.exports = new AutoArticleService(); 