import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface UpgradeModalProps {
    onClose: () => void;
    onSelectPlan: () => void;
}

export const UpgradeModal = ({ onClose, onSelectPlan }: UpgradeModalProps) => {
    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-xl p-6 bg-white">
                <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center gap-2">
                        <div className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
                            <span>🔥 Limited Offer Annual</span>
                            <span>👍</span>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        ✕
                    </button>
                </div>

                <div className="mb-8">
                    <div className="flex items-baseline gap-2 mb-2">
                        <h2 className="text-4xl font-bold">$159.99</h2>
                        <span className="text-gray-600">for a year</span>
                    </div>
                    <p className="text-gray-500 text-sm">$13.25 per month paid annually</p>

                    <div className="mt-4 bg-orange-50 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                            <span className="text-orange-700">5 / 50 left</span>
                            <span className="text-gray-600">Next price: $299</span>
                        </div>
                    </div>
                </div>

                <div className="space-y-6">
                    <p className="text-gray-700">
                        All you need to create content that ranks and drives growth.
                    </p>

                    <div className="space-y-4">
                        <h3 className="font-medium">What you'll get:</h3>
                        <ul className="space-y-3">
                            {[
                                "30 high-quality & unique SEO articles per month.",
                                "Automated topic and keyword suggestions for fresh ideas",
                                "Keyword-focused content tailored to your niche and audience",
                                "Automated Internal linking to create a strong content network",
                                "Custom visuals illustrating the articles for improved readability.",
                                "Custom voice, tone, and audience targeting for tailored results",
                                "Adaptation to local SEO parameters for enhanced regional visibility",
                                "AI-generated, unique, and high-quality images to complement your content"
                            ].map((feature, index) => (
                                <li key={index} className="flex items-start gap-2">
                                    <span className="text-orange-500">✓</span>
                                    <span>{feature}</span>
                                </li>
                            ))}
                        </ul>
                    </div>

                    <p className="text-sm text-gray-600">
                        Includes all future & upcoming updates
                    </p>

                    <Button
                        onClick={onSelectPlan}
                        className="w-full bg-orange-500 hover:bg-orange-600 text-white"
                    >
                        Select plan
                    </Button>
                </div>
            </Card>
        </div>
    );
}; 