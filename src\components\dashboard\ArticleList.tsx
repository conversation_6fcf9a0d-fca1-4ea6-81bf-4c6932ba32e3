import { useState, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useDomain } from '@/contexts/DomainContext';
import { Article } from '@/types/Article';
import {
    Pencil,
    Trash2,
    Clock,
    User,
    Tag,
    ChevronLeft,
    ChevronRight
} from 'lucide-react';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { articleApi } from '@/services/api';
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";

interface ArticleListProps {
    articles: Article[];
    onEdit: (id: string) => void;
    onPreview: (id: string) => void;
    onDelete: (id: string) => void;
    emptyStateMessage?: string;
    emptyStateAction?: React.ReactNode;
}

export const ArticleList = ({ articles, onEdit, onPreview, onDelete, emptyStateMessage, emptyStateAction }: ArticleListProps) => {
    const [activeTab, setActiveTab] = useState('all');
    const [articleToDelete, setArticleToDelete] = useState<Article | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;
    const navigate = useNavigate();
    const { currentDomain } = useDomain();

    // Filter articles by current domain
    const domainArticles = useMemo(() => {
        if (!currentDomain) return [];
        return articles.filter(article => article.domainId === currentDomain._id);
    }, [articles, currentDomain]);

    // Filter by status
    const filteredArticles = useMemo(() => {
        if (activeTab === 'all') return domainArticles;
        return domainArticles.filter(article => article.status.toLowerCase() === activeTab.toLowerCase());
    }, [domainArticles, activeTab]);

    // Sort articles by date in descending order (newest first)
    const sortedArticles = useMemo(() => {
        return [...filteredArticles].sort((a, b) => {
            const dateA = new Date(a.date || a.createdAt || 0).getTime();
            const dateB = new Date(b.date || b.createdAt || 0).getTime();
            return dateB - dateA; // Descending order
        });
    }, [filteredArticles]);

    // Calculate pagination
    const totalPages = Math.max(1, Math.ceil(sortedArticles.length / itemsPerPage));

    // Reset to first page when tab changes or articles change
    useMemo(() => {
        setCurrentPage(1);
    }, [activeTab, articles.length]);

    // Get current page articles
    const currentArticles = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return sortedArticles.slice(startIndex, endIndex);
    }, [sortedArticles, currentPage, itemsPerPage]);

    const getStatusColor = (status: string) => {
        const colors: { [key: string]: string } = {
            published: 'bg-green-100 text-green-800 hover:bg-green-200 font-medium border border-green-200',
            draft: 'bg-gray-100 text-gray-800 hover:bg-gray-200 font-medium border border-gray-200',
            scheduled: 'bg-blue-100 text-blue-800 hover:bg-blue-200 font-medium border border-blue-200',
            generated: 'bg-purple-100 text-purple-800 hover:bg-purple-200 font-medium border border-purple-200'
        };
        return colors[status.toLowerCase()] || 'bg-gray-100 text-gray-800 hover:bg-gray-200 font-medium border border-gray-200';
    };

    const capitalizeFirstLetter = (string: string) => {
        return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
    };

    const formatDate = (dateString: string) => {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-GB', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (e) {
            return dateString;
        }
    };

    const handleArticleClick = async (article: Article) => {
        // Use existing slug or generate one
        const slug = article.urlSlug || article.title.toLowerCase()
            .trim()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '')
            .replace(/-+/g, '-');

        // If article doesn't have a slug, update it
        if (!article.urlSlug) {
            try {
                await articleApi.updateArticle(article.id, { ...article, urlSlug: slug });
            } catch (error) {
                console.error('Error updating article slug:', error);
            }
        }

        // Open in new tab with the slug
        window.open(`/articles/${encodeURIComponent(slug)}`, '_blank', 'noopener,noreferrer');
    };

    const handleDeleteClick = (article: Article) => {
        setArticleToDelete(article);
    };

    const handleDeleteConfirm = () => {
        if (articleToDelete) {
            onDelete(articleToDelete.id);
            setArticleToDelete(null);
        }
    };

    const handleDeleteCancel = () => {
        setArticleToDelete(null);
    };

    const getArticleImage = (article: Article): string | null => {
        // Try to get the thumbnail first
        if (article.image?.thumb) {
            return article.image.thumb;
        }
        // If no thumbnail, try the main image URL
        if (article.image?.url) {
            return article.image.url;
        }
        // If no image, try video thumbnail
        if (article.video?.thumbnail) {
            return article.video.thumbnail;
        }
        // If no video thumbnail, return null for fallback
        return null;
    };

    // Generate pagination items
    const renderPaginationItems = () => {
        const items = [];
        const maxVisiblePages = 5;

        // Previous button
        items.push(
            <PaginationItem key="prev">
                <PaginationPrevious
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                />
            </PaginationItem>
        );

        // First page
        if (totalPages > maxVisiblePages && currentPage > 3) {
            items.push(
                <PaginationItem key={1}>
                    <PaginationLink onClick={() => setCurrentPage(1)}>1</PaginationLink>
                </PaginationItem>
            );

            // Ellipsis after first page
            if (currentPage > 4) {
                items.push(
                    <PaginationItem key="ellipsis-start">
                        <PaginationEllipsis />
                    </PaginationItem>
                );
            }
        }

        // Page numbers
        const startPage = Math.max(1, Math.min(currentPage - Math.floor(maxVisiblePages / 2), totalPages - maxVisiblePages + 1));
        const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        for (let i = startPage; i <= endPage; i++) {
            items.push(
                <PaginationItem key={i}>
                    <PaginationLink
                        isActive={currentPage === i}
                        onClick={() => setCurrentPage(i)}
                    >
                        {i}
                    </PaginationLink>
                </PaginationItem>
            );
        }

        // Ellipsis before last page
        if (totalPages > maxVisiblePages && currentPage < totalPages - 2) {
            if (currentPage < totalPages - 3) {
                items.push(
                    <PaginationItem key="ellipsis-end">
                        <PaginationEllipsis />
                    </PaginationItem>
                );
            }

            // Last page
            items.push(
                <PaginationItem key={totalPages}>
                    <PaginationLink onClick={() => setCurrentPage(totalPages)}>
                        {totalPages}
                    </PaginationLink>
                </PaginationItem>
            );
        }

        // Next button
        items.push(
            <PaginationItem key="next">
                <PaginationNext
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                />
            </PaginationItem>
        );

        return items;
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h3 className="text-xl font-semibold">Articles</h3>
            </div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={!!articleToDelete} onOpenChange={(open) => !open && setArticleToDelete(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure you want to delete this article?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the article
                            "{articleToDelete?.title}".
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel onClick={handleDeleteCancel}>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteConfirm}
                            className="bg-red-600 hover:bg-red-700 text-white"
                        >
                            Delete Article
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {!currentDomain ? (
                <div className="text-center py-8">
                    <h3 className="text-lg font-medium text-gray-900">No Domain Selected</h3>
                    <p className="mt-1 text-sm text-gray-500">Please select a domain to view its articles</p>
                </div>
            ) : filteredArticles.length === 0 ? (
                <div className="text-center py-8">
                    <h3 className="text-lg font-medium text-gray-900">{emptyStateMessage}</h3>
                    {emptyStateAction}
                </div>
            ) : (
                <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
                    <TabsContent value={activeTab} className="space-y-4">
                        {currentArticles.map((article) => (
                            <Card key={article.id} className="p-6">
                                <div className="flex items-start justify-between gap-4">
                                    <div className="flex-shrink-0 w-32">
                                        {(() => {
                                            const imageUrl = getArticleImage(article);
                                            return imageUrl ? (
                                                <img
                                                    src={imageUrl}
                                                    alt={article.title}
                                                    className="w-32 h-32 object-cover rounded-lg border border-gray-200"
                                                    onError={(e) => {
                                                        const target = e.target as HTMLImageElement;
                                                        if (target.src === article.image?.thumb && article.image?.url) {
                                                            target.src = article.image.url;
                                                        } else {
                                                            target.src = '/article-placeholder.svg';
                                                        }
                                                    }}
                                                />
                                            ) : (
                                                <div className="w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center border border-gray-200">
                                                    <img
                                                        src="/article-placeholder.svg"
                                                        alt="Placeholder"
                                                        className="w-16 h-16 opacity-50"
                                                    />
                                                </div>
                                            );
                                        })()}
                                    </div>
                                    <div className="space-y-3 flex-1">
                                        <h3
                                            onClick={() => handleArticleClick(article)}
                                            className="text-lg font-semibold hover:text-blue-600 cursor-pointer"
                                        >
                                            {article.title}
                                        </h3>

                                        <p className="text-gray-600 line-clamp-2">
                                            {article.description}
                                        </p>

                                        <div className="flex items-center gap-4 text-sm text-gray-500">
                                            <div className="flex items-center gap-1">
                                                <User size={16} />
                                                <span>
                                                    {typeof article.author === 'object' && article.author?.name
                                                        ? article.author.name
                                                        : (typeof article.author === 'string' ? article.author : 'Anonymous')}
                                                </span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Clock size={16} />
                                                <span>{formatDate(article.date)}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Tag size={16} />
                                                <span>{article.tag || 'General'}</span>
                                            </div>
                                            <Badge className={`${getStatusColor(article.status)} text-sm px-2.5 py-0.5`}>
                                                {capitalizeFirstLetter(article.status)}
                                            </Badge>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => navigate(`/dashboard/articles/edit/${article.id}`)}
                                            className="flex items-center gap-1"
                                        >
                                            <Pencil className="w-4 h-4" />
                                            Edit
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleDeleteClick(article)}
                                            className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                            Delete
                                        </Button>
                                    </div>
                                </div>
                            </Card>
                        ))}

                        {/* Pagination */}
                        {sortedArticles.length > itemsPerPage && (
                            <div className="mt-6">
                                <Pagination>
                                    <PaginationContent>
                                        {renderPaginationItems()}
                                    </PaginationContent>
                                </Pagination>
                                <div className="text-center text-sm text-gray-500 mt-2">
                                    Showing {Math.min(sortedArticles.length, (currentPage - 1) * itemsPerPage + 1)} to {Math.min(sortedArticles.length, currentPage * itemsPerPage)} of {sortedArticles.length} articles
                                </div>
                            </div>
                        )}
                    </TabsContent>
                </Tabs>
            )}
        </div>
    );
}; 