import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';

const GoogleCallback = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { setAuthToken, updateNewUserStatus } = useAuth();
    const token = searchParams.get('token');
    const error = searchParams.get('error');
    const isNewUser = searchParams.get('new_user') === 'true';
    const [isProcessing, setIsProcessing] = useState(true);

    // Function to handle successful token storage and redirect
    const handleTokenSuccess = () => {
        // Show success message and redirect
        if (isNewUser) {
            toast.success('Account created successfully with Google!');
            navigate('/onboarding');
        } else {
            toast.success('Welcome back! Successfully signed in with Google.');
            navigate('/dashboard/articles');
        }
        setIsProcessing(false);
    };

    useEffect(() => {
        // If there's an error in the URL, handle it
        if (error) {
            toast.error('Failed to authenticate with Google');
            navigate('/login');
            return;
        }

        // If we have a token from the URL
        if (token) {
            // Set the auth token - this will trigger AuthContext to fetch user data
            setAuthToken(token);
            
            // If this is a new user, set the isNewUser flag in AuthContext
            if (isNewUser) {
                updateNewUserStatus(true);
            }
            
            // Add a small delay to ensure the auth context processes the token
            setTimeout(handleTokenSuccess, 100);
        } else {
            // If there's no token, redirect to login
            toast.error('Authentication failed. Please try again.');
            navigate('/login');
            setIsProcessing(false);
        }
    }, [token, error, navigate, isNewUser]);

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-[#fff7ed] p-4">
            <div className="text-center">
                <Loader2 className="h-12 w-12 text-orange-500 animate-spin mx-auto mb-4" />
                <h1 className="text-2xl font-semibold mb-2">Processing your sign in...</h1>
                <p className="text-gray-600">Please wait while we complete the authentication process.</p>
            </div>
        </div>
    );
};

export default GoogleCallback; 