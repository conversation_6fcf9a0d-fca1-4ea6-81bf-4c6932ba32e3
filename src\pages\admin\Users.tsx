import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAdmin } from '../../contexts/AdminContext';
import { Switch } from '../../components/ui/switch';
import { Avatar, AvatarImage, AvatarFallback } from '../../components/ui/avatar';
import { format } from 'date-fns';
import { Badge } from '../../components/ui/badge';
import { Layout, FileText, MoreVertical, Edit, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "../../components/ui/dialog";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import { useToast } from "../../components/ui/use-toast";

interface User {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar: string;
    createdAt: string;
    lastLogin: string;
    isActive: boolean;
    domainCount: number;
    articleCount: number;
    plan: string;
}

interface EditUserFormData {
    firstName: string;
    lastName: string;
    email: string;
    isActive: boolean;
}

const Users = () => {
    const [users, setUsers] = useState<User[]>([]);
    const [loading, setLoading] = useState(true);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<User | null>(null);
    const [editFormData, setEditFormData] = useState<EditUserFormData>({
        firstName: '',
        lastName: '',
        email: '',
        isActive: true
    });
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize] = useState<number>(10);
    const navigate = useNavigate();
    const { token } = useAdmin();
    const { toast } = useToast();

    // Pagination calculation
    const totalPages = Math.ceil(users.length / pageSize);
    const paginatedUsers = users.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
    );

    const goToNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const goToPreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    useEffect(() => {
        const fetchUsers = async () => {
            try {
                const response = await axios.get('http://localhost:5000/api/admin/users', {
                    headers: { Authorization: `Bearer ${token}` }
                });
                setUsers(response.data);
                setLoading(false);
            } catch (error) {
                console.error('Error fetching users:', error);
                setLoading(false);
            }
        };

        fetchUsers();
    }, [token]);

    const handleEditClick = (user: User) => {
        setSelectedUser(user);
        setEditFormData({
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            isActive: user.isActive
        });
        setIsEditDialogOpen(true);
    };

    const handleDeleteClick = (user: User) => {
        setSelectedUser(user);
        setIsDeleteDialogOpen(true);
    };

    const handleEditSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!selectedUser) return;

        try {
            const response = await axios.put(
                `http://localhost:5000/api/admin/users/${selectedUser._id}`,
                editFormData,
                { headers: { Authorization: `Bearer ${token}` } }
            );

            setUsers(users.map(user =>
                user._id === selectedUser._id ? { ...user, ...response.data } : user
            ));

            setIsEditDialogOpen(false);
            toast({
                title: "Success",
                description: "User updated successfully",
            });
        } catch (error) {
            console.error('Error updating user:', error);
            toast({
                title: "Error",
                description: "Failed to update user",
                variant: "destructive",
            });
        }
    };

    const handleDelete = async () => {
        if (!selectedUser) return;

        try {
            await axios.delete(
                `http://localhost:5000/api/admin/users/${selectedUser._id}`,
                { headers: { Authorization: `Bearer ${token}` } }
            );

            setUsers(users.filter(user => user._id !== selectedUser._id));
            setIsDeleteDialogOpen(false);
            toast({
                title: "Success",
                description: "User deleted successfully",
            });
        } catch (error) {
            console.error('Error deleting user:', error);
            toast({
                title: "Error",
                description: "Failed to delete user",
                variant: "destructive",
            });
        }
    };

    const getInitials = (firstName: string, lastName: string) => {
        return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    };

    const getPlanBadgeColor = (plan: string) => {
        switch (plan.toLowerCase()) {
            case 'enterprise':
                return 'bg-purple-100 text-purple-800';
            case 'pro':
                return 'bg-blue-100 text-blue-800';
            case 'basic':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
        );
    }

    return (
        <div className="p-6">
            <h1 className="text-2xl font-semibold mb-6">User Management</h1>
            <div className="bg-white rounded-lg shadow">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead>
                            <tr className="border-b">
                                <th className="text-left p-4">User</th>
                                <th className="text-left p-4">Plan</th>
                                <th className="text-left p-4">Domains</th>
                                <th className="text-left p-4">Articles</th>
                                <th className="text-left p-4">Created At</th>
                                <th className="text-left p-4">Last Login</th>
                                <th className="text-left p-4">Status</th>
                                <th className="text-left p-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {paginatedUsers.map(user => (
                                <tr key={user._id} className="border-b hover:bg-gray-50">
                                    <td className="p-4">
                                        <div className="flex items-start gap-3">
                                            <Avatar className="h-10 w-10">
                                                <AvatarImage
                                                    src={user.avatar || '/user-placeholder.svg'}
                                                    alt={`${user.firstName} ${user.lastName}`}
                                                />
                                                <AvatarFallback>
                                                    {getInitials(user.firstName, user.lastName)}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <span
                                                    className="text-blue-600 hover:underline cursor-pointer block font-medium"
                                                    onClick={() => navigate(`/admin/users/${user._id}`)}
                                                >
                                                    {user.firstName} {user.lastName}
                                                </span>
                                                <span className="text-sm text-gray-500">{user.email}</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="p-4">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPlanBadgeColor(user.plan)}`}>
                                            {user.plan}
                                        </span>
                                    </td>
                                    <td className="p-4">
                                        <div className="flex items-center gap-1.5">
                                            <Layout className="w-4 h-4 text-gray-500" />
                                            <span className="text-sm">{user.domainCount}</span>
                                        </div>
                                    </td>
                                    <td className="p-4">
                                        <div className="flex items-center gap-1.5">
                                            <FileText className="w-4 h-4 text-gray-500" />
                                            <span className="text-sm">{user.articleCount}</span>
                                        </div>
                                    </td>
                                    <td className="p-4 text-sm text-gray-600">{format(new Date(user.createdAt), 'M/d/yyyy')}</td>
                                    <td className="p-4 text-sm text-gray-600">{format(new Date(user.lastLogin), 'M/d/yyyy')}</td>
                                    <td className="p-4">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            }`}>
                                            {user.isActive ? 'Active' : 'Inactive'}
                                        </span>
                                    </td>
                                    <td className="p-4">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="icon">
                                                    <MoreVertical className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onClick={() => handleEditClick(user)}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuItem
                                                    onClick={() => handleDeleteClick(user)}
                                                    className="text-red-600"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>

                    {/* Pagination Controls */}
                    {users.length > pageSize && (
                        <div className="flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-100">
                            <div className="text-sm text-gray-600">
                                Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, users.length)} of {users.length} users
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={goToPreviousPage}
                                    disabled={currentPage === 1}
                                    className="flex items-center gap-1"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    <span>Previous</span>
                                </Button>
                                <div className="text-sm font-medium text-gray-700">
                                    Page {currentPage} of {totalPages}
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={goToNextPage}
                                    disabled={currentPage === totalPages}
                                    className="flex items-center gap-1"
                                >
                                    <span>Next</span>
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Edit User Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                        <DialogDescription>
                            Make changes to the user's information here.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleEditSubmit}>
                        <div className="space-y-4 py-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="firstName">First Name</Label>
                                    <Input
                                        id="firstName"
                                        value={editFormData.firstName}
                                        onChange={(e) => setEditFormData({
                                            ...editFormData,
                                            firstName: e.target.value
                                        })}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="lastName">Last Name</Label>
                                    <Input
                                        id="lastName"
                                        value={editFormData.lastName}
                                        onChange={(e) => setEditFormData({
                                            ...editFormData,
                                            lastName: e.target.value
                                        })}
                                    />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={editFormData.email}
                                    onChange={(e) => setEditFormData({
                                        ...editFormData,
                                        email: e.target.value
                                    })}
                                />
                            </div>
                            <div className="border rounded-lg p-4 space-y-3">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="status" className="text-base">Account Status</Label>
                                        <p className="text-sm text-gray-500 mt-1">
                                            Toggle to enable or disable user access
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <span className={`text-sm font-medium ${editFormData.isActive
                                            ? 'text-green-600'
                                            : 'text-red-600'
                                            }`}>
                                            {editFormData.isActive ? 'Active' : 'Inactive'}
                                        </span>
                                        <Switch
                                            id="status"
                                            checked={editFormData.isActive}
                                            onCheckedChange={(checked) => setEditFormData({
                                                ...editFormData,
                                                isActive: checked
                                            })}
                                        />
                                    </div>
                                </div>
                                <div className="text-xs text-gray-500">
                                    {editFormData.isActive
                                        ? "User has full access to all features and can log in to their account."
                                        : "User cannot log in or access any features while account is inactive."
                                    }
                                </div>
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit">Save changes</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Confirm Deletion</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete this user? This action cannot be undone.
                            All associated data including domains and articles will be permanently deleted.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDelete}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default Users; 