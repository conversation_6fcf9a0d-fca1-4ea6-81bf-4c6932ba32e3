import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useArticle } from '@/hooks/useArticle';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

const ArticlePage = () => {
    const { id } = useParams<{ id: string }>();
    const { article, isLoading, error } = useArticle(id);

    if (isLoading) {
        return <ArticleSkeleton />;
    }

    if (error || !article) {
        return (
            <div className="p-8">
                <h1 className="text-2xl font-bold text-red-500">Error loading article</h1>
                <p className="mt-4">{error?.message || 'Article not found'}</p>
                <Link to="/dashboard/articles">
                    <Button className="mt-4">
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Articles
                    </Button>
                </Link>
            </div>
        );
    }

    // Remove any HTML comments that might be visible
    const cleanContent = article.content
        .replace(/<!--[\s\S]*?-->/g, '') // Remove HTML comments
        .replace(/```html/g, '') // Remove code block markers
        .replace(/```/g, ''); // Remove remaining code block markers

    return (
        <div className="min-h-screen bg-background">
            <div className="container max-w-4xl mx-auto px-4 py-8">
                <div className="mb-6">
                    <Link to="/dashboard/articles">
                        <Button variant="ghost" className="mb-4">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Dashboard
                        </Button>
                    </Link>
                </div>

                <article className="prose prose-lg max-w-none">
                    <div className="flex items-center gap-4 mb-6">
                        <Badge variant="secondary">{article.tag}</Badge>
                        <Badge variant="outline">{article.status.toLowerCase()}</Badge>
                    </div>

                    {article.image && article.image.url && (
                        <div className="mb-8">
                            <div className="relative rounded-lg overflow-hidden">
                                <img
                                    src={article.image.url}
                                    alt={article.image.description || article.title}
                                    className="w-full h-auto object-cover"
                                />
                                {article.image.credit && (
                                    <div className="absolute bottom-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 rounded-tl">
                                        Photo by{' '}
                                        <a
                                            href={article.image.credit.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="underline hover:text-orange-300"
                                        >
                                            {article.image.credit.name}
                                        </a>
                                        {' '}on{' '}
                                        <a
                                            href="https://unsplash.com"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="underline hover:text-orange-300"
                                        >
                                            Unsplash
                                        </a>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    <h1 className="text-4xl font-bold mb-6">{article.title}</h1>

                    <div className="article-content prose prose-lg prose-headings:font-bold prose-h2:text-2xl prose-h3:text-xl prose-p:text-base prose-p:leading-relaxed prose-a:text-primary hover:prose-a:text-primary/80 prose-strong:font-bold prose-ul:list-disc prose-ol:list-decimal">
                        <div dangerouslySetInnerHTML={{ __html: cleanContent }} />
                    </div>

                    <div className="mt-8 pt-4 border-t">
                        <div className="flex flex-wrap gap-2">
                            {article.keywords?.map((keyword, index) => (
                                <Badge key={index} variant="secondary">
                                    {keyword}
                                </Badge>
                            ))}
                        </div>
                        <div className="mt-4 text-sm text-muted-foreground">
                            <p>Written by {article.author} • {article.date}</p>
                        </div>
                    </div>
                </article>
            </div>
        </div>
    );
};

const ArticleSkeleton = () => (
    <div className="container max-w-4xl mx-auto px-4 py-8">
        <Skeleton className="h-8 w-32 mb-8" />
        <Skeleton className="h-12 w-3/4 mb-4" />
        <Skeleton className="h-64 w-full mb-8" />
        <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/6" />
        </div>
    </div>
);

export default ArticlePage;
