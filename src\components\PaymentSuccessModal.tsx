import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';

interface PaymentSuccessModalProps {
    isOpen: boolean;
    onClose: () => void;
    planType: string;
}

const PaymentSuccessModal: React.FC<PaymentSuccessModalProps> = ({ isOpen, onClose, planType }) => {
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <div className="flex justify-center mb-4">
                        <CheckCircle className="h-12 w-12 text-green-500" />
                    </div>
                    <DialogTitle className="text-center text-xl">Payment Successful!</DialogTitle>
                    <DialogDescription className="text-center">
                        Your {planType} subscription has been activated successfully.
                    </DialogDescription>
                </DialogHeader>

                <div className="bg-green-50 p-4 rounded-md border border-green-200 text-green-800 my-4">
                    <p className="font-medium">Your account has been upgraded!</p>
                    <p className="text-sm mt-2">
                        You now have access to all the premium features of your {planType} plan.
                    </p>
                    {/* <ul className="text-sm mt-3 space-y-1 pl-5 list-disc">
                        {planType === 'Monthly' ? (
                            <>
                                <li>3 domains</li>
                                <li>30 articles per month</li>
                                <li>Advanced SEO tools</li>
                                <li>Priority support</li>
                            </>
                        ) : (
                            <>
                                <li>5 domains</li>
                                <li>50 articles per month</li>
                                <li>Premium SEO tools</li>
                                <li>24/7 priority support</li>
                                <li>AI-generated images</li>
                            </>
                        )}
                    </ul> */}
                </div>

                <div className="flex justify-center mt-4">
                    <Button
                        onClick={onClose}
                        className="bg-orange-500 hover:bg-orange-600 text-white"
                    >
                        Continue to Dashboard
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default PaymentSuccessModal; 