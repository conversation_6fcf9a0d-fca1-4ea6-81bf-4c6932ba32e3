import { Button } from '@/components/ui/button';
import { Lightbulb, Pencil, User as UserIcon } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';

interface HeaderProps {
  title?: string;
}

export const Header: React.FC<HeaderProps> = ({ title = "Dashboard" }) => {
  const navigate = useNavigate();

  // If title is empty, don't render the header
  if (title === "") {
    return null;
  }

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
        </div>

        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            className="border-orange-500 text-orange-600 hover:bg-orange-50 flex items-center gap-2"
          >
            <Lightbulb className="w-4 h-4" />
            Get more topic
          </Button>

          <Button className="bg-orange-500 hover:bg-orange-600 text-white">
            <Link to="/dashboard/write" className="flex items-center gap-2">
              Write Article <Pencil className="w-4 h-4" />
            </Link>
          </Button>

          <button
            onClick={() => navigate('/dashboard/account')}
            className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
          >
            <UserIcon className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>
    </header>
  );
};
