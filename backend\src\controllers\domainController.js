const Domain = require('../models/Domain');
const Article = require('../models/Article');
const domainScraperService = require('../services/domainScraperService');

// Get all domains for a user
exports.getDomains = async (req, res) => {
    try {
        const domains = await Domain.find({ userId: req.user.id }).sort({ createdAt: -1 });
        res.json(domains);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch domains', details: error.message });
    }
};

// Get single domain
exports.getDomain = async (req, res) => {
    try {
        const domain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }
        res.json(domain);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch domain', details: error.message });
    }
};

// Create new domain
exports.createDomain = async (req, res) => {
    try {
        const { url } = req.body;

        // Check if domain exists for this user
        const existingDomain = await Domain.findOne({ url, userId: req.user.id });

        if (existingDomain) {
            return res.status(400).json({
                error: 'Domain already exists',
                message: 'You have already registered this domain. Please use a different domain URL.'
            });
        }

        console.log('Starting enhanced domain scraping for:', url);

        // Scrape domain information using enhanced scraper
        const scrapedInfo = await domainScraperService.scrapeWebsite(url);

        console.log(`Scraping completed with ${scrapedInfo.metadata.confidence}% confidence and ${scrapedInfo.metadata.dataPoints} data points`);

        // Extract and map comprehensive scraped data to existing brand info fields
        const brandInfo = {
            // Use the best available name from multiple sources
            name: scrapedInfo.brandInfo?.name || scrapedInfo.title || scrapedInfo.name || '',

            // Combine description sources for richer content
            description: this.buildBestDescription(scrapedInfo),

            // Map target audience with more specificity
            targetAudience: scrapedInfo.brandInfo?.targetAudience || 'General audience',

            // Use the discovered logo
            logo: scrapedInfo.brandInfo?.logo || '',

            // Include location info in audienceLocation if available
            audienceLocation: this.buildAudienceLocation(scrapedInfo),

            // Combine benefits from multiple sources and clean them
            benefits: this.buildComprehensiveBenefits(scrapedInfo),

            // Use enhanced tone analysis
            toneOfVoice: this.buildToneOfVoice(scrapedInfo),

            // Use detected industry
            industry: scrapedInfo.brandInfo?.industry || 'General',

            // Combine tags from multiple sources
            tags: this.buildComprehensiveTags(scrapedInfo)
        };

        // Initialize article settings with defaults
        const articleSettings = {
            useBrandInfo: true,
            articleLength: 'medium',
            language: 'English',
            specificInstructions: '',
            exclusions: ''
        };

        // Create domain with scraped information and metadata
        const domain = new Domain({
            name: scrapedInfo.name || url,
            url: scrapedInfo.url || url,
            title: scrapedInfo.title || '',
            description: scrapedInfo.description || '',
            brandInfo,
            articleSettings,
            userId: req.user.id,
            metadata: {
                scrapedAt: new Date(scrapedInfo.metadata?.scrapedAt || Date.now()),
                confidence: scrapedInfo.metadata?.confidence || 0,
                dataPoints: scrapedInfo.metadata?.dataPoints || 0,
                fallback: scrapedInfo.metadata?.fallback || false,
                error: scrapedInfo.metadata?.error || null
            }
        });

        const savedDomain = await domain.save();

        // Log success with quality metrics
        console.log(`Domain "${savedDomain.name}" created successfully:`, {
            confidence: savedDomain.metadata.confidence,
            dataPoints: savedDomain.metadata.dataPoints,
            fallback: savedDomain.metadata.fallback,
            benefitsFound: brandInfo.benefits.length,
            tagsFound: brandInfo.tags.length,
            hasLogo: !!brandInfo.logo,
            hasDescription: !!brandInfo.description,
            detectedIndustry: brandInfo.industry,
            detectedAudience: brandInfo.targetAudience
        });

        res.status(201).json(savedDomain);
    } catch (error) {
        console.error('Error creating domain:', error);
        res.status(400).json({ error: 'Failed to create domain', details: error.message });
    }
};

// Update domain
exports.updateDomain = async (req, res) => {
    try {
        const domain = await Domain.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            req.body,
            { new: true, runValidators: true }
        );
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }
        res.json(domain);
    } catch (error) {
        res.status(400).json({ error: 'Failed to update domain', details: error.message });
    }
};

// Update domain brand info
exports.updateDomainBrandInfo = async (req, res) => {
    try {
        const { brandInfo } = req.body;

        // Validate required fields
        if (!brandInfo || !brandInfo.name) {
            return res.status(400).json({ error: 'Brand name is required' });
        }

        // Clean up and validate brand info
        const cleanBrandInfo = {
            name: brandInfo.name.trim(),
            description: brandInfo.description?.trim() || '',
            targetAudience: brandInfo.targetAudience?.trim() || 'General audience',
            logo: brandInfo.logo?.trim(),
            audienceLocation: brandInfo.audienceLocation?.trim() || 'Global',
            benefits: (brandInfo.benefits || []).filter(benefit => benefit.trim()),
            toneOfVoice: brandInfo.toneOfVoice || 'Professional',
            industry: brandInfo.industry?.trim() || 'General',
            tags: (brandInfo.tags || []).filter(tag => tag.trim())
        };

        // Get existing domain to preserve any settings not being updated
        const existingDomain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!existingDomain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        // Merge with existing brand info to preserve any fields not being updated
        const mergedBrandInfo = {
            ...existingDomain.brandInfo.toObject(),
            ...cleanBrandInfo
        };

        const domain = await Domain.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            { brandInfo: mergedBrandInfo },
            { new: true, runValidators: true }
        );

        res.json(domain);
    } catch (error) {
        console.error('Error updating domain brand info:', error);
        res.status(400).json({
            error: 'Failed to update domain brand info',
            details: error.message
        });
    }
};

// Delete domain
exports.deleteDomain = async (req, res) => {
    try {
        const domain = await Domain.findOneAndDelete({ _id: req.params.id, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }
        res.json({ message: 'Domain deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: 'Failed to delete domain', details: error.message });
    }
};

// Delete all articles in a domain
exports.deleteArticlesByDomain = async (req, res) => {
    try {
        const domain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        await Article.deleteMany({ domainId: req.params.id, userId: req.user.id });
        res.json({ message: 'All articles in domain deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: 'Failed to delete articles', details: error.message });
    }
};

// Get articles by domain
exports.getArticlesByDomain = async (req, res) => {
    try {
        const domain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        const articles = await Article.find({ domainId: req.params.id, userId: req.user.id }).sort({ createdAt: -1 });
        res.json(articles);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch articles for domain', details: error.message });
    }
};

// Set default domain
exports.setDefaultDomain = async (req, res) => {
    try {
        // Remove default from all domains for this user
        await Domain.updateMany({ userId: req.user.id }, { isDefault: false });

        // Set the specified domain as default
        const domain = await Domain.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            { isDefault: true },
            { new: true }
        );

        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        res.json(domain);
    } catch (error) {
        res.status(500).json({ error: 'Failed to set default domain', details: error.message });
    }
};

// Update domain article settings
exports.updateDomainArticleSettings = async (req, res) => {
    try {
        const { articleSettings } = req.body;

        // Validate and clean article settings
        const cleanSettings = {
            useBrandInfo: articleSettings.useBrandInfo ?? true,
            articleLength: articleSettings.articleLength || 'medium',
            language: articleSettings.language?.trim() || 'English',
            specificInstructions: articleSettings.specificInstructions?.trim() || '',
            exclusions: articleSettings.exclusions?.trim() || ''
        };

        // Get existing domain to preserve any settings not being updated
        const existingDomain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!existingDomain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        // Merge with existing settings to preserve any fields not being updated
        const mergedSettings = {
            ...existingDomain.articleSettings.toObject(),
            ...cleanSettings
        };

        const domain = await Domain.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            { articleSettings: mergedSettings },
            { new: true, runValidators: true }
        );

        res.json(domain);
    } catch (error) {
        console.error('Error updating domain article settings:', error);
        res.status(400).json({
            error: 'Failed to update domain article settings',
            details: error.message
        });
    }
};

// Update domain hosting settings
exports.updateDomainHosting = async (req, res) => {
    try {
        const { domain, subdomain } = req.body;

        // Get existing domain to preserve any settings not being updated
        const existingDomain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!existingDomain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        // If both domain and subdomain are empty strings, it means we're removing the domain
        const isRemovingDomain = domain === '' && subdomain === '';

        // Only validate required fields if we're not removing the domain
        if (!isRemovingDomain && (!domain || !subdomain)) {
            return res.status(400).json({ error: 'Domain and subdomain are required' });
        }

        const hostingSettings = isRemovingDomain ? {
            domain: '',
            subdomain: '',
            isVerified: false,
            cnameRecords: null
        } : {
            domain: domain.trim(),
            subdomain: subdomain.trim(),
            isVerified: false,
            cnameRecords: {
                one: {
                    host: '_c119353c154c717cb2e53d8e55c4f8d1.blog',
                    value: '_7b77f7f2fd3a2e731bd4d44dc7072ae5.xlfgrmvvlj.acm-validations.aws.'
                },
                two: {
                    host: 'blog',
                    value: 'domain-connection-757144916.eu-central-1.elb.amazonaws.com'
                }
            }
        };

        const updatedDomain = await Domain.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            { hostingSettings },
            { new: true, runValidators: true }
        );

        res.json(updatedDomain);
    } catch (error) {
        console.error('Error updating domain hosting settings:', error);
        res.status(400).json({
            error: 'Failed to update domain hosting settings',
            details: error.message
        });
    }
};

// Verify domain hosting settings
exports.verifyDomainHosting = async (req, res) => {
    try {
        const domain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!domain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        // Here you would implement actual DNS verification logic
        // For now, we'll just mark it as verified
        const updatedDomain = await Domain.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            { 'hostingSettings.isVerified': true },
            { new: true }
        );

        res.json(updatedDomain);
    } catch (error) {
        console.error('Error verifying domain hosting:', error);
        res.status(400).json({
            error: 'Failed to verify domain hosting',
            details: error.message
        });
    }
};

// Update domain design settings
exports.updateDomainDesign = async (req, res) => {
    try {
        const { logo, articleTheme, layout, colors, font } = req.body;

        // Get existing domain to preserve any settings not being updated
        const existingDomain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!existingDomain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        // Merge with existing design settings to preserve any fields not being updated
        const mergedDesignSettings = {
            ...existingDomain.designSettings || {},
            ...(logo && { logo }),
            ...(articleTheme && { articleTheme }),
            ...(layout && {
                layout: {
                    ...existingDomain.designSettings?.layout || {},
                    ...layout
                }
            }),
            ...(colors && {
                colors: {
                    ...existingDomain.designSettings?.colors || {},
                    ...colors
                }
            }),
            ...(font && { font })
        };

        const domain = await Domain.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            { designSettings: mergedDesignSettings },
            { new: true, runValidators: true }
        );

        res.json(domain);
    } catch (error) {
        console.error('Error updating domain design settings:', error);
        res.status(400).json({
            error: 'Failed to update domain design settings',
            details: error.message
        });
    }
};

// Update domain navigation settings
exports.updateDomainNavigation = async (req, res) => {
    try {
        const { homeButtonEnabled, ctaButtonDisabled, ctaButtonText, ctaButtonUrl, headerLinks, footerLinks } = req.body;

        console.log('Received navigation settings update:', req.body);

        // Get existing domain to preserve any settings not being updated
        const existingDomain = await Domain.findOne({ _id: req.params.id, userId: req.user.id });
        if (!existingDomain) {
            return res.status(404).json({ error: 'Domain not found' });
        }

        // Create navigationSettings object with proper structure
        const navigationSettings = {
            homeButtonEnabled: homeButtonEnabled !== undefined ? homeButtonEnabled : (existingDomain.navigationSettings?.homeButtonEnabled || false),
            ctaButtonDisabled: ctaButtonDisabled !== undefined ? ctaButtonDisabled : (existingDomain.navigationSettings?.ctaButtonDisabled || false),
            ctaButtonText: ctaButtonText !== undefined ? ctaButtonText : (existingDomain.navigationSettings?.ctaButtonText || ''),
            ctaButtonUrl: ctaButtonUrl !== undefined ? ctaButtonUrl : (existingDomain.navigationSettings?.ctaButtonUrl || ''),
            headerLinks: headerLinks !== undefined ? headerLinks : (existingDomain.navigationSettings?.headerLinks || []),
            footerLinks: footerLinks !== undefined ? footerLinks : (existingDomain.navigationSettings?.footerLinks || [])
        };

        console.log('Navigation settings to save:', navigationSettings);

        // Update the domain with the new navigation settings
        const domain = await Domain.findOneAndUpdate(
            { _id: req.params.id, userId: req.user.id },
            { navigationSettings: navigationSettings },
            { new: true, runValidators: true }
        );

        console.log('Updated domain navigation settings successfully');

        res.json(domain);
    } catch (error) {
        console.error('Error updating domain navigation settings:', error);
        res.status(400).json({
            error: 'Failed to update domain navigation settings',
            details: error.message
        });
    }
};

// Helper methods for building comprehensive brand information
exports.buildBestDescription = (scrapedInfo) => {
    // Combine multiple description sources for richer content
    const sources = [
        scrapedInfo.brandInfo?.description,
        scrapedInfo.description,
        scrapedInfo.brandInfo?.summary
    ].filter(Boolean);

    if (sources.length === 0) return '';

    // Use the longest meaningful description
    const bestDescription = sources.reduce((best, current) => {
        return current.length > best.length ? current : best;
    }, '');

    // Enhance with company info if available
    let enhanced = bestDescription;
    if (scrapedInfo.brandInfo?.companySize && scrapedInfo.brandInfo.companySize !== 'Unknown') {
        enhanced += ` This is a ${scrapedInfo.brandInfo.companySize.toLowerCase()} company`;
        if (scrapedInfo.brandInfo?.headquarters) {
            enhanced += ` based in ${scrapedInfo.brandInfo.headquarters}`;
        }
        enhanced += '.';
    }

    return enhanced.length > 500 ? enhanced.substring(0, 500) + '...' : enhanced;
};

exports.buildAudienceLocation = (scrapedInfo) => {
    const location = scrapedInfo.brandInfo?.audienceLocation || 'Global';
    const headquarters = scrapedInfo.brandInfo?.headquarters;

    if (headquarters && location === 'Global') {
        // If we have headquarters info but location is global, include both
        return `Global (Headquarters: ${headquarters})`;
    }

    return location;
};

exports.buildComprehensiveBenefits = (scrapedInfo) => {
    const allBenefits = [
        ...(scrapedInfo.brandInfo?.benefits || []),
        ...(scrapedInfo.brandInfo?.services || [])
    ];

    // Clean and deduplicate benefits
    const cleanBenefits = allBenefits
        .filter(Boolean)
        .map(benefit => benefit.trim())
        .filter(benefit => benefit.length > 5 && benefit.length < 200)
        .filter((benefit, index, arr) =>
            arr.findIndex(b => b.toLowerCase() === benefit.toLowerCase()) === index
        );

    // Add contact-based benefits if available
    if (scrapedInfo.brandInfo?.contact?.email || scrapedInfo.brandInfo?.contact?.phone) {
        cleanBenefits.push('Direct customer support available');
    }

    // Add social media benefits if available
    const socialPlatforms = Object.keys(scrapedInfo.brandInfo?.socialMedia || {});
    if (socialPlatforms.length > 0) {
        cleanBenefits.push(`Active on ${socialPlatforms.length} social platform${socialPlatforms.length > 1 ? 's' : ''}`);
    }

    return cleanBenefits.slice(0, 8); // Limit to 8 benefits
};

exports.buildToneOfVoice = (scrapedInfo) => {
    const baseTone = scrapedInfo.brandInfo?.toneOfVoice || 'Professional';
    const industry = scrapedInfo.brandInfo?.industry;
    const audience = scrapedInfo.brandInfo?.targetAudience;

    // Enhance tone with industry and audience context
    let enhancedTone = baseTone;

    if (industry && industry !== 'General') {
        enhancedTone += ` and ${industry.toLowerCase()}-focused`;
    }

    if (audience && audience !== 'General audience') {
        enhancedTone += `, tailored for ${audience.toLowerCase()}`;
    }

    return enhancedTone;
};

exports.buildComprehensiveTags = (scrapedInfo) => {
    const allTags = [
        ...(scrapedInfo.brandInfo?.tags || []),
        ...(scrapedInfo.keywords || []),
        ...(scrapedInfo.brandInfo?.keywords || [])
    ];

    // Add industry-specific tags
    if (scrapedInfo.brandInfo?.industry && scrapedInfo.brandInfo.industry !== 'General') {
        allTags.push(scrapedInfo.brandInfo.industry.toLowerCase());
    }

    // Add audience-specific tags
    if (scrapedInfo.brandInfo?.targetAudience && scrapedInfo.brandInfo.targetAudience !== 'General audience') {
        const audienceWords = scrapedInfo.brandInfo.targetAudience.toLowerCase().split(' ');
        allTags.push(...audienceWords.filter(word => word.length > 2));
    }

    // Add social media platform tags
    const socialPlatforms = Object.keys(scrapedInfo.brandInfo?.socialMedia || {});
    if (socialPlatforms.length > 0) {
        allTags.push('social media', 'online presence');
    }

    // Clean and deduplicate tags
    const cleanTags = allTags
        .filter(Boolean)
        .map(tag => tag.trim().toLowerCase())
        .filter(tag => tag.length > 2 && tag.length < 25)
        .filter((tag, index, arr) => arr.indexOf(tag) === index) // Remove duplicates
        .filter(tag => !tag.includes('http')) // Remove URLs
        .slice(0, 12); // Limit to 12 tags

    return cleanTags;
};
