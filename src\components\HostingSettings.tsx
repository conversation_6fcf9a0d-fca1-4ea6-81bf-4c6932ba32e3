import React, { useState, useEffect, useMemo } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import DailyDelightPreview from '@/assets/Images/dailydelight.png';
import ModernCapsulePreview from '@/assets/Images/moderncapsule.png';
import { Eye, Copy, ChevronUp, ChevronDown, Loader2, X, Plus, Home, MousePointer, MenuSquare, MousePointerClick, FootprintsIcon } from 'lucide-react';
import { useDomain } from '@/contexts/DomainContext';
import { toast } from 'sonner';
import { useToast } from "@/components/ui/use-toast";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { HexColorPicker } from "react-colorful";
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "./ui/command";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";
import { fontService, GoogleFont } from '@/services/fontService';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { domainService } from '@/services/domainService';
import { Domain } from '@/types/Article';
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";

const HostingSettings = () => {
    const { currentDomain, refreshDomains } = useDomain();
    const [showDomainCard, setShowDomainCard] = useState(true);
    const [showDesignCard, setShowDesignCard] = useState(true);
    const [showNavigationCard, setShowNavigationCard] = useState(true);
    const [isDomainConnected, setIsDomainConnected] = useState(false);
    const [showRemoveDialog, setShowRemoveDialog] = useState(false);
    const [domain, setDomain] = useState('');
    const [subdomain, setSubdomain] = useState('');
    const { toast: useToastToast } = useToast();
    const [isLoading, setIsLoading] = useState(false);
    const [isVerifying, setIsVerifying] = useState(false);
    const [open, setOpen] = useState(false);

    // Design settings state
    const [logo, setLogo] = useState('');
    const [brandColor, setBrandColor] = useState('#3c484c');
    const [accentColor, setAccentColor] = useState('#cec4c0');
    const [fonts, setFonts] = useState<GoogleFont[]>([]);
    const [selectedFont, setSelectedFont] = useState<string>('Inter');
    const [searchQuery, setSearchQuery] = useState('');
    const [categorizedFonts, setCategorizedFonts] = useState<Record<string, GoogleFont[]>>({});
    const [popularFonts, setPopularFonts] = useState<GoogleFont[]>([]);
    const [layoutType, setLayoutType] = useState('grid-3');
    const [selectedTheme, setSelectedTheme] = useState('Daily Delight');
    const [logoError, setLogoError] = useState('');
    const [isLoadingFonts, setIsLoadingFonts] = useState(false);

    // Navigation settings state
    const [homeButtonEnabled, setHomeButtonEnabled] = useState(false);
    const [ctaButtonDisabled, setCtaButtonDisabled] = useState(true); // Default to disabled
    const [ctaButtonText, setCtaButtonText] = useState('');
    const [ctaButtonUrl, setCtaButtonUrl] = useState('');
    const [headerLinks, setHeaderLinks] = useState<{ id: string, text: string, url: string }[]>([]);
    const [footerLinks, setFooterLinks] = useState<{ id: string, text: string, url: string }[]>([]);
    const [isSavingNavigation, setIsSavingNavigation] = useState(false);
    const [navigationOpen, setNavigationOpen] = useState(true);
    const [linkErrors, setLinkErrors] = useState<Record<string, { text?: boolean, url?: boolean }>>({});
    const [ctaButtonUrlError, setCtaButtonUrlError] = useState(false);
    const [showValidationErrors, setShowValidationErrors] = useState(false); // Control when to show validation errors

    const MAX_LOGO_SIZE = 5 * 1024 * 1024;

    // Helper function to validate a URL
    const isValidUrl = (url: string): boolean => {
        if (!url.trim()) return false;

        try {
            // Try to create a URL object to validate
            // If it fails, we'll add a protocol and try again
            let urlToTest = url;
            if (!urlToTest.startsWith('http://') && !urlToTest.startsWith('https://')) {
                urlToTest = `https://${urlToTest}`;
            }

            new URL(urlToTest); // This will throw if invalid
            return true;
        } catch (e) {
            return false;
        }
    };

    // Initialize domain settings from domain
    useEffect(() => {
        if (currentDomain?.hostingSettings) {
            setDomain(currentDomain.hostingSettings.domain || '');
            setSubdomain(currentDomain.hostingSettings.subdomain || '');
            setIsDomainConnected(currentDomain.hostingSettings.isVerified || false);
        }

        if (currentDomain) {
            console.log('Current domain navigation settings:', currentDomain.navigationSettings);
        }

        if (currentDomain?.designSettings) {
            setLogo(currentDomain.designSettings.logo || '');
            setBrandColor(currentDomain.designSettings.colors?.brand || '#3c484c');
            setAccentColor(currentDomain.designSettings.colors?.accent || '#cec4c0');
            setSelectedFont(currentDomain.designSettings.font || 'Inter');
            if (currentDomain.designSettings.layout?.listEnabled) {
                setLayoutType('list');
            } else {
                setLayoutType(currentDomain.designSettings.layout?.grid || 'grid-3');
            }
            setSelectedTheme(currentDomain.designSettings.articleTheme || 'Daily Delight');
        }

        // Initialize navigation settings
        if (currentDomain) {
            // Always set default values first - CTA button disabled by default
            setHomeButtonEnabled(false);
            setCtaButtonDisabled(true); // Default to disabled
            setCtaButtonText('');
            setCtaButtonUrl('');
            setShowValidationErrors(false); // Reset validation errors

            // Then override with values from domain if they exist
            if (currentDomain.navigationSettings) {
                setHomeButtonEnabled(currentDomain.navigationSettings.homeButtonEnabled || false);

                // For CTA button, if ctaButtonDisabled is explicitly false, then the button is enabled
                // Otherwise, keep it disabled (default)
                const isCTADisabled = currentDomain.navigationSettings.ctaButtonDisabled !== false;
                setCtaButtonDisabled(isCTADisabled);

                // Only set CTA text/URL if they exist in the domain settings and CTA button is enabled
                if (!isCTADisabled) {
                    if (currentDomain.navigationSettings.ctaButtonText) {
                        setCtaButtonText(currentDomain.navigationSettings.ctaButtonText);
                    }
                    if (currentDomain.navigationSettings.ctaButtonUrl) {
                        setCtaButtonUrl(currentDomain.navigationSettings.ctaButtonUrl);
                    }
                }

                if (Array.isArray(currentDomain.navigationSettings.headerLinks) && currentDomain.navigationSettings.headerLinks.length > 0) {
                    console.log('Loading header links:', currentDomain.navigationSettings.headerLinks);
                    setHeaderLinks(currentDomain.navigationSettings.headerLinks);
                } else {
                    setHeaderLinks([{ id: Date.now().toString(), text: '', url: '' }]);
                }

                if (Array.isArray(currentDomain.navigationSettings.footerLinks) && currentDomain.navigationSettings.footerLinks.length > 0) {
                    console.log('Loading footer links:', currentDomain.navigationSettings.footerLinks);
                    setFooterLinks(currentDomain.navigationSettings.footerLinks);
                } else {
                    setFooterLinks([{ id: Date.now().toString(), text: '', url: '' }]);
                }
            } else {
                // Default initialization with one empty field each
                setHeaderLinks([{ id: Date.now().toString(), text: '', url: '' }]);
                setFooterLinks([{ id: Date.now().toString(), text: '', url: '' }]);
            }
        }
    }, [currentDomain]);

    // Separate effect for loading fonts
    useEffect(() => {
        const loadFonts = async () => {
            if (isLoadingFonts) return; // Prevent multiple simultaneous loads
            setIsLoadingFonts(true);
            try {
                const allFonts = await fontService.getFonts();
                setFonts(allFonts);
                setPopularFonts(fontService.getPopularFonts());
                setCategorizedFonts(fontService.getCategorizedFonts());
                fontService.preloadPopularFonts();
            } catch (error) {
                console.error('Error loading fonts:', error);
                toast.error('Failed to load fonts');
            } finally {
                setIsLoadingFonts(false);
            }
        };

        loadFonts();
    }, []); // Only load fonts once when component mounts

    const handleNext = async () => {
        if (!currentDomain?._id) return;

        setIsLoading(true);
        try {
            const updatedDomain = await domainService.updateHostingSettings(currentDomain._id, {
                domain,
                subdomain
            });
            setIsDomainConnected(true);
            toast.success('Domain settings updated successfully');
        } catch (error) {
            console.error('Error updating hosting settings:', error);
            toast.error('Failed to update domain settings');
        } finally {
            setIsLoading(false);
        }
    };

    const handleVerify = async () => {
        if (!currentDomain?._id) return;

        setIsVerifying(true);
        try {
            await domainService.verifyHosting(currentDomain._id);
            toast.success('Domain verified successfully');
        } catch (error) {
            console.error('Error verifying domain:', error);
            toast.error('Failed to verify domain');
        } finally {
            setIsVerifying(false);
        }
    };

    const handleSaveDesign = async () => {
        if (!currentDomain?._id) return;

        setIsLoading(true);
        try {
            const updatedDomain = await domainService.updateDesignSettings(currentDomain._id, {
                logo,
                articleTheme: selectedTheme,
                layout: {
                    grid: layoutType === 'list' ? 'grid-3' : layoutType,
                    listEnabled: layoutType === 'list'
                },
                colors: {
                    brand: brandColor,
                    accent: accentColor
                },
                font: selectedFont
            });

            // No need to call refreshDomains() since we already have the updated domain
            toast.success('Design settings saved successfully');
        } catch (error) {
            console.error('Error saving design settings:', error);
            toast.error('Failed to save design settings');
        } finally {
            setIsLoading(false);
        }
    };

    const handleRemoveDomain = async () => {
        if (!currentDomain?._id) return;

        setIsLoading(true);
        try {
            await domainService.updateHostingSettings(currentDomain._id, {
                domain: '',
                subdomain: ''
            });
            setIsDomainConnected(false);
            setDomain('');
            setSubdomain('');
            setShowRemoveDialog(false);
            toast.success('Domain connection removed');
        } catch (error) {
            console.error('Error removing domain:', error);
            toast.error('Failed to remove domain connection');
        } finally {
            setIsLoading(false);
        }
    };

    const handleCopy = (text: string) => {
        navigator.clipboard.writeText(text);
        toast.success("Copied to clipboard!");
    };

    const handleFontSelect = async (font: GoogleFont) => {
        try {
            await fontService.loadFont(font.family);
            setSelectedFont(font.family);
            setOpen(false);
        } catch (error) {
            console.error('Error loading font:', error);
            toast.error('Failed to load font');
        }
    };

    const filteredFonts = useMemo(() => {
        if (!searchQuery) return fonts;
        return fonts.filter(font =>
            font.family.toLowerCase().includes(searchQuery.toLowerCase()) ||
            font.category.toLowerCase().includes(searchQuery.toLowerCase())
        );
    }, [fonts, searchQuery]);

    const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        setLogoError('');

        if (!file) return;

        // Validate file size
        if (file.size > MAX_LOGO_SIZE) {
            setLogoError('Logo file size must be less than 5MB');
            return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            setLogoError('Please upload an image file');
            return;
        }

        try {
            // Convert to base64 for preview and storage
            const reader = new FileReader();
            reader.onloadend = () => {
                const base64String = reader.result as string;
                setLogo(base64String);
            };
            reader.readAsDataURL(file);
        } catch (error) {
            console.error('Error uploading logo:', error);
            setLogoError('Failed to upload logo');
        }
    };

    const handleRemoveLogo = () => {
        setLogo('');
        setLogoError('');
    };

    const handlePreview = () => {
        if (!currentDomain?._id) return;

        // Get the base URL (localhost or production)
        const baseUrl = window.location.origin;

        // Get the blog URL from domain settings
        const blogUrl = currentDomain.hostingSettings?.domain
            ? `${currentDomain.hostingSettings.subdomain}.${currentDomain.hostingSettings.domain}`
            : `blog.yourdomain.com`;

        window.open(`${baseUrl}/${blogUrl}`, '_blank');
    };

    const handleSaveNavigation = async () => {
        if (!currentDomain?._id) return;

        setIsSavingNavigation(true);
        setShowValidationErrors(true); // Show validation errors when attempting to save

        try {
            // Validate CTA button fields if button is enabled
            if (!ctaButtonDisabled) {
                // Check if text is provided
                if (!ctaButtonText.trim()) {
                    toast.error('CTA Button text is required when button is enabled');
                    throw new Error('CTA Button text is required');
                }

                // Check if URL is provided and valid
                if (!ctaButtonUrl.trim()) {
                    toast.error('CTA Button URL is required when button is enabled');
                    throw new Error('CTA Button URL is required');
                }

                // Validate URL format
                const isCtaUrlValid = isValidUrl(ctaButtonUrl);
                setCtaButtonUrlError(!isCtaUrlValid);
                if (!isCtaUrlValid) {
                    toast.error('Invalid CTA Button URL format');
                    throw new Error('Invalid CTA Button URL format');
                }
            }

            // Validate and filter links
            const validateLink = (link: { id: string, text: string, url: string }) => {
                // Ensure both text and URL are provided
                if (!link.text.trim() || !link.url.trim()) {
                    return false;
                }

                // Basic URL validation
                try {
                    // Try to create a URL object to validate
                    // If it fails, we'll add a protocol and try again
                    let url = link.url;
                    if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = `https://${url}`;
                    }

                    new URL(url); // This will throw if invalid
                    return true;
                } catch (e) {
                    console.warn(`Invalid URL: ${link.url}`);
                    // Set error for this link
                    setLinkErrors(prev => ({
                        ...prev,
                        [link.id]: {
                            ...prev[link.id],
                            url: true
                        }
                    }));
                    return false;
                }
            };

            // Filter and format header links
            const filteredHeaderLinks = headerLinks
                .filter(link => link.text.trim() !== '' && link.url.trim() !== '' && validateLink(link))
                .map(link => {
                    // Format URL if needed
                    let url = link.url.trim();
                    if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = `https://${url}`;
                    }

                    return {
                        id: link.id,
                        text: link.text.trim(),
                        url: url
                    };
                });

            // Filter and format footer links
            const filteredFooterLinks = footerLinks
                .filter(link => link.text.trim() !== '' && link.url.trim() !== '' && validateLink(link))
                .map(link => {
                    // Format URL if needed
                    let url = link.url.trim();
                    if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = `https://${url}`;
                    }

                    return {
                        id: link.id,
                        text: link.text.trim(),
                        url: url
                    };
                });

            // Format CTA button URL if needed
            let formattedCtaUrl = ctaButtonUrl.trim();
            if (formattedCtaUrl && !formattedCtaUrl.startsWith('http://') && !formattedCtaUrl.startsWith('https://')) {
                formattedCtaUrl = `https://${formattedCtaUrl}`;
            }

            // If CTA button is disabled, clear text and URL
            const ctaSettings = !ctaButtonDisabled ? {
                ctaButtonText: ctaButtonText.trim(),
                ctaButtonUrl: formattedCtaUrl
            } : {
                ctaButtonText: '',
                ctaButtonUrl: ''
            };

            console.log('Saving navigation settings:', {
                homeButtonEnabled,
                ctaButtonDisabled,
                ...ctaSettings,
                headerLinks: filteredHeaderLinks,
                footerLinks: filteredFooterLinks
            });

            const updatedDomain = await domainService.updateNavigationSettings(currentDomain._id, {
                homeButtonEnabled,
                ctaButtonDisabled,
                ...ctaSettings,
                headerLinks: filteredHeaderLinks,
                footerLinks: filteredFooterLinks
            });

            console.log('Navigation settings saved successfully:', updatedDomain);

            // Update local state with the saved links
            if (updatedDomain.navigationSettings) {
                if (Array.isArray(updatedDomain.navigationSettings.headerLinks)) {
                    setHeaderLinks(updatedDomain.navigationSettings.headerLinks.length > 0
                        ? updatedDomain.navigationSettings.headerLinks
                        : [{ id: Date.now().toString(), text: '', url: '' }]
                    );
                }

                if (Array.isArray(updatedDomain.navigationSettings.footerLinks)) {
                    setFooterLinks(updatedDomain.navigationSettings.footerLinks.length > 0
                        ? updatedDomain.navigationSettings.footerLinks
                        : [{ id: Date.now().toString(), text: '', url: '' }]
                    );
                }

                // Update CTA button settings
                if (updatedDomain.navigationSettings.ctaButtonText !== undefined) {
                    setCtaButtonText(updatedDomain.navigationSettings.ctaButtonText);
                }
                if (updatedDomain.navigationSettings.ctaButtonUrl !== undefined) {
                    setCtaButtonUrl(updatedDomain.navigationSettings.ctaButtonUrl);
                }
            }

            // No need to call refreshDomains() since we already have the updated domain
            // Just update the current domain in the context if needed
            toast.success('Navigation settings saved successfully');
        } catch (error) {
            console.error('Error saving navigation settings:', error);
            toast.error('Failed to save navigation settings');
        } finally {
            setIsSavingNavigation(false);
        }
    };

    const handleAddHeaderLink = () => {
        const newLinkId = Date.now().toString();
        setHeaderLinks([...headerLinks, { id: newLinkId, text: '', url: '' }]);
        // Initialize error state for the new link
        setLinkErrors(prev => ({
            ...prev,
            [newLinkId]: { text: false, url: false }
        }));
    };

    const handleAddFooterLink = () => {
        const newLinkId = Date.now().toString();
        setFooterLinks([...footerLinks, { id: newLinkId, text: '', url: '' }]);
        // Initialize error state for the new link
        setLinkErrors(prev => ({
            ...prev,
            [newLinkId]: { text: false, url: false }
        }));
    };

    const handleRemoveHeaderLink = (id: string) => {
        // Allow removing all fields
        setHeaderLinks(headerLinks.filter(link => link.id !== id));
        // Clean up error state for the removed link
        setLinkErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[id];
            return newErrors;
        });
    };

    const handleRemoveFooterLink = (id: string) => {
        // Allow removing all fields
        setFooterLinks(footerLinks.filter(link => link.id !== id));
        // Clean up error state for the removed link
        setLinkErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[id];
            return newErrors;
        });
    };

    // Update header link with validation
    const updateHeaderLink = (id: string, field: 'text' | 'url', value: string) => {
        setHeaderLinks(headerLinks.map(link =>
            link.id === id ? { ...link, [field]: value } : link
        ));

        // Only validate URL field if showValidationErrors is true
        if (field === 'url' && showValidationErrors) {
            setLinkErrors(prev => ({
                ...prev,
                [id]: {
                    ...prev[id],
                    url: value.trim() !== '' && !isValidUrl(value)
                }
            }));
        }
    };

    // Update footer link with validation
    const updateFooterLink = (id: string, field: 'text' | 'url', value: string) => {
        setFooterLinks(footerLinks.map(link =>
            link.id === id ? { ...link, [field]: value } : link
        ));

        // Only validate URL field if showValidationErrors is true
        if (field === 'url' && showValidationErrors) {
            setLinkErrors(prev => ({
                ...prev,
                [id]: {
                    ...prev[id],
                    url: value.trim() !== '' && !isValidUrl(value)
                }
            }));
        }
    };

    return (
        <div className="space-y-6">
            {/* Domain Card */}
            <Card className="p-6 overflow-hidden transition-all duration-500 ease-in-out">
                <div className="flex items-start">
                    <div className="w-[300px] shrink-0">
                        <div className="flex items-center gap-2 cursor-pointer group" onClick={() => setShowDomainCard(!showDomainCard)}>
                            <h2 className="text-lg font-medium text-gray-900">Domain</h2>
                            <div className={`transition-transform duration-500 ease-in-out transform ${showDomainCard ? 'rotate-180' : 'rotate-0'} group-hover:scale-110`}>
                                <ChevronDown className="w-5 h-5 text-gray-400" />
                            </div>
                        </div>
                        <p className="text-gray-600 mt-1">
                            Publish automatically all blog articles into your own subdomain
                        </p>
                        <div className="mt-4">
                            <Button
                                variant="ghost"
                                className="text-orange-500 hover:text-orange-600 p-0 h-auto flex items-center gap-2 transition-colors duration-300"
                                onClick={handlePreview}
                            >
                                <Eye className="w-4 h-4" />
                                Preview
                            </Button>
                        </div>
                    </div>

                    <div
                        className={`
                            flex-1 pl-8 
                            transition-all duration-500 ease-in-out
                            transform 
                            ${showDomainCard
                                ? 'opacity-100 max-h-[2000px] translate-y-0'
                                : 'opacity-0 max-h-0 -translate-y-4 overflow-hidden'
                            }
                        `}
                    >
                        {!isDomainConnected ? (
                            <div>
                                <h3 className="text-[15px] font-medium mb-6">Set up your subdomain</h3>

                                <div className="space-y-6">
                                    <div>
                                        <p className="text-gray-700 mb-2">Enter domain to connect</p>
                                        <Input
                                            value={domain}
                                            onChange={(e) => setDomain(e.target.value)}
                                            placeholder="amazon.in"
                                            className="bg-white border-gray-200"
                                        />
                                    </div>

                                    <div>
                                        <p className="text-gray-700 mb-2">Your subdomain</p>
                                        <Input
                                            value={subdomain}
                                            onChange={(e) => setSubdomain(e.target.value)}
                                            placeholder="blog"
                                            className="bg-white border-gray-200"
                                        />
                                    </div>

                                    <div>
                                        <p className="text-gray-700 mb-2">Your blog will be hosted at:</p>
                                        <div className="bg-gray-50 py-2 px-3 rounded text-gray-600">
                                            {subdomain}.{domain || 'yourdomain.com'}
                                        </div>
                                    </div>

                                    <Button
                                        onClick={handleNext}
                                        className="bg-orange-500 hover:bg-orange-600 text-white px-8"
                                        disabled={isLoading}
                                    >
                                        {isLoading ? (
                                            <>
                                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                Updating...
                                            </>
                                        ) : (
                                            'Next'
                                        )}
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <div className="space-y-8">
                                {/* CNAME Record ONE */}
                                <div>
                                    <h3 className="text-[15px] font-medium mb-4">CNAME record (ONE)</h3>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <p className="text-gray-600 mb-2">Host</p>
                                            <div className="flex items-center gap-2">
                                                <Input
                                                    value="_c119353c154c717cb2e53d8e55c4f8d1.blog"
                                                    readOnly
                                                    className="bg-gray-50 border-gray-200"
                                                />
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => handleCopy('_c119353c154c717cb2e53d8e55c4f8d1.blog')}
                                                >
                                                    <Copy className="w-4 h-4 text-gray-500" />
                                                </Button>
                                            </div>
                                        </div>
                                        <div>
                                            <p className="text-gray-600 mb-2">Value</p>
                                            <div className="flex items-center gap-2">
                                                <Input
                                                    value="_7b77f7f2fd3a2e731bd4d44dc7072ae5.xlfgrmvvlj.acm-validations.aws."
                                                    readOnly
                                                    className="bg-gray-50 border-gray-200"
                                                />
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => handleCopy('_7b77f7f2fd3a2e731bd4d44dc7072ae5.xlfgrmvvlj.acm-validations.aws.')}
                                                >
                                                    <Copy className="w-4 h-4 text-gray-500" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* CNAME Record TWO */}
                                <div>
                                    <h3 className="text-[15px] font-medium mb-4">CNAME record (TWO)</h3>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <p className="text-gray-600 mb-2">Host</p>
                                            <div className="flex items-center gap-2">
                                                <Input
                                                    value="blog"
                                                    readOnly
                                                    className="bg-gray-50 border-gray-200"
                                                />
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => handleCopy('blog')}
                                                >
                                                    <Copy className="w-4 h-4 text-gray-500" />
                                                </Button>
                                            </div>
                                        </div>
                                        <div>
                                            <p className="text-gray-600 mb-2">Value</p>
                                            <div className="flex items-center gap-2">
                                                <Input
                                                    value="domain-connection-757144916.eu-central-1.elb.amazonaws.com"
                                                    readOnly
                                                    className="bg-gray-50 border-gray-200"
                                                />
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => handleCopy('domain-connection-757144916.eu-central-1.elb.amazonaws.com')}
                                                >
                                                    <Copy className="w-4 h-4 text-gray-500" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Step by Step Guide */}
                                <div>
                                    <h3 className="text-[15px] font-medium mb-3">Step-by-Step Guide:</h3>
                                    <ol className="list-decimal pl-5 space-y-2 text-gray-600">
                                        <li>Log in to your domain registrar (e.g., GoDaddy, Namecheap).</li>
                                        <li>Navigate to the DNS settings for your domain.</li>
                                        <li>Add both CNAME values above.</li>
                                        <li>Save your changes and verify (can take a few hours)</li>
                                    </ol>
                                    <p className="mt-4 text-gray-600">
                                        Need further guidance? Check our{' '}
                                        <a href="#" className="text-[#C08D6C] hover:text-[#A67A5B]">docs</a>
                                    </p>
                                </div>

                                <div className="flex items-center gap-4">
                                    <Button
                                        onClick={handleVerify}
                                        className="bg-orange-500 hover:bg-orange-600 text-white px-8"
                                        disabled={isVerifying}
                                    >
                                        {isVerifying ? (
                                            <>
                                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                Verifying...
                                            </>
                                        ) : (
                                            'Verify'
                                        )}
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        className="text-orange-500 hover:text-orange-600"
                                        onClick={() => setShowRemoveDialog(true)}
                                    >
                                        Remove domain connection
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </Card>

            {/* Design Card */}
            <Card className="p-6 overflow-hidden transition-all duration-500 ease-in-out">
                <div className="flex items-start">
                    <div className="w-[300px] shrink-0">
                        <div className="flex items-center gap-2 cursor-pointer group" onClick={() => setShowDesignCard(!showDesignCard)}>
                            <h2 className="text-lg font-medium text-gray-900">Design</h2>
                            <div className={`transition-transform duration-500 ease-in-out transform ${showDesignCard ? 'rotate-180' : 'rotate-0'} group-hover:scale-110`}>
                                <ChevronDown className="w-5 h-5 text-gray-400" />
                            </div>
                        </div>
                        <p className="text-gray-400 mt-1">
                            This will be displayed on your profile
                        </p>
                    </div>

                    <div
                        className={`
                            flex-1 pl-8 
                            transition-all duration-500 ease-in-out
                            transform 
                            ${showDesignCard
                                ? 'opacity-100 max-h-[2000px] translate-y-0'
                                : 'opacity-0 max-h-0 -translate-y-4 overflow-hidden'
                            }
                        `}
                    >
                        {/* Design Settings */}
                        <div className="space-y-8">
                            {/* Logo */}
                            <div className="flex items-start gap-6">
                                <div className="w-[300px]">
                                    <h3 className="text-[15px] font-medium text-gray-900">Logo</h3>
                                    <p className="text-gray-400 mt-1.5">This will be displayed on your profile</p>
                                </div>
                                <div className="flex items-center gap-6 flex-1">
                                    <div className="w-24 h-24 rounded-lg bg-gray-50 border-2 border-dashed border-gray-200 flex items-center justify-center overflow-hidden">
                                        {logo ? (
                                            <img
                                                src={logo}
                                                alt="Logo"
                                                className="w-full h-full object-contain p-2"
                                                onError={() => {
                                                    setLogo('');
                                                    setLogoError('Failed to load logo image');
                                                }}
                                            />
                                        ) : (
                                            <div className="text-gray-400 text-sm text-center">
                                                <span className="block">Upload</span>
                                                <span className="block">Logo</span>
                                            </div>
                                        )}
                                    </div>
                                    <div className="flex flex-col gap-2">
                                        <input
                                            type="file"
                                            accept="image/*"
                                            onChange={handleLogoUpload}
                                            className="hidden"
                                            id="logo-upload"
                                        />
                                        <div className="flex gap-2">
                                            <Button
                                                variant="outline"
                                                className="bg-white border-gray-200"
                                                onClick={() => document.getElementById('logo-upload')?.click()}
                                            >
                                                {logo ? 'Change Logo' : 'Upload Logo'}
                                            </Button>
                                            {logo && (
                                                <Button
                                                    variant="outline"
                                                    className="bg-white border-gray-200 text-red-500 hover:text-red-600"
                                                    onClick={handleRemoveLogo}
                                                >
                                                    Remove
                                                </Button>
                                            )}
                                        </div>
                                        {logoError ? (
                                            <p className="text-sm text-red-500">{logoError}</p>
                                        ) : (
                                            <p className="text-sm text-gray-500">Maximum size 5 MB</p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Article Theme */}
                            <div className="flex items-start gap-6">
                                <div className="w-[300px]">
                                    <h3 className="text-[15px] font-medium text-gray-900">Article Theme</h3>
                                    <p className="text-gray-400 mt-1.5">Choose your article template</p>
                                </div>
                                <div className="flex gap-6 flex-1">
                                    <div className="relative">
                                        <input
                                            type="radio"
                                            name="theme"
                                            value="Daily Delight"
                                            id="daily-delight"
                                            className="peer hidden"
                                            checked={selectedTheme === 'Daily Delight'}
                                            onChange={(e) => setSelectedTheme(e.target.value)}
                                        />
                                        <label
                                            htmlFor="daily-delight"
                                            className="block w-[300px] cursor-pointer rounded-lg border bg-white p-4 hover:bg-gray-50 peer-checked:border-orange-500 peer-checked:ring-1 peer-checked:ring-orange-500"
                                        >
                                            <div className="w-full overflow-hidden rounded-lg border border-gray-100 shadow-sm">
                                                <div className="h-[200px] w-full relative">
                                                    <img
                                                        src={ModernCapsulePreview}
                                                        alt="Daily Delight"
                                                        className="w-full h-full object-cover"
                                                    />
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 mt-4">
                                                <div className={`h-4 w-4 rounded-full border-2 flex items-center justify-center ${selectedTheme === 'Daily Delight' ? 'border-orange-500' : 'border-gray-300'}`}>
                                                    {selectedTheme === 'Daily Delight' && (
                                                        <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                                                    )}
                                                </div>
                                                <span className="text-gray-700">Daily Delight</span>
                                            </div>
                                        </label>
                                    </div>
                                    <div className="relative">
                                        <input
                                            type="radio"
                                            name="theme"
                                            value="Modern Capsule"
                                            id="modern-capsule"
                                            className="peer hidden"
                                            checked={selectedTheme === 'Modern Capsule'}
                                            onChange={(e) => setSelectedTheme(e.target.value)}
                                        />
                                        <label
                                            htmlFor="modern-capsule"
                                            className="block w-[300px] cursor-pointer rounded-lg border bg-white p-4 hover:bg-gray-50 peer-checked:border-orange-500 peer-checked:ring-1 peer-checked:ring-orange-500"
                                        >
                                            <div className="w-full overflow-hidden rounded-lg border border-gray-100 shadow-sm">
                                                <div className="h-[200px] w-full relative">
                                                    <img
                                                        src={DailyDelightPreview}
                                                        alt="Modern Capsule"
                                                        className="w-full h-full object-cover"
                                                    />
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 mt-4">
                                                <div className={`h-4 w-4 rounded-full border-2 flex items-center justify-center ${selectedTheme === 'Modern Capsule' ? 'border-orange-500' : 'border-gray-300'}`}>
                                                    {selectedTheme === 'Modern Capsule' && (
                                                        <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                                                    )}
                                                </div>
                                                <span className="text-gray-700">Modern Capsule</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            {/* Blog Layout */}
                            <div className="flex items-start gap-6">
                                <div className="w-[300px]">
                                    <h3 className="text-[15px] font-medium text-gray-900">Blog Layout</h3>
                                    <p className="text-gray-400 mt-1.5">Choose how your blog posts are displayed</p>
                                </div>
                                <div className="flex-1">
                                    <div className="space-y-4">
                                        {/* Grid Layout Options */}
                                        <div className="relative">
                                            <input
                                                type="radio"
                                                name="layout"
                                                value="grid-3"
                                                id="grid-3"
                                                className="peer hidden"
                                                checked={layoutType === 'grid-3'}
                                                onChange={(e) => setLayoutType(e.target.value)}
                                            />
                                            <label
                                                htmlFor="grid-3"
                                                className="block w-[370px] cursor-pointer rounded-lg border bg-white p-3 hover:bg-gray-50 peer-checked:border-orange-500 peer-checked:ring-1 peer-checked:ring-orange-500"
                                            >
                                                <div className="flex gap-1 mb-2">
                                                    <div className="h-12 flex-1 bg-gray-100 rounded"></div>
                                                    <div className="h-12 flex-1 bg-gray-100 rounded"></div>
                                                    <div className="h-12 flex-1 bg-gray-100 rounded"></div>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <div className={`h-4 w-4 rounded-full border-2 ${layoutType === 'grid-3' ? 'border-orange-500 bg-orange-500' : 'border-gray-300'}`}></div>
                                                    <span className="text-sm">3 Column Grid</span>
                                                </div>
                                            </label>
                                        </div>

                                        <div className="relative">
                                            <input
                                                type="radio"
                                                name="layout"
                                                value="grid-4"
                                                id="grid-4"
                                                className="peer hidden"
                                                checked={layoutType === 'grid-4'}
                                                onChange={(e) => setLayoutType(e.target.value)}
                                            />
                                            <label
                                                htmlFor="grid-4"
                                                className="block w-[370px] cursor-pointer rounded-lg border bg-white p-3 hover:bg-gray-50 peer-checked:border-orange-500 peer-checked:ring-1 peer-checked:ring-orange-500"
                                            >
                                                <div className="flex gap-1 mb-2">
                                                    <div className="h-12 flex-1 bg-gray-100 rounded"></div>
                                                    <div className="h-12 flex-1 bg-gray-100 rounded"></div>
                                                    <div className="h-12 flex-1 bg-gray-100 rounded"></div>
                                                    <div className="h-12 flex-1 bg-gray-100 rounded"></div>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <div className={`h-4 w-4 rounded-full border-2 ${layoutType === 'grid-4' ? 'border-orange-500 bg-orange-500' : 'border-gray-300'}`}></div>
                                                    <span className="text-sm">4 Column Grid</span>
                                                </div>
                                            </label>
                                        </div>

                                        <div className="relative">
                                            <input
                                                type="radio"
                                                name="layout"
                                                value="list"
                                                id="list-view"
                                                className="peer hidden"
                                                checked={layoutType === 'list'}
                                                onChange={(e) => setLayoutType(e.target.value)}
                                            />
                                            <label
                                                htmlFor="list-view"
                                                className="block w-[370px] cursor-pointer rounded-lg border bg-white p-3 hover:bg-gray-50 peer-checked:border-orange-500 peer-checked:ring-1 peer-checked:ring-orange-500"
                                            >
                                                <div className="space-y-2 mb-2">
                                                    <div className="flex gap-3">
                                                        <div className="w-20 h-12 bg-gray-100 rounded"></div>
                                                        <div className="flex-1 h-12 bg-gray-100 rounded"></div>
                                                    </div>
                                                    <div className="flex gap-3">
                                                        <div className="w-20 h-12 bg-gray-100 rounded"></div>
                                                        <div className="flex-1 h-12 bg-gray-100 rounded"></div>
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <div className={`h-4 w-4 rounded-full border-2 ${layoutType === 'list' ? 'border-orange-500 bg-orange-500' : 'border-gray-300'}`}></div>
                                                    <span className="text-sm">List View</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Preview Button - Moved below blog layout */}
                            <div className="flex justify-start">
                                <Button
                                    variant="ghost"
                                    className="text-orange-500 hover:text-orange-600 p-0 h-auto flex items-center gap-2 transition-colors duration-300"
                                    onClick={handlePreview}
                                >
                                    <Eye className="w-4 h-4" />
                                    Preview Blog
                                </Button>
                            </div>

                            {/* Brand Color */}
                            <div className="flex items-center gap-6">
                                <div className="w-[300px]">
                                    <h3 className="text-[15px] font-medium text-gray-900">Brand Color</h3>
                                    <p className="text-gray-400 mt-1.5">Choose your main brand color</p>
                                </div>
                                <div className="flex items-center gap-6 flex-1">
                                    <Input
                                        type="text"
                                        value={brandColor}
                                        onChange={(e) => setBrandColor(e.target.value)}
                                        className="w-[100px] bg-white border-gray-200 font-mono"
                                    />
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <button
                                                className="w-8 h-8 rounded border border-gray-200 transition-transform hover:scale-110"
                                                style={{ backgroundColor: brandColor }}
                                            />
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-3" align="start">
                                            <HexColorPicker
                                                color={brandColor}
                                                onChange={setBrandColor}
                                            />
                                        </PopoverContent>
                                    </Popover>
                                </div>
                            </div>

                            {/* Font Color */}
                            <div className="flex items-center gap-6">
                                <div className="w-[300px]">
                                    <h3 className="text-[15px] font-medium text-gray-900">Font Color (Secondary Color)</h3>
                                    <p className="text-gray-400 mt-1.5">Choose your font color</p>
                                </div>
                                <div className="flex items-center gap-6 flex-1">
                                    <Input
                                        type="text"
                                        value={accentColor}
                                        onChange={(e) => setAccentColor(e.target.value)}
                                        className="w-[100px] bg-white border-gray-200 font-mono"
                                    />
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <button
                                                className="w-8 h-8 rounded border border-gray-200 transition-transform hover:scale-110"
                                                style={{ backgroundColor: accentColor }}
                                            />
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-3" align="start">
                                            <HexColorPicker
                                                color={accentColor}
                                                onChange={setAccentColor}
                                            />
                                        </PopoverContent>
                                    </Popover>
                                </div>
                            </div>

                            {/* Font */}
                            <div className="flex items-center gap-6">
                                <div className="w-[300px]">
                                    <h3 className="text-[15px] font-medium text-gray-900">Font</h3>
                                    <p className="text-gray-400 mt-1.5">Choose your Font style</p>
                                </div>
                                <div className="flex-1">
                                    <Popover open={open} onOpenChange={setOpen}>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="outline"
                                                role="combobox"
                                                aria-expanded={open}
                                                className="w-full justify-between bg-white border-gray-200"
                                            >
                                                {selectedFont ? (
                                                    <span style={{ fontFamily: selectedFont }}>
                                                        {selectedFont}
                                                    </span>
                                                ) : (
                                                    "Select a font..."
                                                )}
                                                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-[400px] p-0">
                                            <Command>
                                                <CommandInput
                                                    placeholder="Search fonts..."
                                                    value={searchQuery}
                                                    onValueChange={setSearchQuery}
                                                    className="h-9"
                                                />
                                                {isLoadingFonts ? (
                                                    <div className="p-4 text-center text-sm text-gray-500">
                                                        Loading fonts...
                                                    </div>
                                                ) : (
                                                    <CommandList>
                                                        <CommandEmpty>No font found.</CommandEmpty>
                                                        <CommandGroup className="max-h-[300px] overflow-auto">
                                                            {filteredFonts.map((font) => (
                                                                <CommandItem
                                                                    key={font.family}
                                                                    value={font.family}
                                                                    onSelect={() => handleFontSelect(font)}
                                                                >
                                                                    <div className="flex flex-col w-full gap-1">
                                                                        <div className="flex items-center justify-between">
                                                                            <span style={{ fontFamily: font.family }}>
                                                                                {font.family}
                                                                            </span>
                                                                            {selectedFont === font.family && (
                                                                                <Check className="h-4 w-4" />
                                                                            )}
                                                                        </div>
                                                                        <p
                                                                            className="text-sm text-gray-600"
                                                                            style={{ fontFamily: font.family }}
                                                                        >
                                                                            {font.preview}
                                                                        </p>
                                                                    </div>
                                                                </CommandItem>
                                                            ))}
                                                        </CommandGroup>
                                                    </CommandList>
                                                )}
                                            </Command>
                                        </PopoverContent>
                                    </Popover>
                                </div>
                            </div>

                            {/* Save Button - Moved to bottom */}
                            <div className="flex justify-end pt-4 border-t">
                                <Button
                                    onClick={handleSaveDesign}
                                    className="bg-orange-500 hover:bg-orange-600 text-white px-8"
                                    disabled={isLoadingFonts}
                                >
                                    {isLoadingFonts ? (
                                        <>
                                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            Saving...
                                        </>
                                    ) : (
                                        'Save Changes'
                                    )}
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>

            {/* Navigation Card */}
            <Card className="p-6 overflow-hidden transition-all duration-500 ease-in-out">
                <div className="flex items-start">
                    <div className="w-[300px] shrink-0">
                        <div className="flex items-center gap-2 cursor-pointer group" onClick={() => setShowNavigationCard(!showNavigationCard)}>
                            <h2 className="text-lg font-medium text-gray-900">Navigation</h2>
                            <div className={`transition-transform duration-500 ease-in-out transform ${showNavigationCard ? 'rotate-180' : 'rotate-0'} group-hover:scale-110`}>
                                <ChevronDown className="w-5 h-5 text-gray-400" />
                            </div>
                        </div>
                        <p className="text-gray-600 mt-1">
                            Customize the navigation elements of your blog
                        </p>
                        <div className="mt-4">
                            <Button
                                variant="ghost"
                                className="text-orange-500 hover:text-orange-600 p-0 h-auto flex items-center gap-2 transition-colors duration-300"
                                onClick={handlePreview}
                            >
                                <Eye className="w-4 h-4" />
                                Preview
                            </Button>
                        </div>
                    </div>

                    <div
                        className={`
                            flex-1 pl-8 
                            transition-all duration-500 ease-in-out
                            transform 
                            ${showNavigationCard
                                ? 'opacity-100 max-h-[2000px] translate-y-0'
                                : 'opacity-0 max-h-0 -translate-y-4 overflow-hidden'
                            }
                        `}
                    >
                        {/* Navigation Section */}
                        <div className="space-y-6">
                            <div className="flex items-center justify-between">
                                <h2 className="text-xl font-semibold">Navigation</h2>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setNavigationOpen(!navigationOpen)}
                                    className="text-gray-500"
                                >
                                    {navigationOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                                </Button>
                            </div>

                            {navigationOpen && (
                                <div className="space-y-6">
                                    <p className="text-sm text-gray-500">
                                        Customize the navigation elements of your blog
                                    </p>

                                    {/* Home Button */}
                                    {/* <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <Home className="h-4 w-4 text-orange-500" />
                                            <h3 className="font-medium">Home button</h3>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-gray-500">Enable</span>
                                            <Switch
                                                checked={homeButtonEnabled}
                                                onCheckedChange={setHomeButtonEnabled}
                                            />
                                        </div>
                                    </div> */}

                                    {/* CTA Button */}
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <MousePointerClick className="h-4 w-4 text-orange-500" />
                                            <h3 className="font-medium">CTA Button</h3>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-gray-500">{ctaButtonDisabled ? 'Disabled' : 'Enabled'}</span>
                                            <Switch
                                                checked={!ctaButtonDisabled}
                                                onCheckedChange={(checked) => setCtaButtonDisabled(!checked)}
                                            />
                                        </div>
                                    </div>

                                    {/* CTA Button Text and URL - Only show when CTA button is enabled */}
                                    {!ctaButtonDisabled && (
                                        <div className="space-y-3">
                                            <div className="flex items-center gap-2">
                                                <MousePointer className="h-4 w-4 text-orange-500" />
                                                <h3 className="font-medium">CTA Button Settings</h3>
                                            </div>
                                            <div className="flex items-center gap-3">
                                                <div className="flex-1">
                                                    <Input
                                                        placeholder="Button Text"
                                                        value={ctaButtonText}
                                                        onChange={(e) => setCtaButtonText(e.target.value)}
                                                        className={`w-full bg-white ${showValidationErrors && !ctaButtonText.trim() ? 'border-red-200' : 'border-gray-200'}`}
                                                    />
                                                    {showValidationErrors && !ctaButtonText.trim() && (
                                                        <p className="text-xs text-red-500 mt-1">Text is required</p>
                                                    )}
                                                </div>
                                                <div className="flex-1">
                                                    <Input
                                                        placeholder="Button URL"
                                                        value={ctaButtonUrl}
                                                        onChange={(e) => {
                                                            setCtaButtonUrl(e.target.value);
                                                            if (showValidationErrors) {
                                                                setCtaButtonUrlError(e.target.value.trim() !== '' && !isValidUrl(e.target.value));
                                                            }
                                                        }}
                                                        className={`w-full bg-white ${showValidationErrors && (ctaButtonUrlError || !ctaButtonUrl.trim()) ? 'border-red-200' : 'border-gray-200'}`}
                                                    />
                                                    {showValidationErrors && ctaButtonUrlError && (
                                                        <p className="text-xs text-red-500 mt-1">Invalid URL format</p>
                                                    )}
                                                    {showValidationErrors && !ctaButtonUrl.trim() && (
                                                        <p className="text-xs text-red-500 mt-1">URL is required</p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Header Links */}
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <MenuSquare className="h-4 w-4 text-orange-500" />
                                                <h3 className="font-medium">Header</h3>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={handleAddHeaderLink}
                                                className="text-orange-500 border-orange-500 hover:bg-orange-50"
                                            >
                                                <Plus className="h-3 w-3 mr-1" /> Add new header link
                                            </Button>
                                        </div>

                                        <div className="space-y-3">
                                            {headerLinks.map((link) => (
                                                <div key={link.id} className="flex items-center gap-3">
                                                    <div className="flex-1">
                                                        <Input
                                                            placeholder="Link text"
                                                            value={link.text}
                                                            onChange={(e) => updateHeaderLink(link.id, 'text', e.target.value)}
                                                            className={`w-full bg-white ${showValidationErrors && !link.text.trim() ? 'border-red-200' : 'border-gray-200'}`}
                                                        />
                                                        {showValidationErrors && !link.text.trim() && (
                                                            <p className="text-xs text-red-500 mt-1">Text is required</p>
                                                        )}
                                                    </div>
                                                    <div className="flex-1">
                                                        <Input
                                                            placeholder="URL"
                                                            value={link.url}
                                                            onChange={(e) => updateHeaderLink(link.id, 'url', e.target.value)}
                                                            className={`w-full bg-white ${showValidationErrors && (linkErrors[link.id]?.url || !link.url.trim()) ? 'border-red-200' : 'border-gray-200'}`}
                                                        />
                                                        {showValidationErrors && linkErrors[link.id]?.url && (
                                                            <p className="text-xs text-red-500 mt-1">Invalid URL format</p>
                                                        )}
                                                        {showValidationErrors && !link.url.trim() && (
                                                            <p className="text-xs text-red-500 mt-1">URL is required</p>
                                                        )}
                                                    </div>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleRemoveHeaderLink(link.id)}
                                                        className="text-gray-400 hover:text-red-500"
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            ))}

                                            {headerLinks.length === 0 && (
                                                <p className="text-sm text-gray-500 italic">No header links added yet</p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Footer Links */}
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <FootprintsIcon className="h-4 w-4 text-orange-500" />
                                                <h3 className="font-medium">Footer</h3>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={handleAddFooterLink}
                                                className="text-orange-500 border-orange-500 hover:bg-orange-50"
                                            >
                                                <Plus className="h-3 w-3 mr-1" /> Add new footer link
                                            </Button>
                                        </div>

                                        <div className="space-y-3">
                                            {footerLinks.map((link) => (
                                                <div key={link.id} className="flex items-center gap-3">
                                                    <div className="flex-1">
                                                        <Input
                                                            placeholder="Link text"
                                                            value={link.text}
                                                            onChange={(e) => updateFooterLink(link.id, 'text', e.target.value)}
                                                            className={`w-full bg-white ${showValidationErrors && !link.text.trim() ? 'border-red-200' : 'border-gray-200'}`}
                                                        />
                                                        {showValidationErrors && !link.text.trim() && (
                                                            <p className="text-xs text-red-500 mt-1">Text is required</p>
                                                        )}
                                                    </div>
                                                    <div className="flex-1">
                                                        <Input
                                                            placeholder="URL"
                                                            value={link.url}
                                                            onChange={(e) => updateFooterLink(link.id, 'url', e.target.value)}
                                                            className={`w-full bg-white ${showValidationErrors && (linkErrors[link.id]?.url || !link.url.trim()) ? 'border-red-200' : 'border-gray-200'}`}
                                                        />
                                                        {showValidationErrors && linkErrors[link.id]?.url && (
                                                            <p className="text-xs text-red-500 mt-1">Invalid URL format</p>
                                                        )}
                                                        {showValidationErrors && !link.url.trim() && (
                                                            <p className="text-xs text-red-500 mt-1">URL is required</p>
                                                        )}
                                                    </div>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleRemoveFooterLink(link.id)}
                                                        className="text-gray-400 hover:text-red-500"
                                                    >
                                                        <X className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            ))}

                                            {footerLinks.length === 0 && (
                                                <p className="text-sm text-gray-500 italic">No footer links added yet</p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Save Navigation Button */}
                                    <div className="flex justify-end">
                                        <Button
                                            onClick={handleSaveNavigation}
                                            className="bg-orange-500 hover:bg-orange-600 text-white"
                                            disabled={isSavingNavigation}
                                        >
                                            {isSavingNavigation ? (
                                                <>
                                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                    Saving...
                                                </>
                                            ) : (
                                                'Save Changes'
                                            )}
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </Card>

            {/* Remove Domain Dialog */}
            <AlertDialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Remove Domain Connection</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to remove the domain connection? This will revert your blog to the default domain.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleRemoveDomain}
                            className="bg-red-500 text-white hover:bg-red-600"
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    Removing...
                                </>
                            ) : (
                                'Remove'
                            )}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
};

export default HostingSettings; 