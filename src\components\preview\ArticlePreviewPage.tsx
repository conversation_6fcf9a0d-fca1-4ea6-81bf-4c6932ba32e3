import { useEffect, useState } from 'react';
import { useParams, Link } from 'react-router-dom';
import { Article } from '@/types/Article';
import { articleService } from '@/services/articleService';
import '@/styles/article.css';
import DailyDelightPreview from './DailyDelightPreview';
import ModernCapsulePreview from './ModernCapsulePreview';
import { useDomain } from '@/contexts/DomainContext';

export const ArticlePreviewPage = () => {
    const { slug } = useParams<{ slug: string }>();
    const [article, setArticle] = useState<Article | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const { currentDomain } = useDomain();

    // Get design settings from domain context
    const brandColor = currentDomain?.designSettings?.colors?.brand || '#3c484c';
    const fontColor = currentDomain?.designSettings?.colors?.accent || '#cec4c0';
    const fontFamily = currentDomain?.designSettings?.font || 'Inter';
    const theme = currentDomain?.designSettings?.articleTheme || 'Daily Delight';

    // Set document title when article is loaded
    useEffect(() => {
        if (article?.title) {
            document.title = `${article.title} | ${currentDomain?.name || 'Blog'}`;
        } else {
            document.title = currentDomain?.name ? `${currentDomain.name} | Article` : 'Article';
        }
    }, [article, currentDomain]);

    useEffect(() => {
        const fetchArticle = async () => {
            if (!slug) {
                setError('No article slug provided');
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError(null);
                const fetchedArticle = await articleService.getArticleBySlug(slug);
                setArticle(fetchedArticle);
            } catch (err) {
                console.error('Error fetching article:', err);
                setError('Article not found or no longer available');
            } finally {
                setLoading(false);
            }
        };

        fetchArticle();
    }, [slug]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mb-4"></div>
                    <p className="text-gray-600">Loading article...</p>
                </div>
            </div>
        );
    }

    if (error || !article) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h2>
                    <p className="text-gray-600 mb-6">{error || 'Article not found or no longer available'}</p>
                    <Link to="/" className="text-orange-500 hover:text-orange-600">
                        Return to Articles
                    </Link>
                </div>
            </div>
        );
    }

    // Render the appropriate preview component based on theme
    return (
        <div className="min-h-screen bg-white">
            {theme === 'Modern Capsule' ? (
                <ModernCapsulePreview
                    article={article}
                    fontFamily={fontFamily}
                    fontColor={fontColor}
                    brandColor={brandColor}
                />
            ) : (
                <DailyDelightPreview
                    article={article}
                    fontFamily={fontFamily}
                    fontColor={fontColor}
                    brandColor={brandColor}
                />
            )}
        </div>
    );
}; 