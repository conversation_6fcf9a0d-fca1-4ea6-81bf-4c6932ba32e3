import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { ArticlePreview } from './ArticlePreview';
import { ArticleList } from './ArticleList';
import { articleApi } from '@/services/api';
import { Article } from '@/types/Article';
import { useDomain } from '@/contexts/DomainContext';
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { ArticleGenerateModal } from './ArticleGenerateModal';

export const ArticleManagement = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id: domainIdFromUrl } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('all');
  const [previewingArticle, setPreviewingArticle] = useState<Article | null>(null);
  const [articles, setArticles] = useState<Article[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { currentDomain, domains, setCurrentDomain, isLoading: domainsLoading } = useDomain();
  const { toast } = useToast();
  const [showGenerateModal, setShowGenerateModal] = useState(false);

  // Set the current domain based on URL parameter if provided
  useEffect(() => {
    if (domainIdFromUrl && domains.length > 0) {
      const domainFromUrl = domains.find(d => d._id === domainIdFromUrl);
      if (domainFromUrl && (!currentDomain || currentDomain._id !== domainIdFromUrl)) {
        setCurrentDomain(domainFromUrl);
      }
    }
  }, [domainIdFromUrl, domains, currentDomain, setCurrentDomain]);

  useEffect(() => {
    document.title = 'Articles | BlogBuster';
  }, []);

  const handleAddDomain = () => {
    navigate('/onboarding');
  };

  // Fetch articles from backend
  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();

    const fetchArticles = async () => {
      try {
        setLoading(true);
        if (!currentDomain) {
          setArticles([]);
          return;
        }
        // Pass domain ID to backend for filtering
        const articlesData = await articleApi.getArticles(currentDomain._id);
        // Only update state if component is still mounted
        if (isMounted) {
          // Transform API response to match required Article type
          const processedArticles: Article[] = articlesData.map(article => ({
            id: article.id,
            _id: article.id, // Add _id field to match the Article interface
            title: article.title,
            content: article.content,
            description: article.description || '',
            author: article.author || '',
            date: article.date || new Date().toISOString(),
            status: article.status || 'draft',
            tag: article.tag || '',
            sourceUrl: article.sourceUrl,
            image: article.image || {
              url: '',
              thumb: '',
            },
            domainId: article.domainId || currentDomain._id,
            metadata: article.metadata,
            video: article.video,
            urlSlug: article.urlSlug,
            metaDescription: article.metaDescription,
            excerpt: article.excerpt,
            keywords: article.keywords || []
          }));
          setArticles(processedArticles);
        }
      } catch (err) {
        // Only update error state if component is still mounted
        if (isMounted) {
          console.error('Error fetching articles:', err);
          setError('Failed to load articles');
        }
      } finally {
        // Only update loading state if component is still mounted
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Fetch articles when we have a domain
    if (currentDomain) {
      fetchArticles();
    } else {
      setArticles([]);
      setLoading(false);
    }

    // Cleanup function to prevent memory leaks and state updates after unmount
    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [currentDomain?._id]); // Only depend on the domain ID, not the entire domain object

  // Calculate tab counts based on filtered articles
  const getTabCounts = () => {
    const filteredArticles = currentDomain
      ? articles.filter(a => a.domainId === currentDomain._id)
      : [];

    const generated = filteredArticles.filter(a => a.status.toLowerCase() === 'generated').length;
    const draft = filteredArticles.filter(a => a.status.toLowerCase() === 'draft').length;
    const scheduled = filteredArticles.filter(a => a.status.toLowerCase() === 'scheduled').length;
    const published = filteredArticles.filter(a => a.status.toLowerCase() === 'published').length;

    return {
      all: filteredArticles.length,
      generated,
      draft,
      scheduled,
      published
    };
  };

  const tabCounts = getTabCounts();
  const tabs = [
    { id: 'all', label: 'All', count: tabCounts.all },
    { id: 'draft', label: 'Draft', count: tabCounts.draft },
    { id: 'scheduled', label: 'Scheduled', count: tabCounts.scheduled },
    { id: 'generated', label: 'Generated', count: tabCounts.generated },
    { id: 'published', label: 'Published', count: tabCounts.published },
  ];

  const handleEdit = (id: string) => {
    navigate(`/dashboard/articles/edit/${id}`);
  };

  const handlePreview = (id: string) => {
    const articleToPreview = articles.find(article => article.id === id);
    if (articleToPreview) {
      setPreviewingArticle(articleToPreview);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await articleApi.deleteArticle(id);
      setArticles(articles.filter(article => article.id !== id));
    } catch (error) {
      console.error('Error deleting article:', error);
      alert('Failed to delete article');
    }
  };

  const handleOpenGenerateModal = () => {
    if (!currentDomain) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a domain first"
      });
      return;
    }
    setShowGenerateModal(true);
  };

  const handleGenerateArticle = async (topic: string, language: string) => {
    if (!currentDomain) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a domain first"
      });
      return;
    }

    if (isGenerating) {
      return; // Prevent multiple clicks while generating
    }

    try {
      setIsGenerating(true);
      // Generate article using GPT with topic and language
      const generatedArticle = await articleApi.generateArticle(currentDomain._id, topic, language);

      // Add the new article to the state
      setArticles(prev => [generatedArticle, ...prev]);

      // Show success message
      toast({
        title: "Success",
        description: "New article generated successfully"
      });

      // Close the modal
      setShowGenerateModal(false);

      // Navigate to the edit page
      navigate(`/dashboard/articles/edit/${generatedArticle.id}`);
    } catch (error) {
      console.error('Error generating article:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || 'Failed to generate article'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const filteredArticles = articles.filter(article => {
    if (activeTab === 'all') return true;
    return article.status.toLowerCase() === activeTab;
  });

  // Sort articles by date in descending order (newest first)
  const sortedArticles = [...filteredArticles].sort((a, b) => {
    const dateA = new Date(a.date || a.createdAt || 0).getTime();
    const dateB = new Date(b.date || b.createdAt || 0).getTime();
    return dateB - dateA; // Descending order
  });

  const getEmptyStateMessage = () => {
    switch (activeTab) {
      case 'draft':
        return 'No draft articles';
      case 'scheduled':
        return 'No scheduled articles';
      case 'generated':
        return 'No generated articles';
      case 'published':
        return 'No published articles';
      default:
        return 'No articles yet';
    }
  };

  if (loading || domainsLoading) {
    return (
      <div className="text-center py-8">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
        <p className="mt-2 text-sm text-gray-600">Loading your data...</p>
      </div>
    );
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="space-y-6">
      {!currentDomain ? (
        <div className="text-center py-8">
          <h3 className="text-lg font-medium text-gray-900">No Domain Selected</h3>
          <p className="mt-1 text-sm text-gray-500">Please select a domain or add a new one</p>
          <Button
            onClick={handleAddDomain}
            className="mt-4 bg-orange-500 hover:bg-orange-600 text-white"
          >
            Add New Domain
          </Button>
        </div>
      ) : (
        <>
          {/* Domain Header */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">🌐</span>
                </div>
                <div>
                  <h2 className="font-semibold text-orange-800">{currentDomain.name}</h2>
                  <p className="text-sm text-orange-600">{currentDomain.url}</p>
                </div>
              </div>
              <button
                onClick={handleOpenGenerateModal}
                disabled={isGenerating}
                className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 disabled:opacity-50 transition-colors flex items-center gap-2 font-medium"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                {isGenerating ? 'Generating...' : 'Generate New Article'}
              </button>
            </div>
          </div>

          <div className="flex border-b border-gray-200">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${activeTab === tab.id
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
              >
                {tab.label}
                <span className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              </button>
            ))}
          </div>

          <ArticleList
            articles={sortedArticles}
            onEdit={handleEdit}
            onPreview={handlePreview}
            onDelete={handleDelete}
            emptyStateMessage={getEmptyStateMessage()}
          />

          {previewingArticle && (
            <ArticlePreview
              article={previewingArticle}
              onClose={() => setPreviewingArticle(null)}
            />
          )}

          <ArticleGenerateModal
            isOpen={showGenerateModal}
            onClose={() => setShowGenerateModal(false)}
            onGenerate={handleGenerateArticle}
            defaultLanguage={currentDomain?.articleSettings?.language || 'English (India)'}
            isGenerating={isGenerating}
          />
        </>
      )}
    </div>
  );
};
