import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDomain } from '@/contexts/DomainContext';
import { domainService } from '@/services/domainService';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

// Comprehensive list of languages
const LANGUAGES = [
    { value: 'English (India)', label: 'English (India)' },
    { value: 'English (US)', label: 'English (US)' },
    { value: 'English (UK)', label: 'English (UK)' },
    { value: 'Hindi', label: 'Hindi' },
    { value: 'Spanish', label: 'Spanish' },
    { value: 'French', label: 'French' },
    { value: 'German', label: 'German' },
    { value: 'Italian', label: 'Italian' },
    { value: 'Portuguese', label: 'Portuguese' },
    { value: 'Dutch', label: 'Dutch' },
    { value: 'Russian', label: 'Russian' },
    { value: 'Japanese', label: 'Japanese' },
    { value: 'Korean', label: 'Korean' },
    { value: 'Chinese (Simplified)', label: 'Chinese (Simplified)' },
    { value: 'Chinese (Traditional)', label: 'Chinese (Traditional)' },
    { value: 'Arabic', label: 'Arabic' },
    { value: 'Turkish', label: 'Turkish' },
    { value: 'Vietnamese', label: 'Vietnamese' },
    { value: 'Thai', label: 'Thai' },
    { value: 'Indonesian', label: 'Indonesian' }
];

interface ArticleSettings {
    useBrandInfo: boolean;
    articleLength: 'short' | 'medium' | 'long';
    language: string;
    specificInstructions?: string;
    exclusions?: string;
}

interface ContentSettingsProps {
    onSave?: (settings: ArticleSettings) => Promise<void>;
}

const ContentSettings: React.FC<ContentSettingsProps> = ({ onSave }) => {
    const { currentDomain, refreshDomains } = useDomain();
    const [isLoading, setIsLoading] = useState(false);
    const [settings, setSettings] = useState<ArticleSettings>({
        useBrandInfo: true,
        articleLength: 'medium',
        language: 'English (India)',
        specificInstructions: '',
        exclusions: ''
    });

    useEffect(() => {
        if (currentDomain?.articleSettings) {
            setSettings(currentDomain.articleSettings);
        }
    }, [currentDomain]);

    const handleSave = async () => {
        if (!currentDomain) {
            toast.error('No domain selected');
            return;
        }

        setIsLoading(true);
        try {
            await domainService.updateDomainArticleSettings(currentDomain._id, settings);
            await refreshDomains();
            toast.success('Article settings saved successfully');
            if (onSave) {
                await onSave(settings);
            }
        } catch (error: any) {
            console.error('Error saving article settings:', error);
            toast.error(error.response?.data?.error || error.message || 'Failed to save article settings');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Card className="max-w-3xl mx-auto p-6 space-y-8">
            <div>
                <h2 className="text-2xl font-semibold mb-2">Article Settings</h2>
                <p className="text-gray-500 text-sm">
                    Choose the parameters for all your generated blog articles
                </p>
            </div>

            <div className="space-y-6">
                {/* Use Brand Info Section */}
                <div className="space-y-4">
                    <Label className="text-base font-medium">Use brand info</Label>
                    <RadioGroup
                        value={settings.useBrandInfo ? 'yes' : 'no'}
                        onValueChange={(value) => setSettings(prev => ({
                            ...prev,
                            useBrandInfo: value === 'yes'
                        }))}
                        className="flex gap-4"
                    >
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="brand-yes" />
                            <Label htmlFor="brand-yes">Using brand info</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="brand-no" />
                            <Label htmlFor="brand-no">Not using brand info</Label>
                        </div>
                    </RadioGroup>
                    <p className="text-sm text-gray-500">
                        When selected, Blogbuster uses both the topic brief and your brand info (description, activity, audience, internal links) to tailor the article. If not, only the brief is used.
                    </p>
                </div>

                {/* Article Length Target */}
                <div className="space-y-4">
                    <Label className="text-base font-medium">Article Length Target</Label>
                    <RadioGroup
                        value={settings.articleLength}
                        onValueChange={(value: 'short' | 'medium' | 'long') => setSettings(prev => ({
                            ...prev,
                            articleLength: value
                        }))}
                        className="flex flex-col gap-3"
                    >
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="short" id="length-short" />
                            <Label htmlFor="length-short">Short (100 words)</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="medium" id="length-medium" />
                            <Label htmlFor="length-medium">Medium (200 words)</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="long" id="length-long" />
                            <Label htmlFor="length-long">Long (300 words)</Label>
                        </div>
                    </RadioGroup>
                </div>

                {/* Language Selection */}
                <div className="space-y-4">
                    <Label className="text-base font-medium">Language by default</Label>
                    <Select
                        value={settings.language}
                        onValueChange={(value) => setSettings(prev => ({
                            ...prev,
                            language: value
                        }))}
                    >
                        <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select a language" />
                        </SelectTrigger>
                        <SelectContent>
                            {LANGUAGES.map((lang) => (
                                <SelectItem key={lang.value} value={lang.value}>
                                    {lang.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                {/* Specific Instructions */}
                <div className="space-y-4">
                    <Label className="text-base font-medium">Specific Instructions</Label>
                    <Textarea
                        value={settings.specificInstructions}
                        onChange={(e) => setSettings(prev => ({
                            ...prev,
                            specificInstructions: e.target.value
                        }))}
                        placeholder="Be super friendly, use casual tone, etc."
                        className="min-h-[100px]"
                    />
                </div>

                {/* Exclusions */}
                <div className="space-y-4">
                    <Label className="text-base font-medium">Exclusions</Label>
                    <Textarea
                        value={settings.exclusions}
                        onChange={(e) => setSettings(prev => ({
                            ...prev,
                            exclusions: e.target.value
                        }))}
                        placeholder="Never mention these topics, words, or phrases"
                        className="min-h-[100px]"
                    />
                </div>

                {/* Save Button */}
                <div className="flex justify-end">
                    <Button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="bg-orange-500 hover:bg-orange-600 text-white"
                    >
                        {isLoading ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Saving...
                            </>
                        ) : (
                            'Save Changes'
                        )}
                    </Button>
                </div>
            </div>
        </Card>
    );
};

export default ContentSettings; 