import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { useDomain } from '@/contexts/DomainContext';
import { useAuth } from '@/contexts/AuthContext';
import { AddDomainModal } from '@/components/domain/AddDomainModal';
import { Globe, Plus, Trash2, MoreVertical, LogOut, FileText, RefreshCcw, Wallet, CreditCard, Settings, User } from 'lucide-react';
import type { LucideIcon } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/use-toast";
import { domainService } from '@/services/domainService';

interface SidebarProps {
  activeView: string;
  onViewChange: (view: string) => void;
  onWrite: () => void;
}

export const Sidebar = ({ activeView, onViewChange, onWrite }: SidebarProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const { domains, currentDomain, setCurrentDomain, deleteDomain, refreshDomains } = useDomain();
  const [showAddDomainModal, setShowAddDomainModal] = useState(false);
  const [deletingDomain, setDeletingDomain] = useState<{ id: string; name: string } | null>(null);

  type MenuItem = { id: string; label: string; Icon: LucideIcon; path: string };

  const menuItems: MenuItem[] = [
    { id: 'articles', label: 'Article', Icon: FileText, path: '/dashboard/articles' },
    { id: 'bulk-generate', label: 'Bulk Generate', Icon: RefreshCcw, path: '/dashboard/bulk-generate' },
    { id: 'plans', label: 'Plans', Icon: Wallet, path: '/dashboard/plans' },
    { id: 'transactions', label: 'Transactions', Icon: CreditCard, path: '/dashboard/transactions' },
    { id: 'settings', label: 'Settings', Icon: Settings, path: '/dashboard/settings' },
    { id: 'account', label: 'Account', Icon: User, path: '/dashboard/account' }
  ];

  const handleDeleteDomain = async () => {
    if (!deletingDomain) return;

    try {
      const domainId = deletingDomain.id;
      setDeletingDomain(null); // Close the dialog immediately

      // Delete domain using the context's deleteDomain function
      await deleteDomain(domainId);

      // No need for additional toast as the context's deleteDomain already shows one
    } catch (error) {
      console.error('Error deleting domain:', error);

      // Show error toast with specific message
      let errorMessage = 'Failed to delete domain';
      if (error instanceof Error) {
        if (error.message === 'Domain not found') {
          // If domain not found, we should update our local state to remove it
          const remainingDomains = domains.filter(d => d._id !== deletingDomain.id);
          if (currentDomain?._id === deletingDomain.id) {
            const newCurrentDomain = remainingDomains.length > 0 ? remainingDomains[0] : null;
            setCurrentDomain(newCurrentDomain);
          }
          errorMessage = 'Domain was already deleted or does not exist';
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleAddDomain = () => {
    navigate('/onboarding');
  };

  return (
    <div className="fixed top-0 left-0 w-64 bg-white border-r border-gray-200 flex flex-col h-screen z-10">
      <div className="p-4 border-b border-gray-200">
        <Link to="/dashboard/articles" className="flex items-center gap-2">
          <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold">📝</span>
          </div>
          <span className="font-semibold text-lg">BLOGBUSTER</span>
        </Link>
      </div>

      <div className="flex-1 overflow-y-auto p-4 flex flex-col">
        <div className="flex-1">
          <Button
            onClick={onWrite}
            className="w-full bg-orange-500 hover:bg-orange-600 text-white mb-6"
          >
            ✍️ Write
          </Button>

          <nav className="mb-8">
            {menuItems.map((item) => (
              <Link
                key={item.id}
                to={item.path}
                className={`flex items-center px-3 py-2 rounded-lg mb-1 ${location.pathname === item.path
                  ? 'bg-orange-50 text-orange-600'
                  : 'text-gray-600 hover:bg-gray-50'
                  }`}
              >
                <item.Icon className="mr-3 w-4 h-4" />
                {item.label}
              </Link>
            ))}
          </nav>
        </div>

        {/* Domains Section - Moved near bottom */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">Domains</h3>
          </div>

          {/* Current Domain */}
          {currentDomain && (
            <div className="mb-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-orange-600" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-orange-800 truncate">
                    {currentDomain.name}
                  </p>
                  <p className="text-xs text-orange-600 truncate">
                    {currentDomain.url}
                  </p>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <DropdownMenuItem onSelect={(e) => {
                          e.preventDefault();
                          setDeletingDomain({ id: currentDomain._id, name: currentDomain.name });
                        }}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Domain
                        </DropdownMenuItem>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will permanently delete the domain "{currentDomain.name}" and <strong>ALL</strong> its articles. This action cannot be undone.
                            <span className="block mt-2 text-red-500">
                              Warning: All articles in this domain will be permanently deleted.
                            </span>
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteDomain()}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            Delete Domain
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          )}

          {/* Other Domains */}
          <div className="space-y-1">
            {domains
              .filter(domain => domain._id !== currentDomain?._id)
              .map((domain) => (
                <div
                  key={domain._id}
                  className="flex items-center justify-between px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <button
                    onClick={() => setCurrentDomain(domain)}
                    className="flex items-center gap-2 text-gray-600 flex-1 min-w-0 text-left"
                  >
                    <Globe className="w-4 h-4 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{domain.name}</p>
                      <p className="text-xs text-gray-500 truncate">{domain.url}</p>
                    </div>
                  </button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem onSelect={(e) => {
                            e.preventDefault();
                            setDeletingDomain({ id: domain._id, name: domain.name });
                          }}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Domain
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will permanently delete the domain "{domain.name}" and <strong>ALL</strong> its articles. This action cannot be undone.
                              <span className="block mt-2 text-red-500">
                                Warning: All articles in this domain will be permanently deleted.
                              </span>
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteDomain()}
                              className="bg-red-500 hover:bg-red-600"
                            >
                              Delete Domain
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
          </div>

          {/* Add New Domain Button */}
          <Button
            onClick={handleAddDomain}
            className="w-full bg-orange-500 hover:bg-orange-600 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add New Domain
          </Button>
        </div>

        {/* Support and Feedback Buttons */}
        <div className="space-y-2 text-sm text-gray-600">
          {/* <button className="flex items-center w-full px-2 py-1 hover:bg-gray-50 rounded">
            <span className="mr-2">💬</span>
            Support
          </button>
          <button className="flex items-center w-full px-2 py-1 hover:bg-gray-50 rounded">
            <span className="mr-2">📝</span>
            Feedback
          </button> */}
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-2 py-1 hover:bg-gray-50 rounded text-red-600"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Logout
          </button>
        </div>
      </div>

      {/* Add Domain Modal */}
      {showAddDomainModal && (
        <AddDomainModal onClose={() => setShowAddDomainModal(false)} />
      )}
    </div>
  );
};
